namespace ServiceLink.Domain.ValueObjects;

/// <summary>
/// Value object représentant une adresse de service
/// </summary>
public class ServiceAddress : IEquatable<ServiceAddress>
{
    /// <summary>
    /// Ligne d'adresse 1
    /// </summary>
    public string AddressLine1 { get; private set; } = string.Empty;

    /// <summary>
    /// Ligne d'adresse 2 (optionnelle)
    /// </summary>
    public string? AddressLine2 { get; private set; }

    /// <summary>
    /// Ville
    /// </summary>
    public string City { get; private set; } = string.Empty;

    /// <summary>
    /// Code postal
    /// </summary>
    public string PostalCode { get; private set; } = string.Empty;

    /// <summary>
    /// Région/État
    /// </summary>
    public string? Region { get; private set; }

    /// <summary>
    /// Pays
    /// </summary>
    public string Country { get; private set; } = string.Empty;

    /// <summary>
    /// Latitude
    /// </summary>
    public double? Latitude { get; private set; }

    /// <summary>
    /// Longitude
    /// </summary>
    public double? Longitude { get; private set; }

    /// <summary>
    /// Instructions d'accès spéciales
    /// </summary>
    public string? AccessInstructions { get; private set; }

    /// <summary>
    /// Étage ou numéro d'appartement
    /// </summary>
    public string? Floor { get; private set; }

    /// <summary>
    /// Code d'accès ou digicode
    /// </summary>
    public string? AccessCode { get; private set; }

    /// <summary>
    /// Constructeur privé pour EF Core
    /// </summary>
    private ServiceAddress() { }

    /// <summary>
    /// Constructeur pour créer une nouvelle adresse
    /// </summary>
    public ServiceAddress(
        string addressLine1,
        string city,
        string postalCode,
        string country,
        string? addressLine2 = null,
        string? region = null,
        double? latitude = null,
        double? longitude = null,
        string? accessInstructions = null,
        string? floor = null,
        string? accessCode = null)
    {
        if (string.IsNullOrWhiteSpace(addressLine1))
            throw new ArgumentException("Address line 1 is required", nameof(addressLine1));

        if (string.IsNullOrWhiteSpace(city))
            throw new ArgumentException("City is required", nameof(city));

        if (string.IsNullOrWhiteSpace(postalCode))
            throw new ArgumentException("Postal code is required", nameof(postalCode));

        if (string.IsNullOrWhiteSpace(country))
            throw new ArgumentException("Country is required", nameof(country));

        AddressLine1 = addressLine1.Trim();
        AddressLine2 = string.IsNullOrWhiteSpace(addressLine2) ? null : addressLine2.Trim();
        City = city.Trim();
        PostalCode = postalCode.Trim();
        Region = string.IsNullOrWhiteSpace(region) ? null : region.Trim();
        Country = country.Trim();
        Latitude = latitude;
        Longitude = longitude;
        AccessInstructions = string.IsNullOrWhiteSpace(accessInstructions) ? null : accessInstructions.Trim();
        Floor = string.IsNullOrWhiteSpace(floor) ? null : floor.Trim();
        AccessCode = string.IsNullOrWhiteSpace(accessCode) ? null : accessCode.Trim();
    }

    /// <summary>
    /// Met à jour les coordonnées GPS
    /// </summary>
    public ServiceAddress WithCoordinates(double latitude, double longitude)
    {
        return new ServiceAddress(
            AddressLine1,
            City,
            PostalCode,
            Country,
            AddressLine2,
            Region,
            latitude,
            longitude,
            AccessInstructions,
            Floor,
            AccessCode);
    }

    /// <summary>
    /// Met à jour les instructions d'accès
    /// </summary>
    public ServiceAddress WithAccessInfo(string? instructions, string? floor, string? accessCode)
    {
        return new ServiceAddress(
            AddressLine1,
            City,
            PostalCode,
            Country,
            AddressLine2,
            Region,
            Latitude,
            Longitude,
            instructions,
            floor,
            accessCode);
    }

    /// <summary>
    /// Obtient l'adresse formatée
    /// </summary>
    public string GetFormattedAddress()
    {
        var parts = new List<string> { AddressLine1 };

        if (!string.IsNullOrWhiteSpace(AddressLine2))
            parts.Add(AddressLine2);

        if (!string.IsNullOrWhiteSpace(Floor))
            parts.Add($"Étage {Floor}");

        parts.Add($"{PostalCode} {City}");

        if (!string.IsNullOrWhiteSpace(Region))
            parts.Add(Region);

        parts.Add(Country);

        return string.Join(", ", parts);
    }

    /// <summary>
    /// Obtient l'adresse courte (sans pays et région)
    /// </summary>
    public string GetShortAddress()
    {
        var parts = new List<string> { AddressLine1 };

        if (!string.IsNullOrWhiteSpace(AddressLine2))
            parts.Add(AddressLine2);

        parts.Add($"{PostalCode} {City}");

        return string.Join(", ", parts);
    }

    /// <summary>
    /// Vérifie si l'adresse a des coordonnées GPS
    /// </summary>
    public bool HasCoordinates => Latitude.HasValue && Longitude.HasValue;

    /// <summary>
    /// Calcule la distance avec une autre adresse (en kilomètres)
    /// </summary>
    public double? CalculateDistanceKm(ServiceAddress other)
    {
        if (!HasCoordinates || !other.HasCoordinates)
            return null;

        return CalculateHaversineDistance(
            Latitude!.Value, Longitude!.Value,
            other.Latitude!.Value, other.Longitude!.Value);
    }

    /// <summary>
    /// Calcule la distance en utilisant la formule de Haversine
    /// </summary>
    private static double CalculateHaversineDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371; // Rayon de la Terre en kilomètres

        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);

        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return R * c;
    }

    /// <summary>
    /// Convertit les degrés en radians
    /// </summary>
    private static double ToRadians(double degrees)
    {
        return degrees * Math.PI / 180;
    }

    /// <summary>
    /// Vérifie si l'adresse est dans un rayon donné d'une autre adresse
    /// </summary>
    public bool IsWithinRadius(ServiceAddress center, double radiusKm)
    {
        var distance = CalculateDistanceKm(center);
        return distance.HasValue && distance.Value <= radiusKm;
    }

    /// <summary>
    /// Implémentation de l'égalité
    /// </summary>
    public bool Equals(ServiceAddress? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;

        return AddressLine1 == other.AddressLine1 &&
               AddressLine2 == other.AddressLine2 &&
               City == other.City &&
               PostalCode == other.PostalCode &&
               Region == other.Region &&
               Country == other.Country &&
               Latitude == other.Latitude &&
               Longitude == other.Longitude;
    }

    public override bool Equals(object? obj)
    {
        return Equals(obj as ServiceAddress);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(
            AddressLine1,
            AddressLine2,
            City,
            PostalCode,
            Region,
            Country,
            Latitude,
            Longitude);
    }

    public static bool operator ==(ServiceAddress? left, ServiceAddress? right)
    {
        return Equals(left, right);
    }

    public static bool operator !=(ServiceAddress? left, ServiceAddress? right)
    {
        return !Equals(left, right);
    }

    public override string ToString()
    {
        return GetFormattedAddress();
    }
}
