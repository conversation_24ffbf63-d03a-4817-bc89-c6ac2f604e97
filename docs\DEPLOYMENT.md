# Guide de Déploiement ServiceLink

Ce guide détaille les différentes méthodes de déploiement de ServiceLink, de l'environnement de développement à la production.

## 📋 Table des Matières

- [Prérequis](#prérequis)
- [Déploiement Local](#déploiement-local)
- [Déploiement avec Docker](#déploiement-avec-docker)
- [Déploiement en Production](#déploiement-en-production)
- [Variables d'Environnement](#variables-denvironnement)
- [Monitoring et Logs](#monitoring-et-logs)
- [Dépannage](#dépannage)

## 🔧 Prérequis

### Développement Local
- .NET 9.0 SDK
- PostgreSQL 16+
- Redis 7+
- Node.js 20+ (pour le frontend)

### Déploiement Docker
- Docker 24.0+
- Docker Compose 2.0+

### Production
- Serveur Linux (Ubuntu 22.04 LTS recommandé)
- Docker et Docker Compose
- Nginx (optionnel, pour reverse proxy)
- Certificats SSL/TLS

## 🏠 Déploiement Local

### 1. Configuration de la Base de Données

```bash
# Installation PostgreSQL (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# Création de la base de données
sudo -u postgres createdb servicelink
sudo -u postgres createuser servicelink
sudo -u postgres psql -c "ALTER USER servicelink PASSWORD 'ServiceLink2024!';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE servicelink TO servicelink;"
```

### 2. Configuration Redis

```bash
# Installation Redis
sudo apt install redis-server

# Configuration (optionnel)
sudo nano /etc/redis/redis.conf
# Décommenter et modifier: requirepass ServiceLink2024!

sudo systemctl restart redis-server
```

### 3. Lancement de l'API

```bash
cd backend/src/ServiceLink.API
dotnet run
```

L'API sera accessible sur `https://localhost:5001` et `http://localhost:5000`.

## 🐳 Déploiement avec Docker

### 1. Configuration Rapide

```bash
# Cloner le repository
git clone https://github.com/wilfried11/ServiceLink.git
cd ServiceLink

# Copier et configurer les variables d'environnement
cp .env.example .env
# Éditer .env selon vos besoins

# Déploiement en développement
.\scripts\deploy.ps1 -Environment development

# Ou avec Docker Compose directement
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

### 2. Services Disponibles

| Service | URL | Description |
|---------|-----|-------------|
| API | http://localhost:8080 | API REST ServiceLink |
| PostgreSQL | localhost:5432 | Base de données |
| Redis | localhost:6379 | Cache et sessions |
| pgAdmin | http://localhost:5050 | Interface DB (dev) |
| Redis Commander | http://localhost:8081 | Interface Redis (dev) |
| MailHog | http://localhost:8025 | Test emails (dev) |

### 3. Commandes Utiles

```bash
# Voir les logs
docker-compose logs -f api

# Exécuter des commandes dans le conteneur API
docker-compose exec api dotnet ef database update

# Redémarrer un service
docker-compose restart api

# Arrêter tous les services
.\scripts\stop.ps1
```

## 🚀 Déploiement en Production

### 1. Préparation du Serveur

```bash
# Mise à jour du système
sudo apt update && sudo apt upgrade -y

# Installation Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Installation Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Configuration SSL/TLS

```bash
# Création du répertoire SSL
sudo mkdir -p /etc/nginx/ssl

# Génération d'un certificat auto-signé (pour test)
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/nginx/ssl/servicelink.key \
  -out /etc/nginx/ssl/servicelink.crt

# Pour un certificat Let's Encrypt (recommandé)
sudo apt install certbot
sudo certbot certonly --standalone -d votre-domaine.com
```

### 3. Configuration Production

```bash
# Créer le fichier d'environnement production
cp .env.example .env.production

# Éditer les variables critiques
nano .env.production
```

Variables importantes à modifier :
- `JWT_SECRET_KEY` : Clé secrète forte (32+ caractères)
- `POSTGRES_PASSWORD` : Mot de passe fort
- `REDIS_PASSWORD` : Mot de passe fort
- `SMTP_*` : Configuration email réelle

### 4. Déploiement

```bash
# Déploiement avec le script
.\scripts\deploy.ps1 -Environment production -Version v1.0.0

# Ou manuellement
docker-compose --profile production up -d
```

### 5. Configuration Nginx (Optionnel)

Si vous utilisez Nginx comme reverse proxy externe :

```nginx
server {
    listen 80;
    server_name votre-domaine.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name votre-domaine.com;

    ssl_certificate /etc/letsencrypt/live/votre-domaine.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/votre-domaine.com/privkey.pem;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔐 Variables d'Environnement

### Variables Critiques

| Variable | Description | Exemple |
|----------|-------------|---------|
| `ASPNETCORE_ENVIRONMENT` | Environnement | `Production` |
| `JWT_SECRET_KEY` | Clé JWT | `votre-cle-secrete-32-caracteres-min` |
| `POSTGRES_PASSWORD` | Mot de passe DB | `MotDePasseSecurise123!` |
| `REDIS_PASSWORD` | Mot de passe Redis | `AutreMotDePasseSecurise123!` |

### Variables Email

| Variable | Description | Exemple |
|----------|-------------|---------|
| `SMTP_HOST` | Serveur SMTP | `smtp.gmail.com` |
| `SMTP_PORT` | Port SMTP | `587` |
| `SMTP_USERNAME` | Utilisateur SMTP | `<EMAIL>` |
| `SMTP_PASSWORD` | Mot de passe SMTP | `votre-app-password` |

### Variables de Sécurité

| Variable | Description | Exemple |
|----------|-------------|---------|
| `CORS_ORIGINS` | Origines autorisées | `https://votre-domaine.com` |
| `ENCRYPTION_KEY` | Clé de chiffrement | `cle-de-chiffrement-32-caracteres` |

## 📊 Monitoring et Logs

### Logs Docker

```bash
# Logs de l'API
docker-compose logs -f api

# Logs de la base de données
docker-compose logs -f postgres

# Logs de tous les services
docker-compose logs -f
```

### Health Checks

```bash
# Vérifier la santé de l'API
curl http://localhost:8080/health

# Vérifier la base de données
docker-compose exec postgres pg_isready -U servicelink -d servicelink
```

### Monitoring avec Prometheus (Optionnel)

Ajoutez ces services à votre `docker-compose.yml` :

```yaml
prometheus:
  image: prom/prometheus
  ports:
    - "9090:9090"
  volumes:
    - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

grafana:
  image: grafana/grafana
  ports:
    - "3001:3000"
  environment:
    - GF_SECURITY_ADMIN_PASSWORD=admin
```

## 🔧 Dépannage

### Problèmes Courants

#### L'API ne démarre pas
```bash
# Vérifier les logs
docker-compose logs api

# Vérifier la connectivité DB
docker-compose exec postgres pg_isready -U servicelink -d servicelink
```

#### Erreurs de connexion à la base de données
```bash
# Vérifier les variables d'environnement
docker-compose exec api env | grep CONNECTION

# Redémarrer la base de données
docker-compose restart postgres
```

#### Problèmes de performance
```bash
# Vérifier l'utilisation des ressources
docker stats

# Vérifier les logs Redis
docker-compose logs redis
```

### Commandes de Diagnostic

```bash
# État des conteneurs
docker-compose ps

# Utilisation des ressources
docker stats

# Espace disque
docker system df

# Nettoyage
docker system prune -f
```

### Support

Pour obtenir de l'aide :
1. Consultez les [Issues GitHub](https://github.com/wilfried11/ServiceLink/issues)
2. Vérifiez la [documentation](../README.md)
3. Créez une nouvelle issue avec les logs d'erreur
