using Microsoft.Extensions.Logging;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Application.Services;

/// <summary>
/// Service utilisateur avec mise en cache Redis
/// Démontre l'utilisation des différentes stratégies de cache
/// </summary>
public class CachedUserService : ICachedUserService
{
    private readonly IUserRepository _userRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<CachedUserService> _logger;

    public CachedUserService(
        IUserRepository userRepository,
        ICacheService cacheService,
        ILogger<CachedUserService> logger)
    {
        _userRepository = userRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <summary>
    /// Récupère un profil utilisateur avec cache (Cache-aside pattern)
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Profil utilisateur ou null si non trouvé</returns>
    public async Task<UserResponse?> GetUserProfileAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var cacheKey = CacheKeys.UserProfile(userId);
        
        _logger.LogDebug("Récupération du profil utilisateur {UserId}", userId);

        return await _cacheService.GetOrSetAsync(
            cacheKey,
            async () =>
            {
                _logger.LogDebug("Cache miss - Récupération depuis la base de données pour {UserId}", userId);
                
                var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
                if (user == null)
                {
                    return null;
                }

                return new UserResponse
                {
                    Id = user.Id,
                    Email = user.Email.Value,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    PhoneNumber = user.PhoneNumber?.Value,
                    Role = user.Role,
                    IsActive = user.IsActive,
                    IsEmailConfirmed = user.IsEmailConfirmed,
                    IsPhoneConfirmed = user.IsPhoneConfirmed,
                    ProfileCompletionPercentage = user.ProfileCompletionPercentage,
                    Language = user.Language,
                    TimeZone = user.TimeZone,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt,
                    LastLoginAt = user.LastLoginAt
                };
            },
            CacheTTL.UserProfiles,
            cancellationToken);
    }

    /// <summary>
    /// Récupère un utilisateur par email avec cache
    /// </summary>
    /// <param name="email">Email de l'utilisateur</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Utilisateur ou null si non trouvé</returns>
    public async Task<User?> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        var cacheKey = CacheKeys.UserByEmail(email);

        _logger.LogDebug("Récupération de l'utilisateur par email {Email}", email);

        return await _cacheService.GetOrSetAsync(
            cacheKey,
            async () =>
            {
                _logger.LogDebug("Cache miss - Récupération depuis la base de données pour email {Email}", email);
                var emailValueObject = Email.Create(email);
                return await _userRepository.GetByEmailAsync(emailValueObject, cancellationToken);
            },
            CacheTTL.UserProfiles,
            cancellationToken);
    }

    /// <summary>
    /// Met à jour un profil utilisateur et invalide le cache (Write-through pattern)
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="updateDto">Données de mise à jour</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Profil utilisateur mis à jour</returns>
    public async Task<UserResponse?> UpdateUserProfileAsync(Guid userId, UpdateUserProfileDto updateDto, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mise à jour du profil utilisateur {UserId}", userId);

        // Récupération de l'utilisateur existant
        var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
        if (user == null)
        {
            _logger.LogWarning("Utilisateur {UserId} non trouvé pour mise à jour", userId);
            return null;
        }

        // Mise à jour des propriétés via les méthodes de l'entité
        if (!string.IsNullOrEmpty(updateDto.FirstName) || !string.IsNullOrEmpty(updateDto.LastName))
        {
            user.UpdateBasicInfo(
                updateDto.FirstName ?? user.FirstName,
                updateDto.LastName ?? user.LastName,
                updateDto.PhoneNumber != null ? PhoneNumber.Create(updateDto.PhoneNumber) : user.PhoneNumber
            );
        }

        // Sauvegarde en base de données
        await _userRepository.UpdateAsync(user, cancellationToken);

        // Création du DTO mis à jour
        var updatedProfile = new UserResponse
        {
            Id = user.Id,
            Email = user.Email.Value,
            FirstName = user.FirstName,
            LastName = user.LastName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            IsActive = user.IsActive,
            IsEmailConfirmed = user.IsEmailConfirmed,
            IsPhoneConfirmed = user.IsPhoneConfirmed,
            ProfileCompletionPercentage = user.ProfileCompletionPercentage,
            Language = user.Language,
            TimeZone = user.TimeZone,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            LastLoginAt = user.LastLoginAt
        };

        // Mise à jour du cache (Write-through)
        var cacheKey = CacheKeys.UserProfile(userId);
        await _cacheService.SetAsync(cacheKey, updatedProfile, CacheTTL.UserProfiles, cancellationToken);

        // Invalidation du cache par email
        var emailCacheKey = CacheKeys.UserByEmail(user.Email.Value);
        await _cacheService.RefreshAsync(
            emailCacheKey,
            async () => await _userRepository.GetByEmailAsync(user.Email, cancellationToken),
            CacheTTL.UserProfiles,
            cancellationToken);

        _logger.LogInformation("Profil utilisateur {UserId} mis à jour avec succès", userId);
        return updatedProfile;
    }

    /// <summary>
    /// Supprime un utilisateur et invalide tous les caches associés
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si supprimé avec succès</returns>
    public async Task<bool> DeleteUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Suppression de l'utilisateur {UserId}", userId);

        // Récupération de l'utilisateur pour obtenir l'email
        var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
        if (user == null)
        {
            _logger.LogWarning("Utilisateur {UserId} non trouvé pour suppression", userId);
            return false;
        }

        // Suppression de la base de données
        await _userRepository.DeleteAsync(user, userId, cancellationToken);

        // Invalidation de tous les caches associés
        var profileCacheKey = CacheKeys.UserProfile(userId);
        var emailCacheKey = CacheKeys.UserByEmail(user.Email.Value);

        await _cacheService.RemoveAsync(profileCacheKey, cancellationToken);
        await _cacheService.RemoveAsync(emailCacheKey, cancellationToken);

        // Invalidation des caches de statistiques qui pourraient inclure cet utilisateur
        await _cacheService.RemoveByPatternAsync($"{CacheKeys.STATS_PREFIX}*", cancellationToken);

        _logger.LogInformation("Utilisateur {UserId} supprimé et caches invalidés", userId);

        return true;
    }

    /// <summary>
    /// Précharge les profils utilisateurs les plus consultés (Cache warming)
    /// </summary>
    /// <param name="userIds">IDs des utilisateurs à précharger</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    public async Task WarmupUserProfilesAsync(IEnumerable<Guid> userIds, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Préchargement de {Count} profils utilisateurs", userIds.Count());

        var tasks = userIds.Select(async userId =>
        {
            try
            {
                var cacheKey = CacheKeys.UserProfile(userId);
                await _cacheService.RefreshAsync(
                    cacheKey,
                    async () =>
                    {
                        var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
                        if (user == null) return null;

                        return new UserResponse
                        {
                            Id = user.Id,
                            Email = user.Email.Value,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            PhoneNumber = user.PhoneNumber?.Value,
                            Role = user.Role,
                            IsActive = user.IsActive,
                            IsEmailConfirmed = user.IsEmailConfirmed,
                            IsPhoneConfirmed = user.IsPhoneConfirmed,
                            ProfileCompletionPercentage = user.ProfileCompletionPercentage,
                            Language = user.Language,
                            TimeZone = user.TimeZone,
                            CreatedAt = user.CreatedAt,
                            UpdatedAt = user.UpdatedAt,
                            LastLoginAt = user.LastLoginAt
                        };
                    },
                    CacheTTL.UserProfiles,
                    cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du préchargement du profil {UserId}", userId);
            }
        });

        await Task.WhenAll(tasks);
        _logger.LogInformation("Préchargement des profils utilisateurs terminé");
    }

    /// <summary>
    /// Obtient les statistiques d'utilisation du cache
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Statistiques du cache</returns>
    public async Task<CacheStatistics> GetCacheStatisticsAsync(CancellationToken cancellationToken = default)
    {
        return await _cacheService.GetStatisticsAsync(cancellationToken);
    }
}

/// <summary>
/// Interface pour le service utilisateur avec cache
/// </summary>
public interface ICachedUserService
{
    Task<UserResponse?> GetUserProfileAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<User?> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<UserResponse?> UpdateUserProfileAsync(Guid userId, UpdateUserProfileDto updateDto, CancellationToken cancellationToken = default);
    Task<bool> DeleteUserAsync(Guid userId, CancellationToken cancellationToken = default);
    Task WarmupUserProfilesAsync(IEnumerable<Guid> userIds, CancellationToken cancellationToken = default);
    Task<CacheStatistics> GetCacheStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// DTO pour la mise à jour du profil utilisateur
/// </summary>
public class UpdateUserProfileDto
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? PhoneNumber { get; set; }
}
