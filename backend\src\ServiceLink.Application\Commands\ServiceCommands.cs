using MediatR;
using ServiceLink.Application.DTOs;

namespace ServiceLink.Application.Services.Commands;

public class CreateServiceCommand : IRequest<ServiceDto>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ShortDescription { get; set; }
    public Guid CategoryId { get; set; }
    public Guid ProviderId { get; set; }
    public decimal BasePrice { get; set; }
    public string PricingUnit { get; set; } = string.Empty;
    public string Currency { get; set; } = "EUR";
    public int? Duration { get; set; }
    public bool IsUrgent { get; set; }
    public List<string> Images { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public List<string>? Requirements { get; set; }
    public List<string>? Included { get; set; }
    public List<string>? Excluded { get; set; }
    public ServiceLocationDto Location { get; set; } = new();
    public ServiceAvailabilityDto Availability { get; set; } = new();
}

public class UpdateServiceCommand : IRequest<ServiceDto?>
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? ShortDescription { get; set; }
    public Guid? CategoryId { get; set; }
    public decimal? BasePrice { get; set; }
    public string? PricingUnit { get; set; }
    public string? Currency { get; set; }
    public int? Duration { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsUrgent { get; set; }
    public bool? IsFeatured { get; set; }
    public List<string>? Images { get; set; }
    public List<string>? Tags { get; set; }
    public List<string>? Requirements { get; set; }
    public List<string>? Included { get; set; }
    public List<string>? Excluded { get; set; }
    public ServiceLocationDto? Location { get; set; }
    public ServiceAvailabilityDto? Availability { get; set; }
}

public class DeleteServiceCommand : IRequest<bool>
{
    public Guid Id { get; set; }
}

public class ToggleFavoriteServiceCommand : IRequest<bool>
{
    public Guid ServiceId { get; set; }
    public Guid UserId { get; set; }
}
