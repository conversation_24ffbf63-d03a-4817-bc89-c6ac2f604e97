import React from 'react';
import { Outlet } from 'react-router-dom';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { Footer } from './Footer';
import { Toaster } from '@/components/ui/toaster';
import { useUIStore } from '@/stores';

export const MainLayout: React.FC = () => {
  const { sidebar, isHeaderVisible, isFooterVisible, isMobile } = useUIStore();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      {isHeaderVisible && <Header />}
      
      <div className="flex">
        {/* Sidebar */}
        {sidebar.isOpen && (
          <>
            {/* Overlay for mobile */}
            {isMobile && sidebar.variant === 'overlay' && (
              <div 
                className="fixed inset-0 bg-black/50 z-40 lg:hidden"
                onClick={() => useUIStore.getState().setSidebarOpen(false)}
              />
            )}
            
            <Sidebar />
          </>
        )}
        
        {/* Main Content */}
        <main 
          className={`
            flex-1 transition-all duration-300 ease-in-out
            ${sidebar.isOpen && !isMobile ? 'ml-64' : ''}
            ${isHeaderVisible ? 'pt-16' : ''}
            ${isFooterVisible ? 'pb-16' : ''}
          `}
        >
          <div className="container mx-auto px-4 py-6">
            <Outlet />
          </div>
        </main>
      </div>
      
      {/* Footer */}
      {isFooterVisible && <Footer />}
      
      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
};
