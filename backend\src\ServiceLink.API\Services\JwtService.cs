using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using ServiceLink.API.Configuration;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace ServiceLink.API.Services;

/// <summary>
/// Service pour la gestion des tokens JWT
/// </summary>
public interface IJwtService
{
    /// <summary>
    /// Génère un token JWT pour un utilisateur
    /// </summary>
    /// <param name="user">Utilisateur</param>
    /// <returns>Token JWT</returns>
    string GenerateToken(User user);

    /// <summary>
    /// Génère un refresh token
    /// </summary>
    /// <returns>Refresh token</returns>
    string GenerateRefreshToken();

    /// <summary>
    /// Valide un token JWT
    /// </summary>
    /// <param name="token">Token à valider</param>
    /// <returns>ClaimsPrincipal si valide, null sinon</returns>
    ClaimsPrincipal? ValidateToken(string token);

    /// <summary>
    /// Extrait l'ID utilisateur d'un token
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>ID utilisateur ou null</returns>
    Guid? GetUserIdFromToken(string token);

    /// <summary>
    /// Vérifie si un token est expiré
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>True si expiré</returns>
    bool IsTokenExpired(string token);
}

/// <summary>
/// Implémentation du service JWT
/// </summary>
public class JwtService : IJwtService
{
    private readonly JwtSettings _jwtSettings;
    private readonly ILogger<JwtService> _logger;
    private readonly TokenValidationParameters _tokenValidationParameters;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="jwtSettings">Configuration JWT</param>
    /// <param name="logger">Logger</param>
    public JwtService(IOptions<JwtSettings> jwtSettings, ILogger<JwtService> logger)
    {
        _jwtSettings = jwtSettings.Value;
        _logger = logger;

        if (!_jwtSettings.IsValid())
        {
            throw new InvalidOperationException("Configuration JWT invalide. Vérifiez la clé secrète et les autres paramètres.");
        }

        _tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey)),
            ValidateIssuer = true,
            ValidIssuer = _jwtSettings.Issuer,
            ValidateAudience = true,
            ValidAudience = _jwtSettings.Audience,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.FromMinutes(_jwtSettings.ClockSkewMinutes),
            RequireExpirationTime = true
        };
    }

    /// <summary>
    /// Génère un token JWT pour un utilisateur
    /// </summary>
    /// <param name="user">Utilisateur</param>
    /// <returns>Token JWT</returns>
    public string GenerateToken(User user)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Email, user.Email.Value),
            new(ClaimTypes.Name, user.FullName),
            new(ClaimTypes.GivenName, user.FirstName),
            new(ClaimTypes.Surname, user.LastName),
            new(ClaimTypes.Role, user.Role.ToString()),
            new("email_confirmed", user.IsEmailConfirmed.ToString().ToLower()),
            new("phone_confirmed", user.IsPhoneConfirmed.ToString().ToLower()),
            new("two_factor_enabled", user.IsTwoFactorEnabled.ToString().ToLower()),
            new("is_active", user.IsActive.ToString().ToLower()),
            new("jti", Guid.NewGuid().ToString()), // JWT ID pour invalidation
            new("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        // Ajouter le numéro de téléphone s'il existe
        if (user.PhoneNumber != null)
        {
            claims.Add(new Claim(ClaimTypes.MobilePhone, user.PhoneNumber.Value));
        }

        // Ajouter des claims spécifiques au rôle
        AddRoleSpecificClaims(claims, user.Role);

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpirationMinutes),
            Issuer = _jwtSettings.Issuer,
            Audience = _jwtSettings.Audience,
            SigningCredentials = credentials
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.CreateToken(tokenDescriptor);

        _logger.LogDebug("Token JWT généré pour l'utilisateur {UserId} ({Email})", user.Id, user.Email.Value);

        return tokenHandler.WriteToken(token);
    }

    /// <summary>
    /// Génère un refresh token
    /// </summary>
    /// <returns>Refresh token</returns>
    public string GenerateRefreshToken()
    {
        var randomBytes = new byte[64];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        
        return Convert.ToBase64String(randomBytes)
            .Replace("+", "-")
            .Replace("/", "_")
            .Replace("=", "");
    }

    /// <summary>
    /// Valide un token JWT
    /// </summary>
    /// <param name="token">Token à valider</param>
    /// <returns>ClaimsPrincipal si valide, null sinon</returns>
    public ClaimsPrincipal? ValidateToken(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return null;

        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var principal = tokenHandler.ValidateToken(token, _tokenValidationParameters, out var validatedToken);

            // Vérifier que c'est bien un JWT avec l'algorithme attendu
            if (validatedToken is not JwtSecurityToken jwtToken ||
                !jwtToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
            {
                _logger.LogWarning("Token JWT avec algorithme invalide");
                return null;
            }

            return principal;
        }
        catch (SecurityTokenExpiredException)
        {
            _logger.LogDebug("Token JWT expiré");
            return null;
        }
        catch (SecurityTokenException ex)
        {
            _logger.LogWarning(ex, "Token JWT invalide");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la validation du token JWT");
            return null;
        }
    }

    /// <summary>
    /// Extrait l'ID utilisateur d'un token
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>ID utilisateur ou null</returns>
    public Guid? GetUserIdFromToken(string token)
    {
        var principal = ValidateToken(token);
        var userIdClaim = principal?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    /// <summary>
    /// Vérifie si un token est expiré
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>True si expiré</returns>
    public bool IsTokenExpired(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return true;

        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jsonToken = tokenHandler.ReadJwtToken(token);
            
            return jsonToken.ValidTo <= DateTime.UtcNow;
        }
        catch
        {
            return true;
        }
    }

    /// <summary>
    /// Ajoute des claims spécifiques au rôle
    /// </summary>
    /// <param name="claims">Liste des claims</param>
    /// <param name="role">Rôle de l'utilisateur</param>
    private static void AddRoleSpecificClaims(List<Claim> claims, UserRole role)
    {
        // Permissions basées sur le rôle
        switch (role)
        {
            case UserRole.Admin:
                claims.Add(new Claim("permission", "users.read"));
                claims.Add(new Claim("permission", "users.write"));
                claims.Add(new Claim("permission", "users.delete"));
                claims.Add(new Claim("permission", "admin.access"));
                claims.Add(new Claim("permission", "statistics.read"));
                break;

            case UserRole.Manager:
                claims.Add(new Claim("permission", "users.read"));
                claims.Add(new Claim("permission", "users.write"));
                claims.Add(new Claim("permission", "statistics.read"));
                break;

            case UserRole.Supervisor:
                claims.Add(new Claim("permission", "users.read"));
                claims.Add(new Claim("permission", "users.write"));
                break;

            case UserRole.Support:
                claims.Add(new Claim("permission", "users.read"));
                claims.Add(new Claim("permission", "statistics.read"));
                break;

            case UserRole.Client:
                claims.Add(new Claim("permission", "profile.read"));
                claims.Add(new Claim("permission", "profile.write"));
                break;

            case UserRole.Provider:
                claims.Add(new Claim("permission", "profile.read"));
                claims.Add(new Claim("permission", "profile.write"));
                claims.Add(new Claim("permission", "services.manage"));
                break;
        }

        // Niveau d'accès
        var accessLevel = role switch
        {
            UserRole.Admin => "full",
            UserRole.Manager => "high",
            UserRole.Supervisor => "medium",
            UserRole.Support => "medium",
            UserRole.Provider => "basic",
            UserRole.Client => "basic",
            _ => "none"
        };

        claims.Add(new Claim("access_level", accessLevel));
    }
}
