import { useCallback, useEffect } from 'react'
import { useMutation, useApi } from './useApi'
import { authService } from '../services/authService'
import { useAuthStore } from '../stores/authStore'
import type { 
  LoginRequest, 
  RegisterRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
  UpdateProfileRequest,
  User
} from '../types/auth'

// Hook principal pour l'authentification
export function useAuth() {
  const {
    user,
    isAuthenticated,
    isLoading,
    setUser,
    setLoading,
    clearAuth,
  } = useAuthStore()

  // Initialiser l'utilisateur depuis le token au démarrage
  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true)
      
      try {
        if (authService.isAuthenticated()) {
          // Essayer de récupérer le profil depuis l'API
          const profile = await authService.getProfile()
          setUser(profile)
        } else {
          // E<PERSON>yer de récupérer depuis le token local
          const userFromToken = authService.getUserFromToken()
          if (userFromToken) {
            setUser(userFromToken)
          }
        }
      } catch (error) {
        // Si l'API échoue, utiliser les données du token
        const userFromToken = authService.getUserFromToken()
        if (userFromToken) {
          setUser(userFromToken)
        } else {
          clearAuth()
        }
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()
  }, [setUser, setLoading, clearAuth])

  // Déconnexion
  const logout = useCallback(async () => {
    try {
      await authService.logout()
    } finally {
      clearAuth()
    }
  }, [clearAuth])

  return {
    user,
    isAuthenticated,
    isLoading,
    logout,
  }
}

// Hook pour la connexion
export function useLogin() {
  const { setUser } = useAuthStore()

  return useMutation<User, LoginRequest>(async (credentials) => {
    const response = await authService.login(credentials)
    setUser(response.user)
    return response.user
  })
}

// Hook pour l'inscription
export function useRegister() {
  return useMutation<void, RegisterRequest>(
    (data) => authService.register(data)
  )
}

// Hook pour mot de passe oublié
export function useForgotPassword() {
  return useMutation<void, ForgotPasswordRequest>(
    (data) => authService.forgotPassword(data)
  )
}

// Hook pour réinitialiser le mot de passe
export function useResetPassword() {
  return useMutation<void, ResetPasswordRequest>(
    (data) => authService.resetPassword(data)
  )
}

// Hook pour changer le mot de passe
export function useChangePassword() {
  return useMutation<void, ChangePasswordRequest>(
    (data) => authService.changePassword(data)
  )
}

// Hook pour confirmer l'email
export function useConfirmEmail() {
  return useMutation<void, string>(
    (token) => authService.confirmEmail(token)
  )
}

// Hook pour renvoyer l'email de confirmation
export function useResendConfirmationEmail() {
  return useMutation<void, void>(
    () => authService.resendConfirmationEmail()
  )
}

// Hook pour obtenir le profil utilisateur
export function useProfile() {
  const { user } = useAuthStore()
  
  return useApi(
    () => authService.getProfile(),
    [],
    !!user // Charger seulement si l'utilisateur est connecté
  )
}

// Hook pour mettre à jour le profil
export function useUpdateProfile() {
  const { setUser } = useAuthStore()

  return useMutation<User, UpdateProfileRequest>(async (data) => {
    const updatedUser = await authService.updateProfile(data)
    setUser(updatedUser)
    return updatedUser
  })
}

// Hook pour vérifier l'état d'authentification
export function useAuthCheck() {
  const { isAuthenticated, user } = useAuthStore()

  const requireAuth = useCallback(() => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }
  }, [isAuthenticated])

  const requireRole = useCallback((role: string) => {
    requireAuth()
    if (user?.role !== role) {
      throw new Error(`Role ${role} required`)
    }
  }, [user?.role, requireAuth])

  const hasRole = useCallback((role: string) => {
    return user?.role === role
  }, [user?.role])

  const hasAnyRole = useCallback((roles: string[]) => {
    return user?.role && roles.includes(user.role)
  }, [user?.role])

  return {
    isAuthenticated,
    user,
    requireAuth,
    requireRole,
    hasRole,
    hasAnyRole,
  }
}

// Hook pour la gestion des tokens
export function useTokens() {
  const getToken = useCallback(() => {
    return authService.getToken()
  }, [])

  const refreshToken = useCallback(async () => {
    try {
      return await authService.refreshToken()
    } catch (error) {
      // Si le refresh échoue, déconnecter l'utilisateur
      const { clearAuth } = useAuthStore.getState()
      clearAuth()
      throw error
    }
  }, [])

  const isTokenValid = useCallback(() => {
    return authService.isAuthenticated()
  }, [])

  return {
    getToken,
    refreshToken,
    isTokenValid,
  }
}

// Hook pour la protection des routes
export function useRouteProtection() {
  const { isAuthenticated, user, isLoading } = useAuth()

  const canAccess = useCallback((requiredRole?: string) => {
    if (isLoading) return null // En cours de chargement
    if (!isAuthenticated) return false
    if (!requiredRole) return true
    return user?.role === requiredRole
  }, [isAuthenticated, user?.role, isLoading])

  const redirectPath = useCallback((requiredRole?: string) => {
    if (!isAuthenticated) return '/login'
    if (requiredRole && user?.role !== requiredRole) return '/unauthorized'
    return null
  }, [isAuthenticated, user?.role])

  return {
    canAccess,
    redirectPath,
    isLoading,
  }
}
