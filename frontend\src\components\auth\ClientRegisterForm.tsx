import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { User, Mail, Lock, Eye, EyeOff, ArrowLeft } from 'lucide-react'
import { Button } from '../ui/button'
import { FormInput } from '../ui/form-input'

import { useToast } from '../../hooks/use-toast'

// Schéma de validation pour l'inscription client
const clientRegisterSchema = z.object({
  firstName: z
    .string()
    .min(1, 'Le prénom est requis')
    .min(2, 'Le prénom doit contenir au moins 2 caractères')
    .max(50, 'Le prénom ne peut pas dépasser 50 caractères'),
  lastName: z
    .string()
    .min(1, 'Le nom est requis')
    .min(2, 'Le nom doit contenir au moins 2 caractères')
    .max(50, 'Le nom ne peut pas dépasser 50 caractères'),
  email: z
    .string()
    .min(1, 'L\'email est requis')
    .email('Format d\'email invalide'),
  password: z
    .string()
    .min(8, 'Le mot de passe doit contenir au moins 8 caractères')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre'
    ),
  confirmPassword: z
    .string()
    .min(1, 'La confirmation du mot de passe est requise'),
  acceptTerms: z
    .boolean()
    .refine((val) => val === true, 'Vous devez accepter les conditions d\'utilisation'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword'],
})

type ClientRegisterFormData = z.infer<typeof clientRegisterSchema>

interface ClientRegisterFormProps {
  onSuccess?: () => void
  onBack?: () => void
}

export const ClientRegisterForm: React.FC<ClientRegisterFormProps> = ({
  onSuccess,
  onBack,
}) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const { toast } = useToast()
  const [showPassword, setShowPassword] = React.useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<ClientRegisterFormData>({
    resolver: zodResolver(clientRegisterSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      acceptTerms: false,
    },
  })

  const onSubmit = async (data: ClientRegisterFormData) => {
    try {
      setIsLoading(true)

      // Appel direct à l'API backend avec les bons champs
      const response = await fetch('http://localhost:5280/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          password: data.password,
          confirmPassword: data.confirmPassword,
          role: 1, // UserRole.Client = 1
          acceptTerms: data.acceptTerms,
          language: 'fr-FR',
          timeZone: 'Europe/Paris',
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Inscription échouée')
      }
      toast({
        title: "Inscription réussie",
        description: "Votre compte a été créé avec succès.",
      })
      onSuccess?.()
    } catch (error: any) {
      toast({
        title: "Erreur d'inscription",
        description: error instanceof Error ? error.message : "Une erreur est survenue",
        variant: "destructive",
      })

      // Gérer les erreurs spécifiques
      if (error.statusCode === 409) {
        setError('email', {
          message: 'Cette adresse email est déjà utilisée',
        })
      } else if (error.errors) {
        // Erreurs de validation du serveur
        Object.entries(error.errors).forEach(([field, messages]) => {
          if (Array.isArray(messages) && messages.length > 0) {
            setError(field as keyof ClientRegisterFormData, {
              message: messages[0],
            })
          }
        })
      } else {
        setError('root', {
          message: error.message || 'Une erreur est survenue lors de l\'inscription',
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="flex items-center mb-6">
          {onBack && (
            <button
              onClick={onBack}
              className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          )}
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Inscription Client</h2>
            <p className="text-gray-600 mt-1">
              Créez votre compte pour réserver des services
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FormInput
              label="Prénom"
              placeholder="Votre prénom"
              leftIcon={<User className="h-4 w-4" />}
              error={errors.firstName?.message}
              {...register('firstName')}
            />

            <FormInput
              label="Nom"
              placeholder="Votre nom"
              leftIcon={<User className="h-4 w-4" />}
              error={errors.lastName?.message}
              {...register('lastName')}
            />
          </div>

          <FormInput
            label="Email"
            type="email"
            placeholder="<EMAIL>"
            leftIcon={<Mail className="h-4 w-4" />}
            error={errors.email?.message}
            {...register('email')}
          />

          <FormInput
            label="Mot de passe"
            type={showPassword ? 'text' : 'password'}
            placeholder="Votre mot de passe"
            leftIcon={<Lock className="h-4 w-4" />}
            rightIcon={
              <div className="cursor-pointer">
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none pointer-events-auto"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
            }
            error={errors.password?.message}
            helperText="Au moins 8 caractères avec une minuscule, une majuscule et un chiffre"
            {...register('password')}
          />

          <FormInput
            label="Confirmer le mot de passe"
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder="Confirmez votre mot de passe"
            leftIcon={<Lock className="h-4 w-4" />}
            rightIcon={
              <div className="cursor-pointer">
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none pointer-events-auto"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
            }
            error={errors.confirmPassword?.message}
            {...register('confirmPassword')}
          />

          <div className="flex items-start">
            <input
              type="checkbox"
              id="acceptTerms"
              className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              {...register('acceptTerms')}
            />
            <label htmlFor="acceptTerms" className="ml-2 text-sm text-gray-600">
              J'accepte les{' '}
              <a href="/terms" className="text-blue-600 hover:text-blue-500">
                conditions d'utilisation
              </a>{' '}
              et la{' '}
              <a href="/privacy" className="text-blue-600 hover:text-blue-500">
                politique de confidentialité
              </a>
            </label>
          </div>
          {errors.acceptTerms && (
            <p className="text-sm text-red-600">{errors.acceptTerms.message}</p>
          )}

          {errors.root && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{errors.root.message}</p>
            </div>
          )}

          <Button
            type="submit"
            className="w-full"
            isLoading={isLoading}
            disabled={isLoading}
          >
            {isLoading ? 'Création du compte...' : 'Créer mon compte'}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Vous avez déjà un compte ?{' '}
            <button
              type="button"
              onClick={() => window.location.href = '/login'}
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Se connecter
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
