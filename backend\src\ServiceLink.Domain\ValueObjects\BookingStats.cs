namespace ServiceLink.Domain.ValueObjects;

/// <summary>
/// Statistiques de réservation pour un utilisateur
/// </summary>
public class BookingStats
{
    /// <summary>
    /// Nombre total de réservations
    /// </summary>
    public int TotalBookings { get; set; }

    /// <summary>
    /// Nombre de réservations terminées
    /// </summary>
    public int CompletedBookings { get; set; }

    /// <summary>
    /// Nombre de réservations annulées
    /// </summary>
    public int CancelledBookings { get; set; }

    /// <summary>
    /// Nombre de réservations en attente
    /// </summary>
    public int PendingBookings { get; set; }

    /// <summary>
    /// Chiffre d'affaires total (en centimes)
    /// </summary>
    public long TotalRevenue { get; set; }

    /// <summary>
    /// Note moyenne
    /// </summary>
    public decimal AverageRating { get; set; }

    /// <summary>
    /// Nombre total d'avis
    /// </summary>
    public int TotalReviews { get; set; }

    /// <summary>
    /// Taux de complétion (%)
    /// </summary>
    public decimal CompletionRate { get; set; }

    /// <summary>
    /// Taux d'annulation (%)
    /// </summary>
    public decimal CancellationRate { get; set; }
}
