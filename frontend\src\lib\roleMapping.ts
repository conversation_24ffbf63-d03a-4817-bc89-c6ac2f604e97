// Mapping des rôles entre backend (numéros) et frontend (chaînes)
// Basé sur l'enum UserRole du backend

export type BackendRole = 1 | 2 | 3 | 4 | 5 | 6;
export type FrontendRole = 'Client' | 'Provider' | 'Admin' | 'Support' | 'Manager' | 'Supervisor';

// Mapping backend -> frontend
const BACKEND_TO_FRONTEND_ROLE_MAP: Record<BackendRole, FrontendRole> = {
  1: 'Client',        // UserRole.Client = 1
  2: 'Provider',      // UserRole.Provider = 2  
  3: 'Admin',        // UserRole.Admin = 3
  4: 'Support',      // UserRole.Support = 4
  5: 'Manager',      // UserRole.Manager = 5
  6: 'Supervisor',   // UserRole.Supervisor = 6
};

// Mapping frontend -> backend
const FRONTEND_TO_BACKEND_ROLE_MAP: Record<FrontendRole, BackendRole> = {
  'Client': 1,
  'Provider': 2,
  'Admin': 3,
  'Support': 4,
  'Manager': 5,
  'Supervisor': 6,
};

/**
 * Convertit un rôle backend (numéro) en rôle frontend (chaîne)
 */
export function mapBackendRoleToFrontend(backendRole: number): FrontendRole {
  const role = BACKEND_TO_FRONTEND_ROLE_MAP[backendRole as BackendRole];
  if (!role) {
    console.warn(`⚠️ Rôle backend non reconnu: ${backendRole}, utilisation de 'Client' par défaut`);
    return 'Client';
  }
  console.log(`🔄 Mapping rôle: ${backendRole} -> ${role}`);
  return role;
}

/**
 * Convertit un rôle frontend (chaîne) en rôle backend (numéro)
 */
export function mapFrontendRoleToBackend(frontendRole: FrontendRole): BackendRole {
  const role = FRONTEND_TO_BACKEND_ROLE_MAP[frontendRole];
  if (!role) {
    console.warn(`⚠️ Rôle frontend non reconnu: ${frontendRole}, utilisation de 1 (Client) par défaut`);
    return 1;
  }
  return role;
}

/**
 * Vérifie si un rôle backend est valide
 */
export function isValidBackendRole(role: number): role is BackendRole {
  return role in BACKEND_TO_FRONTEND_ROLE_MAP;
}

/**
 * Vérifie si un rôle frontend est valide
 */
export function isValidFrontendRole(role: string): role is FrontendRole {
  return role in FRONTEND_TO_BACKEND_ROLE_MAP;
}

/**
 * Obtient la description d'un rôle
 */
export function getRoleDescription(role: FrontendRole): string {
  const descriptions: Record<FrontendRole, string> = {
    'Client': 'Client - Utilisateur final demandeur de services',
    'Provider': 'Prestataire - Fournisseur de services à domicile',
    'Admin': 'Administrateur Global - Accès complet au système',
    'Support': 'Support - Assistance aux utilisateurs',
    'Manager': 'Manager - Gestion des réclamations et litiges',
    'Supervisor': 'Superviseur - Validation des prestataires',
  };
  
  return descriptions[role] || 'Rôle inconnu';
}
