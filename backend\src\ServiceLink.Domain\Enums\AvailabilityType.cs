namespace ServiceLink.Domain.Enums;

/// <summary>
/// Énumération des types de disponibilité
/// </summary>
public enum AvailabilityType
{
    /// <summary>
    /// Disponible pour réservation
    /// </summary>
    Available = 1,

    /// <summary>
    /// Indisponible
    /// </summary>
    Unavailable = 2,

    /// <summary>
    /// Réservé/Occupé
    /// </summary>
    Booked = 3,

    /// <summary>
    /// En pause/Congés
    /// </summary>
    Break = 4,

    /// <summary>
    /// Maintenance
    /// </summary>
    Maintenance = 5,

    /// <summary>
    /// Disponibilité conditionnelle (sur demande)
    /// </summary>
    Conditional = 6
}

/// <summary>
/// Extensions pour AvailabilityType
/// </summary>
public static class AvailabilityTypeExtensions
{
    /// <summary>
    /// Obtient la description du type de disponibilité
    /// </summary>
    public static string GetDescription(this AvailabilityType type)
    {
        return type switch
        {
            AvailabilityType.Available => "Disponible",
            AvailabilityType.Unavailable => "Indisponible",
            AvailabilityType.Booked => "Réservé",
            AvailabilityType.Break => "En pause",
            AvailabilityType.Maintenance => "Maintenance",
            AvailabilityType.Conditional => "Sur demande",
            _ => "Inconnu"
        };
    }

    /// <summary>
    /// Obtient la couleur associée au type
    /// </summary>
    public static string GetColor(this AvailabilityType type)
    {
        return type switch
        {
            AvailabilityType.Available => "#22c55e", // Vert
            AvailabilityType.Unavailable => "#ef4444", // Rouge
            AvailabilityType.Booked => "#f59e0b", // Orange
            AvailabilityType.Break => "#8b5cf6", // Violet
            AvailabilityType.Maintenance => "#6b7280", // Gris
            AvailabilityType.Conditional => "#3b82f6", // Bleu
            _ => "#000000" // Noir par défaut
        };
    }

    /// <summary>
    /// Vérifie si le type permet la réservation
    /// </summary>
    public static bool AllowsBooking(this AvailabilityType type)
    {
        return type == AvailabilityType.Available || type == AvailabilityType.Conditional;
    }
}
