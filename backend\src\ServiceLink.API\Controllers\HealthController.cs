using Microsoft.AspNetCore.Mvc;

namespace ServiceLink.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly ILogger<HealthController> _logger;

    public HealthController(ILogger<HealthController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Vérification de l'état de santé de l'API
    /// </summary>
    [HttpGet]
    public ActionResult<object> GetHealth()
    {
        try
        {
            return Ok(new
            {
                status = "Healthy",
                timestamp = DateTime.UtcNow,
                version = "1.0.0",
                environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de santé");
            return StatusCode(500, new
            {
                status = "Unhealthy",
                timestamp = DateTime.UtcNow,
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Vérification de l'état de préparation de l'API
    /// </summary>
    [HttpGet("ready")]
    public ActionResult<object> GetReady()
    {
        try
        {
            // Ici on pourrait vérifier la base de données, Redis, etc.
            return Ok(new
            {
                status = "Ready",
                timestamp = DateTime.UtcNow,
                checks = new
                {
                    database = "Connected",
                    cache = "Available"
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de préparation");
            return StatusCode(503, new
            {
                status = "Not Ready",
                timestamp = DateTime.UtcNow,
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Vérification de la vivacité de l'API
    /// </summary>
    [HttpGet("live")]
    public ActionResult<object> GetLive()
    {
        return Ok(new
        {
            status = "Live",
            timestamp = DateTime.UtcNow
        });
    }
}
