name: 🔄 CI/CD Pipeline - ServiceLink

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  DOTNET_VERSION: '9.0.x'
  NODE_VERSION: '18.x'
  POSTGRES_DB: servicelink_test
  POSTGRES_USER: servicelink_test_user
  POSTGRES_PASSWORD: servicelink_test_password

jobs:
  # ===== BACKEND TESTS =====
  backend-tests:
    name: 🧪 Backend Tests (.NET 9)
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
          POSTGRES_USER: ${{ env.POSTGRES_USER }}
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4

    - name: 🔧 Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: 📦 Restore Dependencies
      run: dotnet restore backend/ServiceLink.sln

    - name: 🏗️ Build Backend
      run: dotnet build backend/ServiceLink.sln --no-restore --configuration Release

    - name: 🧪 Run Unit Tests
      run: dotnet test backend/ServiceLink.sln --no-build --configuration Release --verbosity normal --collect:"XPlat Code Coverage"

    - name: 📊 Upload Coverage Reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: backend
        name: backend-coverage

    - name: 🔍 Code Quality Analysis
      run: |
        dotnet tool install --global dotnet-sonarscanner || true
        # SonarQube analysis would go here

  # ===== FRONTEND TESTS =====
  frontend-tests:
    name: 🎨 Frontend Tests (React + Vite)
    runs-on: ubuntu-latest

    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4

    - name: 🔧 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: 📦 Install Dependencies
      run: |
        cd frontend
        npm ci

    - name: 🔍 Lint Code
      run: |
        cd frontend
        npm run lint

    - name: 🧪 Run Tests
      run: |
        cd frontend
        npm run test:coverage

    - name: 🏗️ Build Frontend
      run: |
        cd frontend
        npm run build

    - name: 📊 Upload Coverage Reports
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # ===== INTEGRATION TESTS =====
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
          POSTGRES_USER: ${{ env.POSTGRES_USER }}
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4

    - name: 🔧 Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: 🔧 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: 🏗️ Build Backend
      run: |
        cd backend
        dotnet restore
        dotnet build --configuration Release

    - name: 🏗️ Build Frontend
      run: |
        cd frontend
        npm ci
        npm run build

    - name: 🚀 Start Services
      run: |
        cd backend
        dotnet run --project src/ServiceLink.API --configuration Release &
        sleep 30
        cd ../frontend
        npm run preview &
        sleep 10

    - name: 🧪 Run Integration Tests
      run: |
        cd backend
        dotnet test tests/ --filter "Category=Integration" --configuration Release

    - name: 🛑 Stop Services
      run: |
        pkill -f "ServiceLink.API" || true
        pkill -f "vite preview" || true

  # ===== SECURITY SCAN =====
  security-scan:
    name: 🛡️ Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4

    - name: 🔍 Run Trivy Vulnerability Scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: 📊 Upload Trivy Scan Results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: 🔍 .NET Security Audit
      run: |
        cd backend
        dotnet list package --vulnerable --include-transitive

    - name: 🔍 npm Security Audit
      run: |
        cd frontend
        npm audit --audit-level moderate

  # ===== DOCKER BUILD =====
  docker-build:
    name: 🐳 Docker Build
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4

    - name: 🔧 Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 🏗️ Build Backend Docker Image
      run: |
        docker build -t servicelink-api:test -f backend/Dockerfile.dev backend/

    - name: 🏗️ Build Frontend Docker Image
      run: |
        docker build -t servicelink-frontend:test -f frontend/Dockerfile.dev frontend/

    - name: 🧪 Test Docker Images
      run: |
        docker run --rm servicelink-api:test dotnet --version
        docker run --rm servicelink-frontend:test node --version

  # ===== DEPLOYMENT (STAGING) =====
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [integration-tests, security-scan, docker-build]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4

    - name: 🚀 Deploy to Staging
      run: |
        echo "🚀 Deploying to staging environment..."
        # Deployment logic would go here
        echo "✅ Staging deployment completed"

  # ===== DEPLOYMENT (PRODUCTION) =====
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [integration-tests, security-scan, docker-build]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4

    - name: 🌟 Deploy to Production
      run: |
        echo "🌟 Deploying to production environment..."
        # Production deployment logic would go here
        echo "✅ Production deployment completed"

    - name: 📢 Notify Deployment
      run: |
        echo "📢 Notifying team of successful deployment..."
        # Slack/Teams notification would go here

  # ===== CLEANUP =====
  cleanup:
    name: 🧹 Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: 🧹 Clean up Docker Images
      run: |
        docker system prune -f || true
        echo "✅ Cleanup completed"
