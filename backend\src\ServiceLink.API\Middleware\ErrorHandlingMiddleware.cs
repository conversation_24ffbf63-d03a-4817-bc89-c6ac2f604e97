using FluentValidation;
using System.Net;
using System.Text.Json;

namespace ServiceLink.API.Middleware;

/// <summary>
/// Middleware pour la gestion globale des erreurs
/// </summary>
public class ErrorHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ErrorHandlingMiddleware> _logger;
    private readonly IWebHostEnvironment _environment;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="next">Délégué suivant</param>
    /// <param name="logger">Logger</param>
    /// <param name="environment">Environnement</param>
    public ErrorHandlingMiddleware(RequestDelegate next, ILogger<ErrorHandlingMiddleware> logger, IWebHostEnvironment environment)
    {
        _next = next;
        _logger = logger;
        _environment = environment;
    }

    /// <summary>
    /// Traite la requête avec gestion d'erreurs
    /// </summary>
    /// <param name="context">Contexte HTTP</param>
    /// <returns>Task</returns>
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur non gérée lors du traitement de la requête {Method} {Path}",
                context.Request.Method, context.Request.Path);

            await HandleExceptionAsync(context, ex);
        }
    }

    /// <summary>
    /// Gère une exception et génère la réponse appropriée
    /// </summary>
    /// <param name="context">Contexte HTTP</param>
    /// <param name="exception">Exception</param>
    /// <returns>Task</returns>
    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = context.Response;
        response.ContentType = "application/json";

        var errorResponse = new ErrorResponse
        {
            TraceId = context.TraceIdentifier,
            Timestamp = DateTime.UtcNow,
            Path = context.Request.Path,
            Method = context.Request.Method
        };

        switch (exception)
        {
            case ValidationException validationEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse.Title = "Erreur de validation";
                errorResponse.Detail = "Une ou plusieurs erreurs de validation se sont produites.";
                errorResponse.Errors = validationEx.Errors
                    .GroupBy(e => e.PropertyName)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Select(e => e.ErrorMessage).ToArray()
                    );
                break;

            case UnauthorizedAccessException:
                response.StatusCode = (int)HttpStatusCode.Unauthorized;
                errorResponse.Title = "Accès non autorisé";
                errorResponse.Detail = "Vous n'êtes pas autorisé à accéder à cette ressource.";
                break;

            case ArgumentException argEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse.Title = "Argument invalide";
                errorResponse.Detail = argEx.Message;
                break;

            case InvalidOperationException invalidOpEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse.Title = "Opération invalide";
                errorResponse.Detail = invalidOpEx.Message;
                break;

            case KeyNotFoundException:
                response.StatusCode = (int)HttpStatusCode.NotFound;
                errorResponse.Title = "Ressource non trouvée";
                errorResponse.Detail = "La ressource demandée n'a pas été trouvée.";
                break;

            case TimeoutException:
                response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                errorResponse.Title = "Timeout";
                errorResponse.Detail = "La requête a pris trop de temps à traiter.";
                break;

            default:
                response.StatusCode = (int)HttpStatusCode.InternalServerError;
                errorResponse.Title = "Erreur interne du serveur";
                errorResponse.Detail = _environment.IsDevelopment() 
                    ? exception.Message 
                    : "Une erreur inattendue s'est produite.";
                
                if (_environment.IsDevelopment())
                {
                    errorResponse.StackTrace = exception.StackTrace;
                }
                break;
        }

        errorResponse.Status = response.StatusCode;

        var jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = _environment.IsDevelopment()
        };

        var jsonResponse = JsonSerializer.Serialize(errorResponse, jsonOptions);
        await response.WriteAsync(jsonResponse);
    }
}

/// <summary>
/// Réponse d'erreur standardisée
/// </summary>
public class ErrorResponse
{
    /// <summary>
    /// Code de statut HTTP
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// Titre de l'erreur
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Détail de l'erreur
    /// </summary>
    public string Detail { get; set; } = string.Empty;

    /// <summary>
    /// ID de trace pour le debugging
    /// </summary>
    public string TraceId { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp de l'erreur
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Chemin de la requête
    /// </summary>
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// Méthode HTTP
    /// </summary>
    public string Method { get; set; } = string.Empty;

    /// <summary>
    /// Erreurs de validation détaillées
    /// </summary>
    public Dictionary<string, string[]>? Errors { get; set; }

    /// <summary>
    /// Stack trace (développement uniquement)
    /// </summary>
    public string? StackTrace { get; set; }
}

/// <summary>
/// Extensions pour le middleware d'erreurs
/// </summary>
public static class ErrorHandlingMiddlewareExtensions
{
    /// <summary>
    /// Ajoute le middleware de gestion d'erreurs
    /// </summary>
    /// <param name="builder">Application builder</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder UseErrorHandling(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ErrorHandlingMiddleware>();
    }
}

/// <summary>
/// Middleware pour le logging des requêtes
/// </summary>
public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="next">Délégué suivant</param>
    /// <param name="logger">Logger</param>
    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    /// <summary>
    /// Traite la requête avec logging
    /// </summary>
    /// <param name="context">Contexte HTTP</param>
    /// <returns>Task</returns>
    public async Task InvokeAsync(HttpContext context)
    {
        var startTime = DateTime.UtcNow;
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Log de la requête entrante
        _logger.LogInformation("Requête {Method} {Path} démarrée à {StartTime}",
            context.Request.Method,
            context.Request.Path,
            startTime);

        try
        {
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();
            var duration = stopwatch.ElapsedMilliseconds;

            // Log de la réponse
            _logger.LogInformation("Requête {Method} {Path} terminée en {Duration}ms avec le statut {StatusCode}",
                context.Request.Method,
                context.Request.Path,
                duration,
                context.Response.StatusCode);

            // Log des requêtes lentes
            if (duration > 1000)
            {
                _logger.LogWarning("Requête lente détectée: {Method} {Path} a pris {Duration}ms",
                    context.Request.Method,
                    context.Request.Path,
                    duration);
            }
        }
    }
}

/// <summary>
/// Extensions pour le middleware de logging des requêtes
/// </summary>
public static class RequestLoggingMiddlewareExtensions
{
    /// <summary>
    /// Ajoute le middleware de logging des requêtes
    /// </summary>
    /// <param name="builder">Application builder</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder UseRequestLogging(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<RequestLoggingMiddleware>();
    }
}
