using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Queries;
using ServiceLink.Domain.Enums;
using System.Security.Claims;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des réservations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class BookingController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<BookingController> _logger;

    public BookingController(IMediator mediator, ILogger<BookingController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Crée une nouvelle réservation
    /// </summary>
    /// <param name="request">Données de la réservation</param>
    /// <returns>Réservation créée</returns>
    [HttpPost]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<BookingResponse>> CreateBooking([FromBody] CreateBookingRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new CreateBookingCommand(userId, request);
            var result = await _mediator.Send(command);
            
            return CreatedAtAction(nameof(GetBooking), new { id = result.Id }, result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid booking request");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating booking");
            return StatusCode(500, new { message = "An error occurred while creating the booking" });
        }
    }

    /// <summary>
    /// Obtient une réservation par ID
    /// </summary>
    /// <param name="id">ID de la réservation</param>
    /// <returns>Détails de la réservation</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<BookingResponse>> GetBooking(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetBookingByIdQuery(id, userId);
            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound();
                
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving booking {BookingId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the booking" });
        }
    }

    /// <summary>
    /// Obtient les réservations du client connecté
    /// </summary>
    /// <param name="status">Filtrer par statut</param>
    /// <param name="startDate">Date de début</param>
    /// <param name="endDate">Date de fin</param>
    /// <param name="page">Numéro de page</param>
    /// <param name="pageSize">Taille de page</param>
    /// <returns>Liste paginée des réservations</returns>
    [HttpGet("my-bookings")]
    [ProducesResponseType(typeof(PagedResult<BookingResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<PagedResult<BookingResponse>>> GetMyBookings(
        [FromQuery] BookingStatus? status = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetClientBookingsQuery(userId)
            {
                StatusFilter = status.HasValue ? new List<BookingStatus> { status.Value } : null,
                StartDate = startDate,
                EndDate = endDate,
                Page = page,
                PageSize = pageSize
            };
            
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user bookings");
            return StatusCode(500, new { message = "An error occurred while retrieving bookings" });
        }
    }

    /// <summary>
    /// Obtient les réservations du prestataire connecté
    /// </summary>
    /// <param name="status">Filtrer par statut</param>
    /// <param name="startDate">Date de début</param>
    /// <param name="endDate">Date de fin</param>
    /// <param name="page">Numéro de page</param>
    /// <param name="pageSize">Taille de page</param>
    /// <returns>Liste paginée des réservations</returns>
    [HttpGet("provider-bookings")]
    [ProducesResponseType(typeof(PagedResult<BookingResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<PagedResult<BookingResponse>>> GetProviderBookings(
        [FromQuery] BookingStatus? status = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetProviderBookingsQuery(userId)
            {
                StatusFilter = status.HasValue ? new List<BookingStatus> { status.Value } : null,
                StartDate = startDate,
                EndDate = endDate,
                Page = page,
                PageSize = pageSize
            };
            
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving provider bookings");
            return StatusCode(500, new { message = "An error occurred while retrieving bookings" });
        }
    }

    /// <summary>
    /// Confirme une réservation (prestataire)
    /// </summary>
    /// <param name="id">ID de la réservation</param>
    /// <param name="notes">Notes du prestataire</param>
    /// <returns>Réservation mise à jour</returns>
    [HttpPost("{id}/confirm")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<BookingResponse>> ConfirmBooking(Guid id, [FromBody] string? notes = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new ConfirmBookingCommand(id, userId, notes);
            var result = await _mediator.Send(command);
            
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid booking confirmation request");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming booking {BookingId}", id);
            return StatusCode(500, new { message = "An error occurred while confirming the booking" });
        }
    }

    /// <summary>
    /// Rejette une réservation (prestataire)
    /// </summary>
    /// <param name="id">ID de la réservation</param>
    /// <param name="reason">Raison du rejet</param>
    /// <returns>Réservation mise à jour</returns>
    [HttpPost("{id}/reject")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<BookingResponse>> RejectBooking(Guid id, [FromBody] string reason)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new RejectBookingCommand(id, userId, reason);
            var result = await _mediator.Send(command);
            
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid booking rejection request");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting booking {BookingId}", id);
            return StatusCode(500, new { message = "An error occurred while rejecting the booking" });
        }
    }

    /// <summary>
    /// Démarre un service (prestataire)
    /// </summary>
    /// <param name="id">ID de la réservation</param>
    /// <returns>Réservation mise à jour</returns>
    [HttpPost("{id}/start")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<BookingResponse>> StartService(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new StartServiceCommand(id, userId);
            var result = await _mediator.Send(command);
            
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid service start request");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting service for booking {BookingId}", id);
            return StatusCode(500, new { message = "An error occurred while starting the service" });
        }
    }

    /// <summary>
    /// Termine un service (prestataire)
    /// </summary>
    /// <param name="id">ID de la réservation</param>
    /// <param name="actualDurationMinutes">Durée réelle en minutes</param>
    /// <returns>Réservation mise à jour</returns>
    [HttpPost("{id}/complete")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<BookingResponse>> CompleteService(Guid id, [FromBody] int? actualDurationMinutes = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new CompleteServiceCommand(id, userId, actualDurationMinutes);
            var result = await _mediator.Send(command);
            
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid service completion request");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing service for booking {BookingId}", id);
            return StatusCode(500, new { message = "An error occurred while completing the service" });
        }
    }

    /// <summary>
    /// Annule une réservation
    /// </summary>
    /// <param name="id">ID de la réservation</param>
    /// <param name="request">Données d'annulation</param>
    /// <returns>Réservation mise à jour</returns>
    [HttpPost("{id}/cancel")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<BookingResponse>> CancelBooking(Guid id, [FromBody] CancelBookingRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new CancelBookingCommand(id, userId, request);
            var result = await _mediator.Send(command);
            
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid booking cancellation request");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling booking {BookingId}", id);
            return StatusCode(500, new { message = "An error occurred while cancelling the booking" });
        }
    }

    /// <summary>
    /// Calcule le prix d'une réservation
    /// </summary>
    /// <param name="serviceId">ID du service</param>
    /// <param name="durationMinutes">Durée en minutes</param>
    /// <param name="isUrgent">Réservation urgente</param>
    /// <param name="scheduledDate">Date prévue</param>
    /// <returns>Détails du prix</returns>
    [HttpGet("calculate-price")]
    [ProducesResponseType(typeof(BookingPriceResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<BookingPriceResponse>> CalculatePrice(
        [FromQuery] Guid serviceId,
        [FromQuery] int durationMinutes,
        [FromQuery] bool isUrgent = false,
        [FromQuery] DateTime? scheduledDate = null)
    {
        try
        {
            var command = new CalculateBookingPriceCommand(
                serviceId, 
                durationMinutes, 
                isUrgent, 
                scheduledDate ?? DateTime.UtcNow.AddHours(2));
                
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid price calculation request");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating price");
            return StatusCode(500, new { message = "An error occurred while calculating the price" });
        }
    }

    /// <summary>
    /// Vérifie la disponibilité d'un service
    /// </summary>
    /// <param name="serviceId">ID du service</param>
    /// <param name="scheduledDate">Date souhaitée</param>
    /// <param name="durationMinutes">Durée en minutes</param>
    /// <returns>Informations de disponibilité</returns>
    [HttpGet("check-availability")]
    [ProducesResponseType(typeof(AvailabilityResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<AvailabilityResponse>> CheckAvailability(
        [FromQuery] Guid serviceId,
        [FromQuery] DateTime scheduledDate,
        [FromQuery] int durationMinutes)
    {
        try
        {
            var command = new CheckAvailabilityCommand(serviceId, scheduledDate, durationMinutes);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid availability check request");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking availability");
            return StatusCode(500, new { message = "An error occurred while checking availability" });
        }
    }

    /// <summary>
    /// Obtient les statistiques de réservation
    /// </summary>
    /// <param name="startDate">Date de début</param>
    /// <param name="endDate">Date de fin</param>
    /// <returns>Statistiques</returns>
    [HttpGet("stats")]
    [ProducesResponseType(typeof(BookingStatsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<BookingStatsResponse>> GetBookingStats(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetBookingStatsQuery(userId, startDate, endDate);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving booking stats");
            return StatusCode(500, new { message = "An error occurred while retrieving statistics" });
        }
    }

    /// <summary>
    /// Obtient l'ID de l'utilisateur connecté
    /// </summary>
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }
        return userId;
    }
}
