using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Queries;
using ServiceLink.Domain.Enums;
using System.Security.Claims;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des réservations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class BookingController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<BookingController> _logger;

    public BookingController(IMediator mediator, ILogger<BookingController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Crée une nouvelle réservation
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<BookingResponse>> CreateBooking([FromBody] CreateBookingRequest request)
    {
        var userId = GetCurrentUserId();
        
        var command = new CreateBookingCommand
        {
            ClientId = userId,
            ProviderId = request.ProviderId,
            ServiceId = request.ServiceId,
            ScheduledDate = request.ScheduledDate,
            EstimatedDurationMinutes = request.EstimatedDurationMinutes,
            ServiceAddress = request.ServiceAddress.ToServiceAddress(),
            ClientNotes = request.ClientNotes,
            IsUrgent = request.IsUrgent,
            IsRecurring = request.IsRecurring,
            RecurrenceConfig = request.RecurrenceConfig,
            Metadata = request.Metadata
        };

        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetBooking), new { id = result.Id }, result);
    }

    /// <summary>
    /// Obtient une réservation par son ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<BookingResponse>> GetBooking(Guid id)
    {
        var userId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();

        var query = new GetBookingByIdQuery
        {
            BookingId = id,
            UserId = userId,
            UserRole = userRole
        };

        var result = await _mediator.Send(query);
        
        if (result == null)
            return NotFound();

        return Ok(result);
    }

    /// <summary>
    /// Obtient les réservations de l'utilisateur connecté
    /// </summary>
    [HttpGet("my")]
    [ProducesResponseType(typeof(PagedResult<BookingResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PagedResult<BookingResponse>>> GetMyBookings(
        [FromQuery] List<BookingStatus>? statuses = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string sortBy = "ScheduledDate",
        [FromQuery] string sortOrder = "desc")
    {
        var userId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();

        var query = new GetUserBookingsQuery
        {
            UserId = userId,
            UserRole = userRole,
            Statuses = statuses,
            StartDate = startDate,
            EndDate = endDate,
            Page = page,
            PageSize = pageSize,
            SortBy = sortBy,
            SortOrder = sortOrder
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Recherche des réservations (Admin uniquement)
    /// </summary>
    [HttpGet("search")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(PagedResult<BookingResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PagedResult<BookingResponse>>> SearchBookings([FromQuery] BookingSearchRequest request)
    {
        var userId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();

        var query = new SearchBookingsQuery
        {
            ClientId = request.ClientId,
            ProviderId = request.ProviderId,
            ServiceId = request.ServiceId,
            Statuses = request.Statuses,
            StartDate = request.StartDate,
            EndDate = request.EndDate,
            MinAmount = request.MinAmount,
            MaxAmount = request.MaxAmount,
            IsUrgentOnly = request.IsUrgentOnly,
            IsRecurringOnly = request.IsRecurringOnly,
            Page = request.Page,
            PageSize = request.PageSize,
            SortBy = request.SortBy,
            SortOrder = request.SortOrder,
            RequestingUserId = userId,
            RequestingUserRole = userRole
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Confirme une réservation (Prestataire uniquement)
    /// </summary>
    [HttpPost("{id:guid}/confirm")]
    [Authorize(Roles = "Provider")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<BookingResponse>> ConfirmBooking(Guid id, [FromBody] string? providerNotes = null)
    {
        var providerId = GetCurrentUserId();

        var command = new ConfirmBookingCommand
        {
            BookingId = id,
            ProviderId = providerId,
            ProviderNotes = providerNotes
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Rejette une réservation (Prestataire uniquement)
    /// </summary>
    [HttpPost("{id:guid}/reject")]
    [Authorize(Roles = "Provider")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<BookingResponse>> RejectBooking(Guid id, [FromBody] string reason)
    {
        var providerId = GetCurrentUserId();

        var command = new RejectBookingCommand
        {
            BookingId = id,
            ProviderId = providerId,
            Reason = reason
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Démarre un service (Prestataire uniquement)
    /// </summary>
    [HttpPost("{id:guid}/start")]
    [Authorize(Roles = "Provider")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<BookingResponse>> StartService(Guid id)
    {
        var providerId = GetCurrentUserId();

        var command = new StartServiceCommand
        {
            BookingId = id,
            ProviderId = providerId
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Termine un service (Prestataire uniquement)
    /// </summary>
    [HttpPost("{id:guid}/complete")]
    [Authorize(Roles = "Provider")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<BookingResponse>> CompleteService(Guid id, [FromBody] CompleteServiceRequest request)
    {
        var providerId = GetCurrentUserId();

        var command = new CompleteServiceCommand
        {
            BookingId = id,
            ProviderId = providerId,
            ActualDurationMinutes = request.ActualDurationMinutes,
            FinalNotes = request.FinalNotes
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Annule une réservation
    /// </summary>
    [HttpPost("{id:guid}/cancel")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<BookingResponse>> CancelBooking(Guid id, [FromBody] CancelBookingRequest request)
    {
        var userId = GetCurrentUserId();

        var command = new CancelBookingCommand
        {
            BookingId = id,
            CancelledBy = userId,
            Reason = request.Reason
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Modifie une réservation (Client uniquement)
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Client")]
    [ProducesResponseType(typeof(BookingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<BookingResponse>> UpdateBooking(Guid id, [FromBody] UpdateBookingRequest request)
    {
        var clientId = GetCurrentUserId();

        var command = new UpdateBookingCommand
        {
            BookingId = id,
            ClientId = clientId,
            ScheduledDate = request.ScheduledDate,
            EstimatedDurationMinutes = request.EstimatedDurationMinutes,
            ClientNotes = request.ClientNotes,
            ServiceAddress = request.ServiceAddress?.ToServiceAddress(),
            ModificationReason = request.ModificationReason
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Obtient les créneaux disponibles pour un service
    /// </summary>
    [HttpGet("available-slots")]
    [ProducesResponseType(typeof(List<AvailableSlotDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<AvailableSlotDto>>> GetAvailableSlots(
        [FromQuery] Guid providerId,
        [FromQuery] Guid serviceId,
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] int durationMinutes,
        [FromQuery] string timeZone = "UTC")
    {
        var query = new GetAvailableSlotsQuery
        {
            ProviderId = providerId,
            ServiceId = serviceId,
            StartDate = startDate,
            EndDate = endDate,
            DurationMinutes = durationMinutes,
            TimeZone = timeZone
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Valide une réservation avant création
    /// </summary>
    [HttpPost("validate")]
    [ProducesResponseType(typeof(BookingValidationResult), StatusCodes.Status200OK)]
    public async Task<ActionResult<BookingValidationResult>> ValidateBooking([FromBody] CreateBookingRequest request)
    {
        var query = new ValidateBookingQuery
        {
            ProviderId = request.ProviderId,
            ServiceId = request.ServiceId,
            ScheduledDate = request.ScheduledDate,
            DurationMinutes = request.EstimatedDurationMinutes,
            ServiceAddress = request.ServiceAddress
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Obtient les statistiques de réservation
    /// </summary>
    [HttpGet("stats")]
    [ProducesResponseType(typeof(BookingStatsDto), StatusCodes.Status200OK)]
    public async Task<ActionResult<BookingStatsDto>> GetBookingStats(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string groupBy = "month")
    {
        var userId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();

        var query = new GetBookingStatsQuery
        {
            UserId = userRole == UserRole.Admin ? null : userId,
            UserRole = userRole,
            StartDate = startDate,
            EndDate = endDate,
            GroupBy = groupBy
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Obtient les réservations à venir
    /// </summary>
    [HttpGet("upcoming")]
    [ProducesResponseType(typeof(List<BookingResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<BookingResponse>>> GetUpcomingBookings(
        [FromQuery] int hoursAhead = 24,
        [FromQuery] int maxResults = 10)
    {
        var userId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();

        var query = new GetUpcomingBookingsQuery
        {
            UserId = userId,
            UserRole = userRole,
            HoursAhead = hoursAhead,
            MaxResults = maxResults
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Obtient les réservations nécessitant une action
    /// </summary>
    [HttpGet("requiring-action")]
    [ProducesResponseType(typeof(List<BookingResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<BookingResponse>>> GetBookingsRequiringAction(
        [FromQuery] List<string>? actionTypes = null)
    {
        var userId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();

        var query = new GetBookingsRequiringActionQuery
        {
            UserId = userId,
            UserRole = userRole,
            ActionTypes = actionTypes
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Obtient l'historique d'une réservation
    /// </summary>
    [HttpGet("{id:guid}/history")]
    [ProducesResponseType(typeof(List<BookingHistoryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<BookingHistoryDto>>> GetBookingHistory(Guid id)
    {
        var userId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();

        var query = new GetBookingHistoryQuery
        {
            BookingId = id,
            RequestingUserId = userId,
            RequestingUserRole = userRole
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Obtient l'ID de l'utilisateur connecté
    /// </summary>
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("ID utilisateur invalide");
        }
        return userId;
    }

    /// <summary>
    /// Obtient le rôle de l'utilisateur connecté
    /// </summary>
    private UserRole GetCurrentUserRole()
    {
        var roleClaim = User.FindFirst(ClaimTypes.Role)?.Value;
        if (string.IsNullOrEmpty(roleClaim) || !Enum.TryParse<UserRole>(roleClaim, out var role))
        {
            throw new UnauthorizedAccessException("Rôle utilisateur invalide");
        }
        return role;
    }
}

/// <summary>
/// DTO pour terminer un service
/// </summary>
public class CompleteServiceRequest
{
    /// <summary>
    /// Durée réelle du service en minutes
    /// </summary>
    public int? ActualDurationMinutes { get; set; }

    /// <summary>
    /// Notes finales du prestataire
    /// </summary>
    public string? FinalNotes { get; set; }
}
