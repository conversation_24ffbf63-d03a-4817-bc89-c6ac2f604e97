using MediatR;
using ServiceLink.Application.DTOs;

namespace ServiceLink.Application.Commands;

/// <summary>
/// Commande pour créer une nouvelle réservation
/// </summary>
public class CreateBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID du client qui fait la réservation
    /// </summary>
    public Guid ClientId { get; set; }

    /// <summary>
    /// Données de la réservation
    /// </summary>
    public CreateBookingRequest BookingData { get; set; } = new();

    public CreateBookingCommand(Guid clientId, CreateBookingRequest bookingData)
    {
        ClientId = clientId;
        BookingData = bookingData;
    }
}

/// <summary>
/// Commande pour confirmer une réservation (par le prestataire)
/// </summary>
public class ConfirmBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du prestataire qui confirme
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Notes du prestataire
    /// </summary>
    public string? ProviderNotes { get; set; }

    public ConfirmBookingCommand(Guid bookingId, Guid providerId, string? providerNotes = null)
    {
        BookingId = bookingId;
        ProviderId = providerId;
        ProviderNotes = providerNotes;
    }
}

/// <summary>
/// Commande pour rejeter une réservation (par le prestataire)
/// </summary>
public class RejectBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du prestataire qui rejette
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Raison du rejet
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    public RejectBookingCommand(Guid bookingId, Guid providerId, string reason)
    {
        BookingId = bookingId;
        ProviderId = providerId;
        Reason = reason;
    }
}

/// <summary>
/// Commande pour démarrer un service
/// </summary>
public class StartServiceCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du prestataire qui démarre le service
    /// </summary>
    public Guid ProviderId { get; set; }

    public StartServiceCommand(Guid bookingId, Guid providerId)
    {
        BookingId = bookingId;
        ProviderId = providerId;
    }
}

/// <summary>
/// Commande pour terminer un service
/// </summary>
public class CompleteServiceCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du prestataire qui termine le service
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Durée réelle du service en minutes
    /// </summary>
    public int? ActualDurationMinutes { get; set; }

    public CompleteServiceCommand(Guid bookingId, Guid providerId, int? actualDurationMinutes = null)
    {
        BookingId = bookingId;
        ProviderId = providerId;
        ActualDurationMinutes = actualDurationMinutes;
    }
}

/// <summary>
/// Commande pour annuler une réservation
/// </summary>
public class CancelBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID de l'utilisateur qui annule
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Données d'annulation
    /// </summary>
    public CancelBookingRequest CancelData { get; set; } = new();

    public CancelBookingCommand(Guid bookingId, Guid userId, CancelBookingRequest cancelData)
    {
        BookingId = bookingId;
        UserId = userId;
        CancelData = cancelData;
    }
}

/// <summary>
/// Commande pour mettre à jour une réservation
/// </summary>
public class UpdateBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du client qui modifie
    /// </summary>
    public Guid ClientId { get; set; }

    /// <summary>
    /// Données de mise à jour
    /// </summary>
    public UpdateBookingRequest UpdateData { get; set; } = new();

    public UpdateBookingCommand(Guid bookingId, Guid clientId, UpdateBookingRequest updateData)
    {
        BookingId = bookingId;
        ClientId = clientId;
        UpdateData = updateData;
    }
}

/// <summary>
/// Commande pour calculer le prix d'une réservation
/// </summary>
public class CalculateBookingPriceCommand : IRequest<BookingPriceResponse>
{
    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Durée en minutes
    /// </summary>
    public int DurationMinutes { get; set; }

    /// <summary>
    /// Indique si c'est une réservation urgente
    /// </summary>
    public bool IsUrgent { get; set; }

    /// <summary>
    /// Date de la réservation (pour les tarifs variables)
    /// </summary>
    public DateTime ScheduledDate { get; set; }

    public CalculateBookingPriceCommand(Guid serviceId, int durationMinutes, bool isUrgent, DateTime scheduledDate)
    {
        ServiceId = serviceId;
        DurationMinutes = durationMinutes;
        IsUrgent = isUrgent;
        ScheduledDate = scheduledDate;
    }
}

/// <summary>
/// Commande pour vérifier la disponibilité
/// </summary>
public class CheckAvailabilityCommand : IRequest<AvailabilityResponse>
{
    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Date et heure souhaitées
    /// </summary>
    public DateTime ScheduledDate { get; set; }

    /// <summary>
    /// Durée en minutes
    /// </summary>
    public int DurationMinutes { get; set; }

    /// <summary>
    /// Adresse du client (pour vérifier la zone de service)
    /// </summary>
    public ServiceAddressDto? ClientAddress { get; set; }

    public CheckAvailabilityCommand(Guid serviceId, DateTime scheduledDate, int durationMinutes, ServiceAddressDto? clientAddress = null)
    {
        ServiceId = serviceId;
        ScheduledDate = scheduledDate;
        DurationMinutes = durationMinutes;
        ClientAddress = clientAddress;
    }
}

/// <summary>
/// Réponse pour le calcul de prix
/// </summary>
public class BookingPriceResponse
{
    /// <summary>
    /// Prix de base en centimes
    /// </summary>
    public long BasePrice { get; set; }

    /// <summary>
    /// Supplément urgence en centimes
    /// </summary>
    public long UrgentSurcharge { get; set; }

    /// <summary>
    /// Prix total en centimes
    /// </summary>
    public long TotalPrice { get; set; }

    /// <summary>
    /// Commission en centimes
    /// </summary>
    public long CommissionAmount { get; set; }

    /// <summary>
    /// Devise
    /// </summary>
    public string Currency { get; set; } = "EUR";

    /// <summary>
    /// Prix formaté
    /// </summary>
    public string TotalPriceFormatted { get; set; } = string.Empty;

    /// <summary>
    /// Détails du calcul
    /// </summary>
    public Dictionary<string, object> CalculationDetails { get; set; } = new();
}

/// <summary>
/// Réponse pour la vérification de disponibilité
/// </summary>
public class AvailabilityResponse
{
    /// <summary>
    /// Indique si le créneau est disponible
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Raison de l'indisponibilité
    /// </summary>
    public string? UnavailabilityReason { get; set; }

    /// <summary>
    /// Créneaux alternatifs suggérés
    /// </summary>
    public List<AlternativeSlotDto> AlternativeSlots { get; set; } = new();

    /// <summary>
    /// Distance entre le prestataire et le client (en km)
    /// </summary>
    public double? DistanceKm { get; set; }

    /// <summary>
    /// Temps de trajet estimé (en minutes)
    /// </summary>
    public int? TravelTimeMinutes { get; set; }

    /// <summary>
    /// Indique si le service est dans la zone couverte
    /// </summary>
    public bool IsInServiceArea { get; set; }
}

/// <summary>
/// DTO pour un créneau alternatif
/// </summary>
public class AlternativeSlotDto
{
    /// <summary>
    /// Date et heure du créneau
    /// </summary>
    public DateTime DateTime { get; set; }

    /// <summary>
    /// Durée disponible en minutes
    /// </summary>
    public int AvailableDurationMinutes { get; set; }

    /// <summary>
    /// Prix pour ce créneau
    /// </summary>
    public long Price { get; set; }

    /// <summary>
    /// Indique si c'est un créneau urgent
    /// </summary>
    public bool IsUrgent { get; set; }
}
