using MediatR;
using ServiceLink.Application.DTOs;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Application.Commands;

/// <summary>
/// Commande pour créer une nouvelle réservation
/// </summary>
public class CreateBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID du client qui fait la réservation
    /// </summary>
    public Guid ClientId { get; set; }

    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Date et heure prévues pour le service
    /// </summary>
    public DateTime ScheduledDate { get; set; }

    /// <summary>
    /// Durée estimée en minutes
    /// </summary>
    public int EstimatedDurationMinutes { get; set; }

    /// <summary>
    /// Adresse où le service doit être effectué
    /// </summary>
    public ServiceAddress ServiceAddress { get; set; } = new();

    /// <summary>
    /// Notes du client
    /// </summary>
    public string ClientNotes { get; set; } = string.Empty;

    /// <summary>
    /// Indique si c'est une réservation urgente
    /// </summary>
    public bool IsUrgent { get; set; }

    /// <summary>
    /// Indique si c'est une réservation récurrente
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Configuration de récurrence (JSON)
    /// </summary>
    public string? RecurrenceConfig { get; set; }

    /// <summary>
    /// Métadonnées additionnelles
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Commande pour confirmer une réservation
/// </summary>
public class ConfirmBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du prestataire qui confirme
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Notes du prestataire
    /// </summary>
    public string? ProviderNotes { get; set; }
}

/// <summary>
/// Commande pour rejeter une réservation
/// </summary>
public class RejectBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du prestataire qui rejette
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Raison du rejet
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// Commande pour démarrer un service
/// </summary>
public class StartServiceCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du prestataire qui démarre le service
    /// </summary>
    public Guid ProviderId { get; set; }
}

/// <summary>
/// Commande pour terminer un service
/// </summary>
public class CompleteServiceCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du prestataire qui termine le service
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Durée réelle du service en minutes
    /// </summary>
    public int? ActualDurationMinutes { get; set; }

    /// <summary>
    /// Notes finales du prestataire
    /// </summary>
    public string? FinalNotes { get; set; }
}

/// <summary>
/// Commande pour annuler une réservation
/// </summary>
public class CancelBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID de l'utilisateur qui annule
    /// </summary>
    public Guid CancelledBy { get; set; }

    /// <summary>
    /// Raison de l'annulation
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// Commande pour modifier une réservation
/// </summary>
public class UpdateBookingCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du client qui modifie
    /// </summary>
    public Guid ClientId { get; set; }

    /// <summary>
    /// Nouvelle date et heure prévues
    /// </summary>
    public DateTime? ScheduledDate { get; set; }

    /// <summary>
    /// Nouvelle durée estimée en minutes
    /// </summary>
    public int? EstimatedDurationMinutes { get; set; }

    /// <summary>
    /// Nouvelles notes du client
    /// </summary>
    public string? ClientNotes { get; set; }

    /// <summary>
    /// Nouvelle adresse de service
    /// </summary>
    public ServiceAddress? ServiceAddress { get; set; }

    /// <summary>
    /// Raison de la modification
    /// </summary>
    public string ModificationReason { get; set; } = string.Empty;
}

/// <summary>
/// Commande pour marquer les réservations expirées
/// </summary>
public class MarkExpiredBookingsCommand : IRequest<int>
{
    /// <summary>
    /// Date limite pour considérer une réservation comme expirée
    /// </summary>
    public DateTime CutoffDate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Commande pour calculer et mettre à jour les commissions
/// </summary>
public class CalculateBookingCommissionCommand : IRequest<BookingResponse>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// Taux de commission à appliquer
    /// </summary>
    public decimal? CommissionRate { get; set; }
}

/// <summary>
/// Commande pour envoyer des rappels de réservation
/// </summary>
public class SendBookingRemindersCommand : IRequest<int>
{
    /// <summary>
    /// Nombre d'heures avant la réservation pour envoyer le rappel
    /// </summary>
    public int HoursBefore { get; set; } = 24;

    /// <summary>
    /// Type de rappel (email, sms, push)
    /// </summary>
    public string ReminderType { get; set; } = "email";
}

/// <summary>
/// Commande pour créer des réservations récurrentes
/// </summary>
public class CreateRecurringBookingsCommand : IRequest<List<BookingResponse>>
{
    /// <summary>
    /// ID de la réservation parent
    /// </summary>
    public Guid ParentBookingId { get; set; }

    /// <summary>
    /// Nombre d'occurrences à créer
    /// </summary>
    public int NumberOfOccurrences { get; set; }

    /// <summary>
    /// Date de fin pour les récurrences
    /// </summary>
    public DateTime? EndDate { get; set; }
}

/// <summary>
/// Commande pour valider la disponibilité d'une réservation
/// </summary>
public class ValidateBookingAvailabilityCommand : IRequest<bool>
{
    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Date et heure prévues
    /// </summary>
    public DateTime ScheduledDate { get; set; }

    /// <summary>
    /// Durée en minutes
    /// </summary>
    public int DurationMinutes { get; set; }

    /// <summary>
    /// ID de la réservation à exclure (pour les modifications)
    /// </summary>
    public Guid? ExcludeBookingId { get; set; }
}

/// <summary>
/// Commande pour synchroniser les réservations avec les calendriers externes
/// </summary>
public class SyncExternalCalendarCommand : IRequest<int>
{
    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Type de calendrier (google, outlook, etc.)
    /// </summary>
    public string CalendarType { get; set; } = string.Empty;

    /// <summary>
    /// Token d'accès au calendrier
    /// </summary>
    public string AccessToken { get; set; } = string.Empty;
}

/// <summary>
/// Commande pour optimiser les créneaux de réservation
/// </summary>
public class OptimizeBookingSlotsCommand : IRequest<List<DateTime>>
{
    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Date de début de la période
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Date de fin de la période
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Durée souhaitée en minutes
    /// </summary>
    public int DurationMinutes { get; set; }

    /// <summary>
    /// Adresse du client pour optimiser les déplacements
    /// </summary>
    public ServiceAddress? ClientAddress { get; set; }
}
