import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Mail, ArrowLeft, Send } from 'lucide-react'
import { But<PERSON> } from '../ui/Button'
import { Input } from '../ui/Input'
import { authService } from '../../services/authService'
import { toast } from 'sonner'

// Schéma de validation pour mot de passe oublié
const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, 'L\'email est requis')
    .email('Format d\'email invalide'),
})

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>

interface ForgotPasswordFormProps {
  onSuccess?: () => void
  onBack?: () => void
}

export const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  onSuccess,
  onBack,
}) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const [emailSent, setEmailSent] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    getValues,
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  })

  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      setIsLoading(true)
      await authService.forgotPassword(data)
      setEmailSent(true)
      toast.success('Email de réinitialisation envoyé!')
      onSuccess?.()
    } catch (error: any) {
      if (error.statusCode === 404) {
        setError('email', {
          message: 'Aucun compte associé à cette adresse email',
        })
      } else {
        setError('root', {
          message: error.message || 'Une erreur est survenue',
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendEmail = async () => {
    const email = getValues('email')
    if (email) {
      try {
        setIsLoading(true)
        await authService.forgotPassword({ email })
        toast.success('Email renvoyé!')
      } catch (error) {
        toast.error('Erreur lors du renvoi de l\'email')
      } finally {
        setIsLoading(false)
      }
    }
  }

  if (emailSent) {
    return (
      <div className="w-full max-w-md mx-auto">
        <div className="bg-white shadow-lg rounded-lg p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <Send className="h-6 w-6 text-green-600" />
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Email envoyé !
            </h2>
            
            <p className="text-gray-600 mb-6">
              Nous avons envoyé un lien de réinitialisation à{' '}
              <span className="font-medium">{getValues('email')}</span>
            </p>
            
            <div className="space-y-4">
              <Button
                onClick={handleResendEmail}
                variant="outline"
                isLoading={isLoading}
                className="w-full"
              >
                Renvoyer l'email
              </Button>
              
              <Button
                onClick={onBack}
                variant="ghost"
                className="w-full"
                leftIcon={<ArrowLeft className="h-4 w-4" />}
              >
                Retour à la connexion
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="flex items-center mb-6">
          {onBack && (
            <button
              onClick={onBack}
              className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          )}
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Mot de passe oublié
            </h2>
            <p className="text-gray-600 mt-1">
              Entrez votre email pour recevoir un lien de réinitialisation
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <Input
            label="Adresse email"
            type="email"
            placeholder="<EMAIL>"
            leftIcon={<Mail className="h-4 w-4" />}
            error={errors.email?.message}
            helperText="Nous vous enverrons un lien pour réinitialiser votre mot de passe"
            {...register('email')}
          />

          {errors.root && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{errors.root.message}</p>
            </div>
          )}

          <Button
            type="submit"
            className="w-full"
            isLoading={isLoading}
            disabled={isLoading}
            leftIcon={<Send className="h-4 w-4" />}
          >
            {isLoading ? 'Envoi en cours...' : 'Envoyer le lien'}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Vous vous souvenez de votre mot de passe ?{' '}
            <button
              type="button"
              onClick={onBack}
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Se connecter
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
