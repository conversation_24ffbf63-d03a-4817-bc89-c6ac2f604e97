using ServiceLink.Application.DTOs;

namespace ServiceLink.Application.Interfaces;

/// <summary>
/// Interface pour les notifications temps réel des réservations
/// </summary>
public interface IBookingRealtimeService
{
    /// <summary>
    /// Notifie une nouvelle réservation
    /// </summary>
    Task NotifyNewBookingAsync(BookingNotificationDto notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Notifie la confirmation d'une réservation
    /// </summary>
    Task NotifyBookingConfirmedAsync(BookingNotificationDto notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Notifie le rejet d'une réservation
    /// </summary>
    Task NotifyBookingRejectedAsync(BookingNotificationDto notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Notifie le démarrage d'un service
    /// </summary>
    Task NotifyServiceStartedAsync(BookingNotificationDto notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Notifie la fin d'un service
    /// </summary>
    Task NotifyServiceCompletedAsync(BookingNotificationDto notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Notifie l'annulation d'une réservation
    /// </summary>
    Task NotifyBookingCancelledAsync(BookingNotificationDto notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Notifie la modification d'une réservation
    /// </summary>
    Task NotifyBookingModifiedAsync(BookingNotificationDto notification, CancellationToken cancellationToken = default);
}

/// <summary>
/// DTO pour les notifications de réservation
/// </summary>
public class BookingNotificationDto
{
    public Guid BookingId { get; set; }
    public Guid ClientId { get; set; }
    public Guid ProviderId { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public DateTime BookingDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public string? Message { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
