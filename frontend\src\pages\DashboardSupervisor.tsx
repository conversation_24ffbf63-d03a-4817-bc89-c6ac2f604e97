import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, FileCheck, UserX, Alert<PERSON>riangle, CheckCircle, Clock, Users, Eye } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useAuthStore } from '@/stores/authStore';

export const DashboardSupervisor: React.FC = () => {
  const { user } = useAuthStore();

  // Mock supervisor stats (à remplacer par de vraies données)
  const supervisorStats = {
    pendingDocuments: 15,
    verifiedDocuments: 89,
    rejectedDocuments: 6,
    suspendedAccounts: 3,
    flaggedProfiles: 8,
    qualityScore: 94,
    documentsToday: 12,
    averageVerificationTime: 24 // en heures
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-amber-50 to-yellow-50 -mx-6 px-6 py-8 rounded-lg">
        <h1 className="text-3xl font-bold mb-2 text-amber-800">
          Supervision Qualité - {user?.firstName}
        </h1>
        <p className="text-amber-700 mb-6">
          Vérifiez les documents, validez les profils et maintenez la qualité de la plateforme
        </p>
        
        {/* Supervisor Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-800">{supervisorStats.pendingDocuments}</div>
            <p className="text-sm text-orange-600">Documents en attente</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-800">{supervisorStats.verifiedDocuments}</div>
            <p className="text-sm text-green-600">Documents vérifiés</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-red-800">{supervisorStats.flaggedProfiles}</div>
            <p className="text-sm text-red-600">Profils signalés</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Shield className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-800">{supervisorStats.qualityScore}%</div>
            <p className="text-sm text-blue-600">Score qualité</p>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700">Temps de Vérification</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-800">{supervisorStats.averageVerificationTime}h</div>
            <p className="text-xs text-orange-600">
              Objectif: &lt; 48h
            </p>
          </CardContent>
        </Card>

        <Card className="border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Taux d'Approbation</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800">
              {((supervisorStats.verifiedDocuments / (supervisorStats.verifiedDocuments + supervisorStats.rejectedDocuments)) * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-green-600">
              Documents approuvés
            </p>
          </CardContent>
        </Card>

        <Card className="border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-700">Comptes Suspendus</CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-800">{supervisorStats.suspendedAccounts}</div>
            <p className="text-xs text-red-600">
              Ce mois-ci
            </p>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Vérifications Aujourd'hui</CardTitle>
            <FileCheck className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">{supervisorStats.documentsToday}</div>
            <p className="text-xs text-blue-600">
              Documents traités
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Actions de supervision
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/supervisor/documents" className="block">
              <Button className="h-auto p-6 flex-col space-y-3 w-full bg-orange-600 hover:bg-orange-700">
                <FileCheck className="h-8 w-8" />
                <span className="text-lg">Vérification Documents</span>
                <span className="text-sm opacity-90">
                  {supervisorStats.pendingDocuments} en attente
                </span>
              </Button>
            </Link>
            
            <Link to="/supervisor/profiles" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-red-200 hover:bg-red-50">
                <Eye className="h-8 w-8 text-red-600" />
                <span className="text-lg">Profils Signalés</span>
                <span className="text-sm text-muted-foreground">
                  {supervisorStats.flaggedProfiles} à examiner
                </span>
              </Button>
            </Link>
            
            <Link to="/supervisor/quality" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-blue-200 hover:bg-blue-50">
                <Shield className="h-8 w-8 text-blue-600" />
                <span className="text-lg">Contrôle Qualité</span>
                <span className="text-sm text-muted-foreground">Audits et rapports</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Document Verification Queue */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileCheck className="h-5 w-5 text-orange-600" />
              File de Vérification
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 text-orange-600" />
                  <span className="text-sm">SIRET - Pierre Martin</span>
                </div>
                <Badge variant="secondary">2h</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 text-orange-600" />
                  <span className="text-sm">Assurance - Marie Dubois</span>
                </div>
                <Badge variant="secondary">5h</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Diplôme - Jean Dupont</span>
                </div>
                <Badge className="bg-green-100 text-green-800">Approuvé</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-600" />
              Performance Supervision
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Temps de vérification</span>
                <span>{supervisorStats.averageVerificationTime}h / 48h</span>
              </div>
              <Progress value={(48 - supervisorStats.averageVerificationTime) / 48 * 100} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Score qualité</span>
                <span>{supervisorStats.qualityScore}%</span>
              </div>
              <Progress value={supervisorStats.qualityScore} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Taux d'approbation</span>
                <span>{((supervisorStats.verifiedDocuments / (supervisorStats.verifiedDocuments + supervisorStats.rejectedDocuments)) * 100).toFixed(1)}%</span>
              </div>
              <Progress value={(supervisorStats.verifiedDocuments / (supervisorStats.verifiedDocuments + supervisorStats.rejectedDocuments)) * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quality Control Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-600" />
            Alertes Qualité
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <span className="text-sm">{supervisorStats.flaggedProfiles} profils signalés</span>
              </div>
              <Badge variant="destructive">Urgent</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Clock className="h-4 w-4 text-orange-600" />
                <span className="text-sm">{supervisorStats.pendingDocuments} documents en attente</span>
              </div>
              <Badge variant="secondary">À traiter</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">Qualité excellente</span>
              </div>
              <Badge className="bg-green-100 text-green-800">OK</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Supervision Tips */}
      <Card className="bg-gradient-to-r from-amber-50 to-yellow-50">
        <CardHeader>
          <CardTitle className="text-amber-800">🔍 Conseils de supervision</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <FileCheck className="h-5 w-5 text-orange-600 mt-1" />
              <div>
                <p className="font-medium text-orange-800">Vérifiez minutieusement</p>
                <p className="text-sm text-orange-600">Chaque document doit être authentique</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Shield className="h-5 w-5 text-blue-600 mt-1" />
              <div>
                <p className="font-medium text-blue-800">Maintenez la qualité</p>
                <p className="text-sm text-blue-600">La réputation de la plateforme en dépend</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
