using MediatR;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Queries;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.Entities;

namespace ServiceLink.Application.Handlers;

/// <summary>
/// Handler pour la requête de récupération de la liste des utilisateurs
/// </summary>
public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, UserListResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<GetUsersQueryHandler> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="unitOfWork">Unit of Work</param>
    /// <param name="logger">Logger</param>
    public GetUsersQueryHandler(IUnitOfWork unitOfWork, ILogger<GetUsersQueryHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    /// <summary>
    /// Traite la requête de récupération de la liste des utilisateurs
    /// </summary>
    /// <param name="request">Requête</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste paginée des utilisateurs</returns>
    public async Task<UserListResponse> Handle(GetUsersQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Récupération de la liste des utilisateurs - Page {PageNumber}, Taille {PageSize}", 
            request.PageNumber, request.PageSize);

        try
        {
            // Récupérer les utilisateurs avec pagination et filtres
            var pagedResult = await _unitOfWork.Users.GetUsersPagedAsync(
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                role: request.Role,
                isActive: request.IsActive,
                isEmailConfirmed: request.IsEmailConfirmed,
                searchTerm: request.SearchTerm,
                cancellationToken: cancellationToken);

            // Mapper les utilisateurs vers les DTOs
            var userResponses = pagedResult.Items.Select(MapToUserResponse).ToList();

            _logger.LogDebug("Récupération réussie de {Count} utilisateurs sur {Total}",
                userResponses.Count, pagedResult.TotalCount);

            return new UserListResponse
            {
                Users = userResponses,
                TotalCount = pagedResult.TotalCount,
                PageNumber = pagedResult.PageNumber,
                PageSize = pagedResult.PageSize,
                TotalPages = pagedResult.TotalPages
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la liste des utilisateurs");
            throw;
        }
    }

    /// <summary>
    /// Convertit une entité User en UserResponse
    /// </summary>
    /// <param name="user">Entité utilisateur</param>
    /// <returns>DTO de réponse</returns>
    private static UserResponse MapToUserResponse(User user)
    {
        return new UserResponse
        {
            Id = user.Id,
            Email = user.Email.Value,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            RoleDescription = user.Role.ToString(),
            IsEmailConfirmed = user.IsEmailConfirmed,
            IsPhoneConfirmed = user.IsPhoneConfirmed,
            IsActive = user.IsActive,
            IsTwoFactorEnabled = user.IsTwoFactorEnabled,
            AvatarUrl = user.AvatarUrl,
            ProfileCompletionPercentage = user.ProfileCompletionPercentage,
            Language = user.Language,
            TimeZone = user.TimeZone,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            LastLoginAt = user.LastLoginAt
        };
    }
}
