import type { 
  Service, 
  ServiceCategory, 
  ServiceFilters,
  CreateServiceRequest,
  UpdateServiceRequest 
} from '../types/service'
import type { PaginatedResponse, PaginationParams } from '../types/api'
import { get, post, put, del } from '../lib/api'

export const serviceService = {
  // Obtenir toutes les catégories
  async getCategories(): Promise<ServiceCategory[]> {
    return await get<ServiceCategory[]>('/services/categories')
  },

  // Obtenir une catégorie par ID
  async getCategoryById(id: string): Promise<ServiceCategory> {
    return await get<ServiceCategory>(`/services/categories/${id}`)
  },

  // Rechercher des services avec filtres et pagination
  async searchServices(
    filters: ServiceFilters = {},
    pagination: PaginationParams = {}
  ): Promise<PaginatedResponse<Service>> {
    const params = new URLSearchParams()
    
    // Ajouter les filtres
    if (filters.categoryId) params.append('categoryId', filters.categoryId)
    if (filters.location) params.append('location', filters.location)
    if (filters.minPrice) params.append('minPrice', filters.minPrice.toString())
    if (filters.maxPrice) params.append('maxPrice', filters.maxPrice.toString())
    if (filters.rating) params.append('rating', filters.rating.toString())
    if (filters.availability) params.append('availability', filters.availability)
    if (filters.urgent) params.append('urgent', filters.urgent.toString())
    if (filters.radius) params.append('radius', filters.radius.toString())
    if (filters.latitude) params.append('latitude', filters.latitude.toString())
    if (filters.longitude) params.append('longitude', filters.longitude.toString())
    
    // Ajouter la pagination
    if (pagination.pageNumber) params.append('pageNumber', pagination.pageNumber.toString())
    if (pagination.pageSize) params.append('pageSize', pagination.pageSize.toString())
    if (pagination.sortBy) params.append('sortBy', pagination.sortBy)
    if (pagination.sortDirection) params.append('sortDirection', pagination.sortDirection)
    if (pagination.search) params.append('search', pagination.search)

    return await get<PaginatedResponse<Service>>(`/services?${params.toString()}`)
  },

  // Obtenir un service par ID
  async getServiceById(id: string): Promise<Service> {
    return await get<Service>(`/services/${id}`)
  },

  // Obtenir les services populaires
  async getPopularServices(limit: number = 6): Promise<Service[]> {
    return await get<Service[]>(`/services/popular?limit=${limit}`)
  },

  // Obtenir les services en vedette
  async getFeaturedServices(limit: number = 8): Promise<Service[]> {
    return await get<Service[]>(`/services/featured?limit=${limit}`)
  },

  // Obtenir les services d'un prestataire
  async getServicesByProvider(providerId: string): Promise<Service[]> {
    return await get<Service[]>(`/services/provider/${providerId}`)
  },

  // Créer un nouveau service (pour les prestataires)
  async createService(data: CreateServiceRequest): Promise<Service> {
    return await post<Service>('/services', data)
  },

  // Mettre à jour un service
  async updateService(id: string, data: UpdateServiceRequest): Promise<Service> {
    return await put<Service>(`/services/${id}`, data)
  },

  // Supprimer un service
  async deleteService(id: string): Promise<void> {
    await del(`/services/${id}`)
  },

  // Obtenir les statistiques des services
  async getServiceStats(): Promise<{
    totalServices: number
    totalProviders: number
    totalBookings: number
    averageRating: number
    topCategories: { name: string; count: number }[]
  }> {
    return await get('/services/stats')
  },

  // Recherche de suggestions (autocomplete)
  async getSearchSuggestions(query: string): Promise<string[]> {
    return await get<string[]>(`/services/suggestions?q=${encodeURIComponent(query)}`)
  },

  // Obtenir les services à proximité
  async getNearbyServices(
    latitude: number,
    longitude: number,
    radius: number = 10,
    limit: number = 10
  ): Promise<Service[]> {
    return await get<Service[]>(
      `/services/nearby?lat=${latitude}&lng=${longitude}&radius=${radius}&limit=${limit}`
    )
  },

  // Marquer un service comme favori
  async toggleFavorite(serviceId: string): Promise<{ isFavorite: boolean }> {
    return await post<{ isFavorite: boolean }>(`/services/${serviceId}/favorite`)
  },

  // Obtenir les services favoris de l'utilisateur
  async getFavoriteServices(): Promise<Service[]> {
    return await get<Service[]>('/services/favorites')
  },

  // Signaler un service
  async reportService(serviceId: string, reason: string, description?: string): Promise<void> {
    await post(`/services/${serviceId}/report`, { reason, description })
  }
}
