import React from 'react';
import { Star, Download, Smartphone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export const TrustSection: React.FC = () => {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          
          {/* Trustpilot Section */}
          <div className="text-center lg:text-left">
            <div className="mb-8">
              <div className="text-6xl font-bold text-gray-900 mb-2">1 255 000</div>
              <p className="text-xl text-muted-foreground">services réalisés</p>
            </div>

            <div className="mb-8">
              <div className="text-6xl font-bold text-gray-900 mb-2">3 760 000</div>
              <p className="text-xl text-muted-foreground">clients satisfaits</p>
            </div>

            {/* Trustpilot Rating */}
            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-center lg:justify-start space-x-2 mb-3">
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star key={star} className="w-6 h-6 fill-green-500 text-green-500" />
                    ))}
                  </div>
                  <span className="font-bold text-lg">4.9/5</span>
                </div>
                
                <div className="text-center lg:text-left">
                  <div className="font-semibold text-green-800 mb-1">Trustpilot</div>
                  <div className="text-sm text-green-700">
                    Basé sur 45 234 avis
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Yoojo Cover */}
            <div className="mt-8">
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-blue-600 mb-2">ServiceLink Cover</div>
                <p className="text-muted-foreground">
                  La protection intégrale pour tous vos services
                </p>
              </div>
            </div>
          </div>

          {/* App Download Section */}
          <div className="bg-blue-600 rounded-2xl p-8 text-white text-center lg:text-left">
            <div className="mb-6">
              <h3 className="text-2xl font-bold mb-3">
                Plus simple avec l'application
              </h3>
              <p className="text-blue-100">
                Réservez, suivez et payez vos services depuis votre smartphone
              </p>
            </div>

            {/* App Store Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start mb-6">
              <Button variant="secondary" className="flex items-center space-x-2">
                <Download className="w-5 h-5" />
                <div className="text-left">
                  <div className="text-xs">Télécharger sur</div>
                  <div className="font-semibold">App Store</div>
                </div>
              </Button>
              
              <Button variant="secondary" className="flex items-center space-x-2">
                <Download className="w-5 h-5" />
                <div className="text-left">
                  <div className="text-xs">Disponible sur</div>
                  <div className="font-semibold">Google Play</div>
                </div>
              </Button>
            </div>

            {/* ServiceLink Logo */}
            <div className="flex items-center justify-center lg:justify-start space-x-3">
              <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center">
                <span className="text-blue-600 font-bold text-xl">S</span>
              </div>
              <span className="text-2xl font-bold">ServiceLink</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
