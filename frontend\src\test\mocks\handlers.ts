import { http, HttpResponse } from 'msw'

const API_BASE_URL = 'http://localhost:8080/api'

export const handlers = [
  // Auth endpoints
  http.post(`${API_BASE_URL}/auth/login`, () => {
    return HttpResponse.json({
      token: 'mock-jwt-token',
      refreshToken: 'mock-refresh-token',
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: '<PERSON>',
        lastName: 'Do<PERSON>',
        role: 'User',
        isEmailConfirmed: true,
        createdAt: '2024-01-01T00:00:00Z',
      },
    })
  }),

  http.post(`${API_BASE_URL}/auth/register`, () => {
    return HttpResponse.json({
      message: 'User registered successfully',
      user: {
        id: '2',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'User',
        isEmailConfirmed: false,
        createdAt: '2024-01-01T00:00:00Z',
      },
    })
  }),

  http.post(`${API_BASE_URL}/auth/refresh`, () => {
    return HttpResponse.json({
      token: 'new-mock-jwt-token',
      refreshToken: 'new-mock-refresh-token',
    })
  }),

  http.post(`${API_BASE_URL}/auth/logout`, () => {
    return HttpResponse.json({ message: 'Logged out successfully' })
  }),

  // User endpoints
  http.get(`${API_BASE_URL}/users/profile`, () => {
    return HttpResponse.json({
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'User',
      isEmailConfirmed: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    })
  }),

  http.put(`${API_BASE_URL}/users/profile`, () => {
    return HttpResponse.json({
      id: '1',
      email: '<EMAIL>',
      firstName: 'John Updated',
      lastName: 'Doe Updated',
      role: 'User',
      isEmailConfirmed: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T12:00:00Z',
    })
  }),

  // Users list (admin)
  http.get(`${API_BASE_URL}/users`, () => {
    return HttpResponse.json({
      data: [
        {
          id: '1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: 'User',
          isEmailConfirmed: true,
          createdAt: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'User',
          role: 'Admin',
          isEmailConfirmed: true,
          createdAt: '2024-01-01T00:00:00Z',
        },
      ],
      totalCount: 2,
      pageNumber: 1,
      pageSize: 10,
      totalPages: 1,
    })
  }),

  // Health check
  http.get(`${API_BASE_URL}/health`, () => {
    return HttpResponse.json({
      status: 'Healthy',
      timestamp: new Date().toISOString(),
    })
  }),

  // Error handlers
  http.get(`${API_BASE_URL}/error/500`, () => {
    return HttpResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }),

  http.get(`${API_BASE_URL}/error/401`, () => {
    return HttpResponse.json(
      { message: 'Unauthorized' },
      { status: 401 }
    )
  }),

  http.get(`${API_BASE_URL}/error/404`, () => {
    return HttpResponse.json(
      { message: 'Not found' },
      { status: 404 }
    )
  }),
]
