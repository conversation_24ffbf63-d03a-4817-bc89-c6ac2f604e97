namespace ServiceLink.Domain.Entities;

/// <summary>
/// Entité représentant une catégorie de service
/// </summary>
public class ServiceCategory : BaseEntity
{
    /// <summary>
    /// Nom de la catégorie
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// Description de la catégorie
    /// </summary>
    public string Description { get; private set; } = string.Empty;

    /// <summary>
    /// Slug pour l'URL
    /// </summary>
    public string Slug { get; private set; } = string.Empty;

    /// <summary>
    /// Icône de la catégorie
    /// </summary>
    public string Icon { get; private set; } = string.Empty;

    /// <summary>
    /// Couleur de la catégorie (hex)
    /// </summary>
    public string Color { get; private set; } = "#000000";

    /// <summary>
    /// Image de la catégorie
    /// </summary>
    public string? ImageUrl { get; private set; }

    /// <summary>
    /// Ordre d'affichage
    /// </summary>
    public int DisplayOrder { get; private set; }

    /// <summary>
    /// Indique si la catégorie est active
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// Indique si la catégorie est mise en avant
    /// </summary>
    public bool IsFeatured { get; private set; }

    /// <summary>
    /// ID de la catégorie parent (pour hiérarchie)
    /// </summary>
    public Guid? ParentCategoryId { get; private set; }

    /// <summary>
    /// Taux de commission par défaut pour cette catégorie
    /// </summary>
    public decimal DefaultCommissionRate { get; private set; }

    /// <summary>
    /// Métadonnées SEO (JSON)
    /// </summary>
    public string? SeoMetadata { get; private set; }

    /// <summary>
    /// Navigation vers la catégorie parent
    /// </summary>
    public virtual ServiceCategory? ParentCategory { get; set; }

    /// <summary>
    /// Sous-catégories
    /// </summary>
    public virtual ICollection<ServiceCategory> SubCategories { get; set; } = new List<ServiceCategory>();

    /// <summary>
    /// Services dans cette catégorie
    /// </summary>
    public virtual ICollection<Service> Services { get; set; } = new List<Service>();

    /// <summary>
    /// Constructeur privé pour EF Core
    /// </summary>
    private ServiceCategory() { }

    /// <summary>
    /// Constructeur pour créer une nouvelle catégorie
    /// </summary>
    public ServiceCategory(
        string name,
        string description,
        string slug,
        string icon,
        string color = "#000000",
        decimal defaultCommissionRate = 5.0m,
        Guid? parentCategoryId = null)
    {
        Name = name;
        Description = description;
        Slug = slug;
        Icon = icon;
        Color = color;
        DefaultCommissionRate = defaultCommissionRate;
        ParentCategoryId = parentCategoryId;
        IsActive = true;
        DisplayOrder = 0;
    }

    /// <summary>
    /// Met à jour les informations de base
    /// </summary>
    public void UpdateBasicInfo(string name, string description, string icon, string color)
    {
        Name = name;
        Description = description;
        Icon = icon;
        Color = color;
    }

    /// <summary>
    /// Met à jour l'image
    /// </summary>
    public void UpdateImage(string? imageUrl)
    {
        ImageUrl = imageUrl;
    }

    /// <summary>
    /// Met à jour l'ordre d'affichage
    /// </summary>
    public void UpdateDisplayOrder(int order)
    {
        DisplayOrder = order;
    }

    /// <summary>
    /// Active ou désactive la catégorie
    /// </summary>
    public void SetActive(bool isActive)
    {
        IsActive = isActive;
    }

    /// <summary>
    /// Met en avant ou retire de la mise en avant
    /// </summary>
    public void SetFeatured(bool isFeatured)
    {
        IsFeatured = isFeatured;
    }

    /// <summary>
    /// Met à jour le taux de commission
    /// </summary>
    public void UpdateCommissionRate(decimal rate)
    {
        if (rate < 0 || rate > 100)
            throw new ArgumentException("Commission rate must be between 0 and 100");

        DefaultCommissionRate = rate;
    }

    /// <summary>
    /// Met à jour les métadonnées SEO
    /// </summary>
    public void UpdateSeoMetadata(string? metadata)
    {
        SeoMetadata = metadata;
    }

    /// <summary>
    /// Vérifie si c'est une catégorie racine
    /// </summary>
    public bool IsRootCategory => ParentCategoryId == null;

    /// <summary>
    /// Obtient le chemin complet de la catégorie
    /// </summary>
    public string GetFullPath()
    {
        if (ParentCategory == null)
            return Name;

        return $"{ParentCategory.GetFullPath()} > {Name}";
    }
}
