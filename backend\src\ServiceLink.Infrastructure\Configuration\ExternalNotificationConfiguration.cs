using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SendGrid;
using ServiceLink.Application.Interfaces;
using ServiceLink.Infrastructure.Services;

namespace ServiceLink.Infrastructure.Configuration;

/// <summary>
/// Configuration des services de notification externes
/// </summary>
public static class ExternalNotificationConfiguration
{
    /// <summary>
    /// Configure les services de notification externes
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Collection de services</returns>
    public static IServiceCollection AddExternalNotificationServices(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        try
        {
            // Configuration des settings
            services.Configure<ExternalNotificationSettings>(configuration.GetSection("ExternalNotifications"));
            services.Configure<SendGridSettings>(configuration.GetSection("SendGrid"));
            services.Configure<TwilioSettings>(configuration.GetSection("Twilio"));
            services.Configure<FirebaseSettings>(configuration.GetSection("Firebase"));

            // Service de templates
            services.AddSingleton<INotificationTemplateService, NotificationTemplateService>();

            // Configuration SendGrid
            var sendGridSettings = configuration.GetSection("SendGrid").Get<SendGridSettings>();
            if (!string.IsNullOrEmpty(sendGridSettings?.ApiKey))
            {
                services.AddScoped<ISendGridClient>(provider => new SendGridClient(sendGridSettings.ApiKey));
                services.AddScoped<SendGridEmailService>();
            }

            // Configuration des services individuels
            services.AddScoped<TwilioSmsService>();
            services.AddScoped<FirebasePushService>();

            // Factory principale
            services.AddScoped<IExternalNotificationServiceFactory, ExternalNotificationServiceFactory>();

            // Health checks pour les services de notification
            services.AddHealthChecks()
                .AddCheck<SendGridHealthCheck>("sendgrid")
                .AddCheck<TwilioHealthCheck>("twilio")
                .AddCheck<FirebaseHealthCheck>("firebase");

            return services;
        }
        catch (Exception ex)
        {
            // Log l'erreur sans utiliser le service provider
            Console.WriteLine($"Erreur lors de la configuration des services de notification externes: {ex.Message}");
            throw;
        }
    }
}

/// <summary>
/// Health check pour SendGrid
/// </summary>
public class SendGridHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly ILogger<SendGridHealthCheck> _logger;
    private readonly SendGridSettings _settings;

    public SendGridHealthCheck(
        ILogger<SendGridHealthCheck> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _settings = configuration.GetSection("SendGrid").Get<SendGridSettings>() ?? new SendGridSettings();
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_settings.ApiKey))
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("SendGrid API Key non configurée");
            }

            // Test basique de connectivité
            await Task.Delay(1, cancellationToken);
            
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("SendGrid configuré correctement");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du health check SendGrid");
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy($"Erreur SendGrid: {ex.Message}");
        }
    }
}

/// <summary>
/// Health check pour Twilio
/// </summary>
public class TwilioHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly ILogger<TwilioHealthCheck> _logger;
    private readonly TwilioSettings _settings;

    public TwilioHealthCheck(
        ILogger<TwilioHealthCheck> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _settings = configuration.GetSection("Twilio").Get<TwilioSettings>() ?? new TwilioSettings();
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_settings.AccountSid) || string.IsNullOrEmpty(_settings.AuthToken))
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Twilio credentials non configurées");
            }

            if (string.IsNullOrEmpty(_settings.FromPhoneNumber))
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Numéro Twilio expéditeur non configuré");
            }

            // Test basique de connectivité
            await Task.Delay(1, cancellationToken);
            
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Twilio configuré correctement");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du health check Twilio");
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy($"Erreur Twilio: {ex.Message}");
        }
    }
}

/// <summary>
/// Health check pour Firebase
/// </summary>
public class FirebaseHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly ILogger<FirebaseHealthCheck> _logger;
    private readonly FirebaseSettings _settings;

    public FirebaseHealthCheck(
        ILogger<FirebaseHealthCheck> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _settings = configuration.GetSection("Firebase").Get<FirebaseSettings>() ?? new FirebaseSettings();
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_settings.ProjectId))
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Firebase Project ID non configuré");
            }

            var hasServiceAccount = !string.IsNullOrEmpty(_settings.ServiceAccountKeyPath) && File.Exists(_settings.ServiceAccountKeyPath) ||
                                   !string.IsNullOrEmpty(_settings.ServiceAccountKeyJson);

            if (!hasServiceAccount)
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Firebase Service Account non configuré");
            }

            // Test basique de connectivité
            await Task.Delay(1, cancellationToken);
            
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Firebase configuré correctement");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du health check Firebase");
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy($"Erreur Firebase: {ex.Message}");
        }
    }
}

/// <summary>
/// Middleware pour gérer les webhooks de notification
/// </summary>
public class NotificationWebhookMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<NotificationWebhookMiddleware> _logger;

    public NotificationWebhookMiddleware(
        RequestDelegate next,
        ILogger<NotificationWebhookMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Traitement des webhooks de notification
            if (context.Request.Path.StartsWithSegments("/api/webhooks/notifications"))
            {
                await HandleNotificationWebhook(context);
                return;
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur dans le middleware de webhooks de notification");
            context.Response.StatusCode = 500;
            await context.Response.WriteAsync("Erreur interne du serveur");
        }
    }

    private async Task HandleNotificationWebhook(HttpContext context)
    {
        try
        {
            var provider = context.Request.Path.Value?.Split('/').LastOrDefault();
            
            _logger.LogInformation("Webhook de notification reçu pour le provider: {Provider}", provider);

            // Lecture du body
            using var reader = new StreamReader(context.Request.Body);
            var body = await reader.ReadToEndAsync();

            // Traitement selon le provider
            switch (provider?.ToLowerInvariant())
            {
                case "sendgrid":
                    await HandleSendGridWebhook(context, body);
                    break;
                case "twilio":
                    await HandleTwilioWebhook(context, body);
                    break;
                case "firebase":
                    await HandleFirebaseWebhook(context, body);
                    break;
                default:
                    context.Response.StatusCode = 404;
                    await context.Response.WriteAsync("Provider non supporté");
                    return;
            }

            context.Response.StatusCode = 200;
            await context.Response.WriteAsync("OK");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook de notification");
            context.Response.StatusCode = 500;
            await context.Response.WriteAsync("Erreur de traitement");
        }
    }

    private async Task HandleSendGridWebhook(HttpContext context, string body)
    {
        _logger.LogDebug("Traitement webhook SendGrid: {Body}", body);
        // Traitement des événements SendGrid (delivered, opened, clicked, etc.)
        await Task.CompletedTask;
    }

    private async Task HandleTwilioWebhook(HttpContext context, string body)
    {
        _logger.LogDebug("Traitement webhook Twilio: {Body}", body);
        // Traitement des événements Twilio (delivered, failed, etc.)
        await Task.CompletedTask;
    }

    private async Task HandleFirebaseWebhook(HttpContext context, string body)
    {
        _logger.LogDebug("Traitement webhook Firebase: {Body}", body);
        // Traitement des événements Firebase (si configurés)
        await Task.CompletedTask;
    }
}
