@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.5;
    font-weight: 400;

    /* Shadcn UI Variables */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 142 76% 36%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 142 76% 36%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    margin: 0;
    min-height: 100vh;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--accent-500));
}

/* Focus styles */
.focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* ServiceLink Custom Components */
@layer components {
  .service-card {
    @apply bg-card border border-border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow;
  }

  .booking-status-pending {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
  }

  .booking-status-confirmed {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
  }

  .booking-status-completed {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
  }

  .booking-status-cancelled {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
  }

  .booking-status-rejected {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300;
  }

  .booking-status-inprogress {
    @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300;
  }

  .booking-status-expired {
    @apply bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300;
  }
}

/* Smooth transitions */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
