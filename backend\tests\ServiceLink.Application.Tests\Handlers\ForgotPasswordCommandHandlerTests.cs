using System.Threading;
using System.Threading.Tasks;
using Moq;
using Xunit;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Handlers;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;

public class ForgotPasswordCommandHandlerTests
{
    [Fact]
    public async Task Handle_ValidUser_SendsResetEmailAndReturnsTrue()
    {
        // Arrange
        var userRepository = new Mock<IUserRepository>();
        var emailService = new Mock<IEmailService>();
        var passwordResetTokenRepository = new Mock<IPasswordResetTokenRepository>();
        var user = new User(
            ServiceLink.Domain.ValueObjects.Email.Create("<EMAIL>"),
            "Test",
            "User",
            ServiceLink.Domain.Enums.UserRole.Client,
            null,
            null
        );
        user.Activate();
        userRepository.Setup(r => r.GetByEmailAsync(It.IsAny<string>(), It.IsAny<CancellationToken>())).Returns((string email, CancellationToken ct) => Task.FromResult(user));
        passwordResetTokenRepository.Setup(r => r.GenerateToken()).Returns("reset-token");
        passwordResetTokenRepository.Setup(r => r.SaveAsync(user.Id, "reset-token")).Returns((Guid id, string token) => Task.CompletedTask);
        emailService.Setup(e => e.SendPasswordResetEmailAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>())).Returns((string a, string b, string c, CancellationToken ct) => Task.CompletedTask);
        var handler = new ForgotPasswordCommandHandler(userRepository.Object, emailService.Object, passwordResetTokenRepository.Object);
        var command = new ForgotPasswordCommand { Email = "<EMAIL>" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.True(result);
    }
    [Fact]
    public async Task Handle_UserNotFoundOrInactive_ReturnsFalse()
    {
        // Arrange
        var userRepository = new Mock<IUserRepository>();
        var emailService = new Mock<IEmailService>();
        var passwordResetTokenRepository = new Mock<IPasswordResetTokenRepository>();
        userRepository.Setup(r => r.GetByEmailAsync(It.IsAny<string>(), It.IsAny<CancellationToken>())).Returns((string email, CancellationToken ct) => Task.FromResult<User?>(null));
        var handler = new ForgotPasswordCommandHandler(userRepository.Object, emailService.Object, passwordResetTokenRepository.Object);
        var command = new ForgotPasswordCommand { Email = "<EMAIL>" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.False(result);
    }
}
