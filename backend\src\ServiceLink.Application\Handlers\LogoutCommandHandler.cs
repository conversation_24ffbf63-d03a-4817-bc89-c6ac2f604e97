using MediatR;
using ServiceLink.Application.Commands;
using ServiceLink.Domain.Interfaces;

namespace ServiceLink.Application.Handlers;

public class LogoutCommandHandler : IRequestHandler<LogoutCommand, bool>
{
    private readonly IRefreshTokenRepository _refreshTokenRepository;

    public LogoutCommandHandler(IRefreshTokenRepository refreshTokenRepository)
    {
        _refreshTokenRepository = refreshTokenRepository;
    }

    public async Task<bool> Handle(LogoutCommand request, CancellationToken cancellationToken)
    {
        // Invalider le refresh token fourni
        if (string.IsNullOrEmpty(request.RefreshToken))
            return false;
        await _refreshTokenRepository.InvalidateAsync(request.RefreshToken);
        return true;
    }
}
