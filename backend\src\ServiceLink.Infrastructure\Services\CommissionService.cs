using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Implémentation du service de calcul automatique des commissions
/// </summary>
public class CommissionService : ICommissionService
{
    private readonly ILogger<CommissionService> _logger;
    private readonly CommissionSettings _settings;
    private readonly IPaymentServiceFactory _paymentServiceFactory;

    public CommissionService(
        IConfiguration configuration,
        ILogger<CommissionService> logger,
        IPaymentServiceFactory paymentServiceFactory)
    {
        _logger = logger;
        _settings = configuration.GetSection("Commission").Get<CommissionSettings>() ?? new CommissionSettings();
        _paymentServiceFactory = paymentServiceFactory;
    }

    /// <inheritdoc />
    public async Task<CommissionCalculationResult> CalculateCommissionAsync(
        CommissionCalculationRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calcul de commission pour montant {Amount} {Currency}, provider {Provider}", 
                request.BaseAmount, request.Currency, request.PaymentProvider);

            var result = new CommissionCalculationResult
            {
                BaseAmount = request.BaseAmount,
                Currency = request.Currency,
                Breakdown = new CommissionBreakdown()
            };

            var steps = new List<CalculationStep>();

            // Étape 1: Calcul de la commission de base de la plateforme
            var platformCommission = CalculatePlatformCommission(
                request.BaseAmount, 
                request.ServiceCategory, 
                request.ProviderTier);

            result.PlatformCommission = platformCommission;
            result.Breakdown.BasePlatformCommission = platformCommission;

            steps.Add(new CalculationStep
            {
                Description = "Commission de base de la plateforme",
                AmountBefore = request.BaseAmount,
                Adjustment = -platformCommission,
                AmountAfter = request.BaseAmount - platformCommission,
                Rate = GetCommissionRates(request.PaymentProvider, request.ServiceCategory).BaseCommissionRate
            });

            // Étape 2: Calcul des frais de traitement
            var processingFees = CalculateProcessingFees(
                request.BaseAmount, 
                request.PaymentProvider, 
                request.PaymentMethod);

            result.ProcessingFees = processingFees;
            result.Breakdown.FixedProcessingFee = GetFixedProcessingFee(request.PaymentProvider, request.PaymentMethod);
            result.Breakdown.VariableProcessingFee = processingFees - result.Breakdown.FixedProcessingFee;

            steps.Add(new CalculationStep
            {
                Description = "Frais de traitement du provider",
                AmountBefore = request.BaseAmount - platformCommission,
                Adjustment = -processingFees,
                AmountAfter = request.BaseAmount - platformCommission - processingFees,
                Rate = GetCommissionRates(request.PaymentProvider, request.ServiceCategory).ProcessingFeeRate
            });

            // Étape 3: Calcul du montant net pour le prestataire
            result.NetAmount = request.BaseAmount - platformCommission - processingFees;

            // Étape 4: Calcul du montant total à facturer (incluant les frais)
            result.TotalAmount = request.BaseAmount + processingFees;

            // Calcul des taux
            result.CommissionRate = request.BaseAmount > 0 
                ? (decimal)platformCommission / request.BaseAmount * 100 
                : 0;

            result.ProcessingFeeRate = request.BaseAmount > 0 
                ? (decimal)processingFees / request.BaseAmount * 100 
                : 0;

            result.Breakdown.Steps = steps;

            _logger.LogInformation("Commission calculée: Base={BaseAmount}, Commission={Commission}, Frais={Fees}, Net={Net}", 
                result.BaseAmount, result.PlatformCommission, result.ProcessingFees, result.NetAmount);

            // Simuler une opération async
            await Task.Delay(1, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de commission pour {BookingId}", request.BookingId);
            throw;
        }
    }

    /// <inheritdoc />
    public CommissionRateConfiguration GetCommissionRates(PaymentProvider provider, string serviceCategory)
    {
        try
        {
            _logger.LogDebug("Récupération des taux de commission pour {Provider}, catégorie {Category}", 
                provider, serviceCategory);

            // Configuration par défaut selon le provider
            var config = provider switch
            {
                PaymentProvider.Stripe => new CommissionRateConfiguration
                {
                    BaseCommissionRate = _settings.StripeCommissionRate,
                    ProcessingFeeRate = 2.9m,
                    FixedProcessingFee = 30, // 0.30 EUR
                    FixedFee = 0,
                    MinimumCommission = 50, // 0.50 EUR
                    MaximumCommission = 50000 // 500.00 EUR
                },
                PaymentProvider.PayPal => new CommissionRateConfiguration
                {
                    BaseCommissionRate = _settings.PayPalCommissionRate,
                    ProcessingFeeRate = 3.4m,
                    FixedProcessingFee = 35, // 0.35 EUR
                    FixedFee = 0,
                    MinimumCommission = 50,
                    MaximumCommission = 50000
                },
                PaymentProvider.Flutterwave => new CommissionRateConfiguration
                {
                    BaseCommissionRate = _settings.FlutterwaveCommissionRate,
                    ProcessingFeeRate = 1.4m,
                    FixedProcessingFee = 0,
                    FixedFee = 0,
                    MinimumCommission = 25,
                    MaximumCommission = 25000
                },
                _ => new CommissionRateConfiguration
                {
                    BaseCommissionRate = _settings.DefaultCommissionRate,
                    ProcessingFeeRate = 2.5m,
                    FixedProcessingFee = 30,
                    FixedFee = 0,
                    MinimumCommission = 50,
                    MaximumCommission = 50000
                }
            };

            // Ajustements par catégorie de service
            config = AdjustRatesForServiceCategory(config, serviceCategory);

            // Configuration des réductions par niveau
            config.TierDiscounts = new Dictionary<UserTier, decimal>
            {
                [UserTier.Newcomer] = 0m,      // Aucune réduction
                [UserTier.Standard] = 5m,      // 5% de réduction
                [UserTier.Premium] = 10m,      // 10% de réduction
                [UserTier.VIP] = 15m,          // 15% de réduction
                [UserTier.Partner] = 20m       // 20% de réduction
            };

            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des taux pour {Provider}", provider);
            throw;
        }
    }

    /// <inheritdoc />
    public long CalculatePlatformCommission(long amount, string serviceCategory, UserTier userTier)
    {
        try
        {
            var rates = GetCommissionRates(PaymentProvider.Stripe, serviceCategory); // Utiliser Stripe comme référence
            
            // Calcul de la commission de base
            var baseCommission = (long)(amount * rates.BaseCommissionRate / 100);
            
            // Application de la réduction selon le niveau utilisateur
            var discount = rates.TierDiscounts.GetValueOrDefault(userTier, 0m);
            var discountAmount = (long)(baseCommission * discount / 100);
            
            var finalCommission = baseCommission - discountAmount;
            
            // Respect des limites min/max
            finalCommission = Math.Max(finalCommission, rates.MinimumCommission);
            finalCommission = Math.Min(finalCommission, rates.MaximumCommission);

            _logger.LogDebug("Commission plateforme calculée: Base={Base}, Réduction={Discount}, Final={Final}", 
                baseCommission, discountAmount, finalCommission);

            return finalCommission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de la commission plateforme");
            throw;
        }
    }

    /// <inheritdoc />
    public long CalculateProcessingFees(long amount, PaymentProvider provider, PaymentMethodType paymentMethod)
    {
        try
        {
            var rates = GetCommissionRates(provider, "default");
            
            // Frais variables basés sur le pourcentage
            var variableFee = (long)(amount * rates.ProcessingFeeRate / 100);
            
            // Frais fixes selon la méthode de paiement
            var fixedFee = GetFixedProcessingFee(provider, paymentMethod);
            
            var totalFees = variableFee + fixedFee;

            _logger.LogDebug("Frais de traitement calculés: Variable={Variable}, Fixe={Fixed}, Total={Total}", 
                variableFee, fixedFee, totalFees);

            return totalFees;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul des frais de traitement");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<CommissionHistoryDto>> GetCommissionHistoryAsync(
        Guid userId, 
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Récupération de l'historique des commissions pour {UserId} du {StartDate} au {EndDate}", 
                userId, startDate, endDate);

            // En production, récupérer depuis la base de données
            // Pour l'instant, retourner des données de test
            var history = new List<CommissionHistoryDto>
            {
                new CommissionHistoryDto
                {
                    Id = Guid.NewGuid(),
                    BookingId = Guid.NewGuid(),
                    PaymentId = "pi_test_123",
                    BaseAmount = 5000,
                    PlatformCommission = 250,
                    ProcessingFees = 175,
                    NetAmount = 4575,
                    Status = CommissionStatus.Paid,
                    CreatedAt = DateTime.UtcNow.AddDays(-7),
                    PaidAt = DateTime.UtcNow.AddDays(-1),
                    PaymentProvider = PaymentProvider.Stripe,
                    Currency = "EUR"
                }
            };

            await Task.Delay(1, cancellationToken);
            return history;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'historique des commissions pour {UserId}", userId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<CommissionPayoutResult> ProcessCommissionPayoutAsync(
        CommissionPayoutRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Traitement du paiement de commission pour {ProviderId}, montant {Amount}",
                request.ServiceProviderId, request.Amount);

            // En production, intégrer avec les APIs de paiement pour les virements
            // Pour l'instant, simuler le traitement

            var payoutFees = CalculatePayoutFees(request.Amount, request.PayoutMethod);
            var netAmount = request.Amount - payoutFees;

            var result = new CommissionPayoutResult
            {
                Success = true,
                PayoutTransactionId = $"payout_{DateTime.UtcNow:yyyyMMddHHmmss}",
                AmountPaid = request.Amount,
                PayoutFees = payoutFees,
                NetAmountReceived = netAmount,
                ProcessedAt = DateTime.UtcNow,
                EstimatedArrival = CalculateEstimatedArrival(request.PayoutMethod),
                ProcessedCommissionIds = request.CommissionIds
            };

            _logger.LogInformation("Paiement de commission traité: {TransactionId}, Net={NetAmount}",
                result.PayoutTransactionId, result.NetAmountReceived);

            await Task.Delay(100, cancellationToken); // Simuler le traitement
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du paiement de commission pour {ProviderId}",
                request.ServiceProviderId);

            return new CommissionPayoutResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                ProcessedCommissionIds = request.CommissionIds
            };
        }
    }

    /// <inheritdoc />
    public async Task<decimal> GetCommissionRateAsync(Guid categoryId, Guid providerId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Récupération du taux de commission pour la catégorie {CategoryId} et le prestataire {ProviderId}",
                categoryId, providerId);

            // En production, récupérer depuis la base de données en fonction de la catégorie et du prestataire
            // Pour l'instant, retourner un taux par défaut basé sur des règles simples

            await Task.Delay(1, cancellationToken); // Simuler l'accès à la base de données

            // Taux par défaut selon la catégorie (simulation)
            var defaultRate = _settings.DefaultCommissionRate;

            // TODO: Implémenter la logique de récupération depuis la base de données
            // - Vérifier s'il y a un taux spécifique pour cette catégorie
            // - Vérifier s'il y a un taux négocié pour ce prestataire
            // - Appliquer les réductions selon le volume d'affaires du prestataire

            return defaultRate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du taux de commission pour {CategoryId}/{ProviderId}",
                categoryId, providerId);

            // En cas d'erreur, retourner le taux par défaut
            return _settings.DefaultCommissionRate;
        }
    }

    #region Méthodes privées

    private CommissionRateConfiguration AdjustRatesForServiceCategory(CommissionRateConfiguration config, string serviceCategory)
    {
        // Ajustements selon la catégorie de service
        var adjustedConfig = new CommissionRateConfiguration
        {
            BaseCommissionRate = config.BaseCommissionRate,
            ProcessingFeeRate = config.ProcessingFeeRate,
            FixedProcessingFee = config.FixedProcessingFee,
            FixedFee = config.FixedFee,
            MinimumCommission = config.MinimumCommission,
            MaximumCommission = config.MaximumCommission,
            TierDiscounts = config.TierDiscounts
        };

        adjustedConfig.BaseCommissionRate = serviceCategory.ToLowerInvariant() switch
        {
            "cleaning" => config.BaseCommissionRate * 0.9m, // 10% de réduction
            "maintenance" => config.BaseCommissionRate * 0.95m, // 5% de réduction
            "premium" => config.BaseCommissionRate * 1.1m, // 10% d'augmentation
            _ => config.BaseCommissionRate
        };

        return adjustedConfig;
    }

    private long GetFixedProcessingFee(PaymentProvider provider, PaymentMethodType paymentMethod)
    {
        return (provider, paymentMethod) switch
        {
            (PaymentProvider.Stripe, PaymentMethodType.Card) => 30, // 0.30 EUR
            (PaymentProvider.Stripe, PaymentMethodType.ApplePay) => 30,
            (PaymentProvider.Stripe, PaymentMethodType.GooglePay) => 30,
            (PaymentProvider.PayPal, PaymentMethodType.PayPal) => 35, // 0.35 EUR
            (PaymentProvider.PayPal, PaymentMethodType.Card) => 35,
            (PaymentProvider.Flutterwave, PaymentMethodType.Card) => 0,
            (PaymentProvider.Flutterwave, PaymentMethodType.MobileMoney) => 0,
            _ => 25 // 0.25 EUR par défaut
        };
    }

    private long CalculatePayoutFees(long amount, PayoutMethod payoutMethod)
    {
        return payoutMethod switch
        {
            PayoutMethod.BankTransfer => Math.Max(100, (long)(amount * 0.005m)), // Min 1.00 EUR ou 0.5%
            PayoutMethod.PayPal => (long)(amount * 0.02m), // 2%
            PayoutMethod.MobileMoney => (long)(amount * 0.01m), // 1%
            PayoutMethod.Crypto => (long)(amount * 0.001m), // 0.1%
            _ => 100 // 1.00 EUR par défaut
        };
    }

    private DateTime CalculateEstimatedArrival(PayoutMethod payoutMethod)
    {
        var businessDays = payoutMethod switch
        {
            PayoutMethod.BankTransfer => 2, // 2 jours ouvrés
            PayoutMethod.PayPal => 0, // Instantané
            PayoutMethod.MobileMoney => 0, // Instantané
            PayoutMethod.Crypto => 0, // Instantané
            _ => 1 // 1 jour par défaut
        };

        var estimatedDate = DateTime.UtcNow.AddDays(businessDays);
        
        // Ajuster pour éviter les week-ends (simplification)
        while (estimatedDate.DayOfWeek == DayOfWeek.Saturday || estimatedDate.DayOfWeek == DayOfWeek.Sunday)
        {
            estimatedDate = estimatedDate.AddDays(1);
        }

        return estimatedDate;
    }

    #endregion
}

/// <summary>
/// Configuration des commissions
/// </summary>
public class CommissionSettings
{
    /// <summary>
    /// Taux de commission par défaut
    /// </summary>
    public decimal DefaultCommissionRate { get; set; } = 5.0m;

    /// <summary>
    /// Taux de commission Stripe
    /// </summary>
    public decimal StripeCommissionRate { get; set; } = 4.5m;

    /// <summary>
    /// Taux de commission PayPal
    /// </summary>
    public decimal PayPalCommissionRate { get; set; } = 5.0m;

    /// <summary>
    /// Taux de commission Flutterwave
    /// </summary>
    public decimal FlutterwaveCommissionRate { get; set; } = 3.5m;

    /// <summary>
    /// Commission minimum en centimes
    /// </summary>
    public long MinimumCommission { get; set; } = 50; // 0.50 EUR

    /// <summary>
    /// Commission maximum en centimes
    /// </summary>
    public long MaximumCommission { get; set; } = 50000; // 500.00 EUR

    /// <summary>
    /// Activer les réductions par niveau
    /// </summary>
    public bool EnableTierDiscounts { get; set; } = true;

    /// <summary>
    /// Fréquence de paiement des commissions en jours
    /// </summary>
    public int PayoutFrequencyDays { get; set; } = 7; // Hebdomadaire

    /// <summary>
    /// Montant minimum pour déclencher un paiement automatique
    /// </summary>
    public long MinimumPayoutAmount { get; set; } = 2000; // 20.00 EUR
}
