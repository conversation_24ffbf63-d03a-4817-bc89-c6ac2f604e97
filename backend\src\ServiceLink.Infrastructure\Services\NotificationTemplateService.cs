using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using System.Text.RegularExpressions;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Service de gestion des templates de notification
/// </summary>
public class NotificationTemplateService : INotificationTemplateService
{
    private readonly ILogger<NotificationTemplateService> _logger;
    private readonly Dictionary<string, NotificationTemplate> _templates;

    public NotificationTemplateService(ILogger<NotificationTemplateService> logger)
    {
        _logger = logger;
        _templates = new Dictionary<string, NotificationTemplate>();
        
        InitializeDefaultTemplates();
    }

    /// <inheritdoc />
    public async Task<NotificationTemplate?> GetTemplateAsync(string templateName, string language = "fr")
    {
        try
        {
            var key = $"{templateName}_{language}";
            
            if (_templates.TryGetValue(key, out var template))
            {
                _logger.LogDebug("Template {TemplateName} trouvé pour la langue {Language}", templateName, language);
                return template;
            }

            // Fallback vers la langue par défaut si le template n'existe pas dans la langue demandée
            if (language != "fr")
            {
                var fallbackKey = $"{templateName}_fr";
                if (_templates.TryGetValue(fallbackKey, out var fallbackTemplate))
                {
                    _logger.LogWarning("Template {TemplateName} non trouvé pour {Language}, utilisation du fallback français", 
                        templateName, language);
                    return fallbackTemplate;
                }
            }

            _logger.LogWarning("Template {TemplateName} non trouvé pour la langue {Language}", templateName, language);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du template {TemplateName}", templateName);
            return null;
        }
    }

    /// <inheritdoc />
    public string RenderTemplate(NotificationTemplate template, Dictionary<string, object> data)
    {
        try
        {
            var content = template.Content;
            
            // Remplacement des variables dans le template
            foreach (var variable in data)
            {
                var placeholder = $"{{{{{variable.Key}}}}}";
                var value = variable.Value?.ToString() ?? string.Empty;
                content = content.Replace(placeholder, value);
            }

            // Remplacement des variables système
            content = ReplaceSystemVariables(content);

            _logger.LogDebug("Template {TemplateName} rendu avec {VariableCount} variables", 
                template.Name, data.Count);

            return content;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du rendu du template {TemplateName}", template.Name);
            return template.Content; // Retourner le contenu original en cas d'erreur
        }
    }

    /// <inheritdoc />
    public async Task<NotificationTemplate> SaveTemplateAsync(
        NotificationTemplate template, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            template.UpdatedAt = DateTime.UtcNow;
            
            if (template.Id == Guid.Empty)
            {
                template.Id = Guid.NewGuid();
                template.CreatedAt = DateTime.UtcNow;
                template.Version = 1;
            }
            else
            {
                template.Version++;
            }

            var key = $"{template.Name}_{template.Language}";
            _templates[key] = template;

            _logger.LogInformation("Template {TemplateName} sauvegardé avec succès (version {Version})", 
                template.Name, template.Version);

            await Task.Delay(1, cancellationToken); // Simuler une opération async
            return template;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la sauvegarde du template {TemplateName}", template.Name);
            throw;
        }
    }

    /// <summary>
    /// Obtient tous les templates disponibles
    /// </summary>
    /// <returns>Liste des templates</returns>
    public IEnumerable<NotificationTemplate> GetAllTemplates()
    {
        return _templates.Values;
    }

    /// <summary>
    /// Supprime un template
    /// </summary>
    /// <param name="templateName">Nom du template</param>
    /// <param name="language">Langue du template</param>
    /// <returns>True si supprimé avec succès</returns>
    public bool DeleteTemplate(string templateName, string language = "fr")
    {
        try
        {
            var key = $"{templateName}_{language}";
            var removed = _templates.Remove(key);
            
            if (removed)
            {
                _logger.LogInformation("Template {TemplateName} supprimé pour la langue {Language}", templateName, language);
            }
            
            return removed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du template {TemplateName}", templateName);
            return false;
        }
    }

    /// <summary>
    /// Valide un template
    /// </summary>
    /// <param name="template">Template à valider</param>
    /// <returns>Liste des erreurs de validation</returns>
    public List<string> ValidateTemplate(NotificationTemplate template)
    {
        var errors = new List<string>();

        try
        {
            if (string.IsNullOrWhiteSpace(template.Name))
            {
                errors.Add("Le nom du template est requis");
            }

            if (string.IsNullOrWhiteSpace(template.Content))
            {
                errors.Add("Le contenu du template est requis");
            }

            if (string.IsNullOrWhiteSpace(template.Language))
            {
                errors.Add("La langue du template est requise");
            }

            // Validation des variables dans le template
            var variablesInContent = ExtractVariablesFromContent(template.Content);
            var undeclaredVariables = variablesInContent.Except(template.Variables).ToList();
            
            if (undeclaredVariables.Any())
            {
                errors.Add($"Variables non déclarées trouvées: {string.Join(", ", undeclaredVariables)}");
            }

            _logger.LogDebug("Validation du template {TemplateName}: {ErrorCount} erreurs trouvées", 
                template.Name, errors.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la validation du template {TemplateName}", template.Name);
            errors.Add($"Erreur de validation: {ex.Message}");
        }

        return errors;
    }

    #region Méthodes privées

    /// <summary>
    /// Initialise les templates par défaut
    /// </summary>
    private void InitializeDefaultTemplates()
    {
        try
        {
            // Template de bienvenue
            var welcomeTemplate = new NotificationTemplate
            {
                Id = Guid.NewGuid(),
                Name = "welcome",
                Type = ExternalNotificationType.Email,
                Language = "fr",
                Subject = "Bienvenue sur ServiceLink, {{userName}}!",
                Content = @"
                    <h1>Bienvenue sur ServiceLink!</h1>
                    <p>Bonjour {{userName}},</p>
                    <p>Nous sommes ravis de vous accueillir sur ServiceLink, votre plateforme de services à domicile.</p>
                    <p>Votre compte a été créé avec succès. Vous pouvez maintenant:</p>
                    <ul>
                        <li>Rechercher des services près de chez vous</li>
                        <li>Réserver des prestations en quelques clics</li>
                        <li>Gérer vos réservations en temps réel</li>
                    </ul>
                    <p>Si vous avez des questions, n'hésitez pas à nous contacter.</p>
                    <p>Cordialement,<br>L'équipe ServiceLink</p>
                ",
                Variables = new List<string> { "userName" },
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Version = 1
            };

            // Template de confirmation de réservation
            var bookingConfirmationTemplate = new NotificationTemplate
            {
                Id = Guid.NewGuid(),
                Name = "booking_confirmation",
                Type = ExternalNotificationType.Email,
                Language = "fr",
                Subject = "Confirmation de votre réservation #{{bookingId}}",
                Content = @"
                    <h1>Réservation confirmée!</h1>
                    <p>Bonjour {{userName}},</p>
                    <p>Votre réservation a été confirmée avec succès.</p>
                    <h2>Détails de la réservation:</h2>
                    <ul>
                        <li><strong>Service:</strong> {{serviceName}}</li>
                        <li><strong>Date:</strong> {{bookingDate}}</li>
                        <li><strong>Heure:</strong> {{bookingTime}}</li>
                        <li><strong>Prestataire:</strong> {{providerName}}</li>
                        <li><strong>Prix:</strong> {{price}} €</li>
                    </ul>
                    <p>Le prestataire vous contactera prochainement pour finaliser les détails.</p>
                    <p>Cordialement,<br>L'équipe ServiceLink</p>
                ",
                Variables = new List<string> { "userName", "bookingId", "serviceName", "bookingDate", "bookingTime", "providerName", "price" },
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Version = 1
            };

            // Template SMS de rappel
            var smsReminderTemplate = new NotificationTemplate
            {
                Id = Guid.NewGuid(),
                Name = "booking_reminder",
                Type = ExternalNotificationType.SMS,
                Language = "fr",
                Subject = "Rappel de réservation",
                Content = "Rappel: Votre RDV {{serviceName}} avec {{providerName}} est prévu demain à {{bookingTime}}. ServiceLink",
                Variables = new List<string> { "serviceName", "providerName", "bookingTime" },
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Version = 1
            };

            // Template push notification
            var pushNotificationTemplate = new NotificationTemplate
            {
                Id = Guid.NewGuid(),
                Name = "new_message",
                Type = ExternalNotificationType.Push,
                Language = "fr",
                Subject = "Nouveau message",
                Content = "{{senderName}} vous a envoyé un message: {{messagePreview}}",
                Variables = new List<string> { "senderName", "messagePreview" },
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Version = 1
            };

            // Ajout des templates au dictionnaire
            _templates[$"{welcomeTemplate.Name}_{welcomeTemplate.Language}"] = welcomeTemplate;
            _templates[$"{bookingConfirmationTemplate.Name}_{bookingConfirmationTemplate.Language}"] = bookingConfirmationTemplate;
            _templates[$"{smsReminderTemplate.Name}_{smsReminderTemplate.Language}"] = smsReminderTemplate;
            _templates[$"{pushNotificationTemplate.Name}_{pushNotificationTemplate.Language}"] = pushNotificationTemplate;

            _logger.LogInformation("Templates par défaut initialisés: {Count} templates chargés", _templates.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initialisation des templates par défaut");
        }
    }

    /// <summary>
    /// Remplace les variables système dans le contenu
    /// </summary>
    /// <param name="content">Contenu à traiter</param>
    /// <returns>Contenu avec variables système remplacées</returns>
    private static string ReplaceSystemVariables(string content)
    {
        var now = DateTime.Now;
        
        content = content.Replace("{{currentDate}}", now.ToString("dd/MM/yyyy"));
        content = content.Replace("{{currentTime}}", now.ToString("HH:mm"));
        content = content.Replace("{{currentYear}}", now.Year.ToString());
        content = content.Replace("{{platformName}}", "ServiceLink");
        content = content.Replace("{{supportEmail}}", "<EMAIL>");
        content = content.Replace("{{websiteUrl}}", "https://servicelink.com");

        return content;
    }

    /// <summary>
    /// Extrait les variables du contenu d'un template
    /// </summary>
    /// <param name="content">Contenu du template</param>
    /// <returns>Liste des variables trouvées</returns>
    private static List<string> ExtractVariablesFromContent(string content)
    {
        var variables = new List<string>();
        
        if (string.IsNullOrEmpty(content))
            return variables;

        var regex = new Regex(@"\{\{(\w+)\}\}", RegexOptions.IgnoreCase);
        var matches = regex.Matches(content);

        foreach (Match match in matches)
        {
            if (match.Groups.Count > 1)
            {
                var variableName = match.Groups[1].Value;
                if (!variables.Contains(variableName))
                {
                    variables.Add(variableName);
                }
            }
        }

        return variables;
    }

    #endregion
}
