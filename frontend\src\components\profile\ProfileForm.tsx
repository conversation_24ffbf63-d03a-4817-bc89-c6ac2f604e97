import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { User, Mail, Save, Loader2 } from 'lucide-react'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { useAuth } from '../../contexts/AuthContext'
import {type UpdateProfileRequest } from '../../types/auth'

// Schéma de validation pour le profil
const profileSchema = z.object({
  firstName: z
    .string()
    .min(1, 'Le prénom est requis')
    .min(2, 'Le prénom doit contenir au moins 2 caractères')
    .max(50, 'Le prénom ne peut pas dépasser 50 caractères'),
  lastName: z
    .string()
    .min(1, 'Le nom est requis')
    .min(2, 'Le nom doit contenir au moins 2 caractères')
    .max(50, 'Le nom ne peut pas dépasser 50 caractères'),
  email: z
    .string()
    .min(1, 'L\'email est requis')
    .email('Format d\'email invalide'),
})

type ProfileFormData = z.infer<typeof profileSchema>

interface ProfileFormProps {
  onSuccess?: () => void
}

export const ProfileForm: React.FC<ProfileFormProps> = ({ onSuccess }) => {
  const { user, updateProfile, isLoading } = useAuth()

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    setError,
    reset,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
    },
  })

  // Réinitialiser le formulaire quand l'utilisateur change
  React.useEffect(() => {
    if (user) {
      reset({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
      })
    }
  }, [user, reset])

  const onSubmit = async (data: ProfileFormData) => {
    try {
      await updateProfile(data as UpdateProfileRequest)
      onSuccess?.()
    } catch (error: any) {
      // Gérer les erreurs spécifiques
      if (error.statusCode === 409) {
        setError('email', {
          message: 'Cette adresse email est déjà utilisée',
        })
      } else if (error.errors) {
        // Erreurs de validation du serveur
        Object.entries(error.errors).forEach(([field, messages]) => {
          if (Array.isArray(messages) && messages.length > 0) {
            setError(field as keyof ProfileFormData, {
              message: messages[0],
            })
          }
        })
      } else {
        setError('root', {
          message: error.message || 'Une erreur est survenue lors de la mise à jour',
        })
      }
    }
  }

  const handleReset = () => {
    if (user) {
      reset({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
      })
    }
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Mon Profil</h2>
          <p className="text-gray-600">
            Gérez vos informations personnelles
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Prénom"
              placeholder="Votre prénom"
              leftIcon={<User className="h-4 w-4" />}
              error={errors.firstName?.message}
              {...register('firstName')}
            />

            <Input
              label="Nom"
              placeholder="Votre nom"
              leftIcon={<User className="h-4 w-4" />}
              error={errors.lastName?.message}
              {...register('lastName')}
            />
          </div>

          <Input
            label="Email"
            type="email"
            placeholder="<EMAIL>"
            leftIcon={<Mail className="h-4 w-4" />}
            error={errors.email?.message}
            helperText="Votre adresse email sera utilisée pour la connexion"
            {...register('email')}
          />

          {/* Informations en lecture seule */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">
              Informations du compte
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Rôle :</span>
                <span className="ml-2 font-medium text-gray-900">
                  {user?.role}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Email confirmé :</span>
                <span className={`ml-2 font-medium ${
                  user?.isEmailConfirmed ? 'text-green-600' : 'text-red-600'
                }`}>
                  {user?.isEmailConfirmed ? 'Oui' : 'Non'}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Membre depuis :</span>
                <span className="ml-2 font-medium text-gray-900">
                  {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('fr-FR') : '-'}
                </span>
              </div>
            </div>
          </div>

          {errors.root && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{errors.root.message}</p>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="submit"
              className="flex-1"
              isLoading={isLoading}
              disabled={isLoading || !isDirty}
              leftIcon={isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
            >
              {isLoading ? 'Mise à jour...' : 'Sauvegarder'}
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              disabled={isLoading || !isDirty}
              className="flex-1 sm:flex-none"
            >
              Annuler
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
