using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Interfaces;
using ServiceLink.Application.Services;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour tester et gérer le cache Redis
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize] // Nécessite une authentification
public class CacheController : ControllerBase
{
    private readonly ICacheService _cacheService;
    private readonly ICachedUserService _cachedUserService;
    private readonly ILogger<CacheController> _logger;

    public CacheController(
        ICacheService cacheService,
        ICachedUserService cachedUserService,
        ILogger<CacheController> logger)
    {
        _cacheService = cacheService;
        _cachedUserService = cachedUserService;
        _logger = logger;
    }

    /// <summary>
    /// Obtient les statistiques du cache Redis
    /// </summary>
    /// <returns>Statistiques détaillées du cache</returns>
    [HttpGet("statistics")]
    [Authorize(Roles = "AdminGlobal,Manager")]
    public async Task<ActionResult<CacheStatistics>> GetCacheStatistics()
    {
        try
        {
            _logger.LogInformation("Récupération des statistiques du cache Redis");
            
            var statistics = await _cacheService.GetStatisticsAsync();
            
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques du cache");
            return StatusCode(500, new { message = "Erreur lors de la récupération des statistiques du cache" });
        }
    }

    /// <summary>
    /// Teste la connectivité Redis avec une opération simple
    /// </summary>
    /// <returns>Résultat du test de connectivité</returns>
    [HttpGet("health")]
    [AllowAnonymous] // Accessible sans authentification pour les health checks
    public async Task<ActionResult<object>> TestRedisHealth()
    {
        try
        {
            _logger.LogDebug("Test de connectivité Redis");
            
            var testKey = "health_check_test";
            var testValue = new { timestamp = DateTime.UtcNow, message = "Redis health check" };
            
            // Test d'écriture
            await _cacheService.SetAsync(testKey, testValue, TimeSpan.FromMinutes(1));
            
            // Test de lecture
            var retrievedValue = await _cacheService.GetAsync<object>(testKey);
            
            // Test de suppression
            await _cacheService.RemoveAsync(testKey);
            
            var result = new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                operations = new
                {
                    write = "success",
                    read = retrievedValue != null ? "success" : "failed",
                    delete = "success"
                }
            };
            
            _logger.LogInformation("Test de connectivité Redis réussi");
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Échec du test de connectivité Redis");
            
            var result = new
            {
                status = "unhealthy",
                timestamp = DateTime.UtcNow,
                error = ex.Message
            };
            
            return StatusCode(503, result);
        }
    }

    /// <summary>
    /// Vide le cache pour une clé spécifique
    /// </summary>
    /// <param name="key">Clé à supprimer du cache</param>
    /// <returns>Résultat de l'opération</returns>
    [HttpDelete("clear/{key}")]
    [Authorize(Roles = "AdminGlobal,Manager")]
    public async Task<ActionResult> ClearCacheKey(string key)
    {
        try
        {
            _logger.LogInformation("Suppression de la clé de cache: {Key}", key);
            
            await _cacheService.RemoveAsync(key);
            
            return Ok(new { message = $"Clé '{key}' supprimée du cache avec succès" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la clé: {Key}", key);
            return StatusCode(500, new { message = "Erreur lors de la suppression de la clé du cache" });
        }
    }

    /// <summary>
    /// Vide le cache par pattern (ex: "user:*")
    /// </summary>
    /// <param name="pattern">Pattern des clés à supprimer</param>
    /// <returns>Résultat de l'opération</returns>
    [HttpDelete("clear-pattern/{pattern}")]
    [Authorize(Roles = "AdminGlobal")]
    public async Task<ActionResult> ClearCacheByPattern(string pattern)
    {
        try
        {
            _logger.LogInformation("Suppression des clés par pattern: {Pattern}", pattern);
            
            await _cacheService.RemoveByPatternAsync(pattern);
            
            return Ok(new { message = $"Clés correspondant au pattern '{pattern}' supprimées du cache avec succès" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression par pattern: {Pattern}", pattern);
            return StatusCode(500, new { message = "Erreur lors de la suppression des clés du cache" });
        }
    }

    /// <summary>
    /// Précharge les profils utilisateurs les plus consultés
    /// </summary>
    /// <param name="userIds">Liste des IDs utilisateurs à précharger</param>
    /// <returns>Résultat de l'opération</returns>
    [HttpPost("warmup/users")]
    [Authorize(Roles = "AdminGlobal,Manager")]
    public async Task<ActionResult> WarmupUserProfiles([FromBody] List<Guid> userIds)
    {
        try
        {
            _logger.LogInformation("Préchargement de {Count} profils utilisateurs", userIds.Count);
            
            if (userIds.Count > 100)
            {
                return BadRequest(new { message = "Maximum 100 utilisateurs peuvent être préchargés à la fois" });
            }
            
            await _cachedUserService.WarmupUserProfilesAsync(userIds);
            
            return Ok(new { 
                message = $"{userIds.Count} profils utilisateurs préchargés avec succès",
                userIds = userIds
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du préchargement des profils utilisateurs");
            return StatusCode(500, new { message = "Erreur lors du préchargement des profils utilisateurs" });
        }
    }

    /// <summary>
    /// Vérifie si une clé existe dans le cache
    /// </summary>
    /// <param name="key">Clé à vérifier</param>
    /// <returns>True si la clé existe, false sinon</returns>
    [HttpGet("exists/{key}")]
    [Authorize(Roles = "AdminGlobal,Manager")]
    public async Task<ActionResult<object>> CheckKeyExists(string key)
    {
        try
        {
            _logger.LogDebug("Vérification de l'existence de la clé: {Key}", key);
            
            var exists = await _cacheService.ExistsAsync(key);
            
            return Ok(new { 
                key = key,
                exists = exists,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de la clé: {Key}", key);
            return StatusCode(500, new { message = "Erreur lors de la vérification de la clé" });
        }
    }

    /// <summary>
    /// Obtient des informations détaillées sur l'utilisation du cache
    /// </summary>
    /// <returns>Informations détaillées sur le cache</returns>
    [HttpGet("info")]
    [Authorize(Roles = "AdminGlobal")]
    public async Task<ActionResult<object>> GetCacheInfo()
    {
        try
        {
            _logger.LogInformation("Récupération des informations détaillées du cache");
            
            var statistics = await _cacheService.GetStatisticsAsync();
            
            var info = new
            {
                redis = new
                {
                    totalKeys = statistics.TotalKeys,
                    usedMemory = statistics.UsedMemory,
                    usedMemoryHuman = FormatBytes(statistics.UsedMemory),
                    connectedClients = statistics.ConnectedClients,
                    totalCommandsProcessed = statistics.TotalCommandsProcessed,
                    hitRatio = statistics.HitRatio,
                    uptimeInSeconds = statistics.UptimeInSeconds,
                    uptimeHuman = TimeSpan.FromSeconds(statistics.UptimeInSeconds).ToString(@"dd\.hh\:mm\:ss")
                },
                cacheKeys = new
                {
                    userPrefix = CacheKeys.USER_PREFIX,
                    servicePrefix = CacheKeys.SERVICE_PREFIX,
                    bookingPrefix = CacheKeys.BOOKING_PREFIX,
                    categoryPrefix = CacheKeys.CATEGORY_PREFIX,
                    searchPrefix = CacheKeys.SEARCH_PREFIX,
                    statsPrefix = CacheKeys.STATS_PREFIX
                },
                cacheTtl = new
                {
                    searchResults = CacheTTL.SearchResults.TotalMinutes + " minutes",
                    userProfiles = CacheTTL.UserProfiles.TotalMinutes + " minutes",
                    serviceCategories = CacheTTL.ServiceCategories.TotalHours + " hours",
                    dashboardStats = CacheTTL.DashboardStats.TotalHours + " hours",
                    configuration = CacheTTL.Configuration.TotalHours + " hours",
                    defaultTtl = CacheTTL.Default.TotalMinutes + " minutes"
                },
                timestamp = DateTime.UtcNow
            };
            
            return Ok(info);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des informations du cache");
            return StatusCode(500, new { message = "Erreur lors de la récupération des informations du cache" });
        }
    }

    /// <summary>
    /// Formate les bytes en format lisible
    /// </summary>
    /// <param name="bytes">Nombre de bytes</param>
    /// <returns>Taille formatée</returns>
    private static string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = bytes;
        
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        
        return $"{number:n1} {suffixes[counter]}";
    }
}
