# Dockerfile pour le développement ServiceLink API
# Optimisé pour le développement avec hot reload et debugging

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS development
WORKDIR /app

# Installer les outils de développement
RUN dotnet tool install --global dotnet-ef
RUN dotnet tool install --global dotnet-watch
ENV PATH="$PATH:/root/.dotnet/tools"

# Copier les fichiers de projet pour la restauration des packages
COPY ["src/ServiceLink.API/ServiceLink.API.csproj", "src/ServiceLink.API/"]
COPY ["src/ServiceLink.Application/ServiceLink.Application.csproj", "src/ServiceLink.Application/"]
COPY ["src/ServiceLink.Domain/ServiceLink.Domain.csproj", "src/ServiceLink.Domain/"]
COPY ["src/ServiceLink.Infrastructure/ServiceLink.Infrastructure.csproj", "src/ServiceLink.Infrastructure/"]

# Restaurer les dépendances
RUN dotnet restore "src/ServiceLink.API/ServiceLink.API.csproj"

# Copier tout le code source
COPY . .

# Exposer les ports pour HTTP et HTTPS
EXPOSE 8080
EXPOSE 8081

# Variables d'environnement pour le développement
ENV ASPNETCORE_ENVIRONMENT=Development
ENV ASPNETCORE_URLS=http://+:8080;https://+:8081
ENV DOTNET_USE_POLLING_FILE_WATCHER=true
ENV DOTNET_RUNNING_IN_CONTAINER=true

# Créer le répertoire pour les certificats HTTPS
RUN mkdir -p /https

# Point d'entrée pour le développement avec hot reload
ENTRYPOINT ["dotnet", "watch", "run", "--project", "src/ServiceLink.API/ServiceLink.API.csproj", "--urls", "http://+:8080;https://+:8081"]

# Healthcheck pour le développement
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1
