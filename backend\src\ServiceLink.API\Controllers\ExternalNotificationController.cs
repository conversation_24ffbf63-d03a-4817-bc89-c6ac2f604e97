using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Interfaces;
using ServiceLink.Infrastructure.Services;
using System.Security.Claims;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour gérer les notifications externes (Email, SMS, Push)
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ExternalNotificationController : ControllerBase
{
    private readonly IExternalNotificationServiceFactory _notificationFactory;
    private readonly INotificationTemplateService _templateService;
    private readonly ILogger<ExternalNotificationController> _logger;

    public ExternalNotificationController(
        IExternalNotificationServiceFactory notificationFactory,
        INotificationTemplateService templateService,
        ILogger<ExternalNotificationController> logger)
    {
        _notificationFactory = notificationFactory;
        _templateService = templateService;
        _logger = logger;
    }

    /// <summary>
    /// Envoie une notification externe
    /// </summary>
    /// <param name="request">Détails de la notification</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de l'envoi</returns>
    [HttpPost("send")]
    public async Task<IActionResult> SendNotification(
        [FromBody] ExternalNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Demande d'envoi de notification {Type} vers {Recipients}", 
                request.Type, string.Join(", ", request.Recipients));

            var result = await _notificationFactory.SendNotificationAsync(request, cancellationToken);

            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    data = result
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = result.ErrorMessage,
                    data = result
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification");
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Envoie des notifications en lot
    /// </summary>
    /// <param name="requests">Liste des notifications à envoyer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultats des envois</returns>
    [HttpPost("send-bulk")]
    public async Task<IActionResult> SendBulkNotifications(
        [FromBody] List<ExternalNotificationRequest> requests,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Demande d'envoi en lot de {Count} notifications", requests.Count);

            var results = new List<ExternalNotificationResult>();

            // Grouper par type de notification pour optimiser l'envoi
            var groupedRequests = requests.GroupBy(r => r.Type);

            foreach (var group in groupedRequests)
            {
                var service = _notificationFactory.CreateNotificationService(group.Key);
                var groupResults = await service.SendBulkNotificationsAsync(group, cancellationToken);
                results.AddRange(groupResults);
            }

            var successCount = results.Count(r => r.Success);
            var totalCount = results.Count;

            return Ok(new
            {
                success = true,
                data = new
                {
                    results = results,
                    summary = new
                    {
                        total = totalCount,
                        successful = successCount,
                        failed = totalCount - successCount,
                        successRate = totalCount > 0 ? (decimal)successCount / totalCount * 100 : 0
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi en lot de notifications");
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Vérifie le statut d'une notification
    /// </summary>
    /// <param name="type">Type de notification</param>
    /// <param name="notificationId">ID de la notification</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Statut de la notification</returns>
    [HttpGet("status/{type}/{notificationId}")]
    public async Task<IActionResult> GetNotificationStatus(
        [FromRoute] string type,
        [FromRoute] string notificationId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!Enum.TryParse<ExternalNotificationType>(type, true, out var notificationType))
            {
                return BadRequest(new { success = false, message = "Type de notification invalide" });
            }

            var service = _notificationFactory.CreateNotificationService(notificationType);
            var status = await service.GetNotificationStatusAsync(notificationId, cancellationToken);

            return Ok(new
            {
                success = true,
                data = new
                {
                    notificationId = notificationId,
                    type = type,
                    status = status.ToString()
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification du statut de notification {NotificationId}", notificationId);
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Obtient les services de notification disponibles
    /// </summary>
    /// <returns>Liste des services disponibles</returns>
    [HttpGet("services")]
    public IActionResult GetAvailableServices()
    {
        try
        {
            var services = _notificationFactory.GetAvailableServices();
            var serviceInfo = services.Select(s => new
            {
                type = s.ServiceType.ToString(),
                name = s.GetType().Name,
                available = true
            });

            return Ok(new
            {
                success = true,
                data = serviceInfo
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des services disponibles");
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Valide une adresse de destination
    /// </summary>
    /// <param name="type">Type de notification</param>
    /// <param name="destination">Adresse à valider</param>
    /// <returns>Résultat de la validation</returns>
    [HttpPost("validate")]
    public IActionResult ValidateDestination(
        [FromQuery] string type,
        [FromBody] string destination)
    {
        try
        {
            if (!Enum.TryParse<ExternalNotificationType>(type, true, out var notificationType))
            {
                return BadRequest(new { success = false, message = "Type de notification invalide" });
            }

            var service = _notificationFactory.CreateNotificationService(notificationType);
            var isValid = service.ValidateDestination(destination);

            return Ok(new
            {
                success = true,
                data = new
                {
                    destination = destination,
                    type = type,
                    isValid = isValid
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la validation de destination");
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Obtient tous les templates disponibles
    /// </summary>
    /// <param name="language">Langue des templates (optionnel)</param>
    /// <returns>Liste des templates</returns>
    [HttpGet("templates")]
    public IActionResult GetTemplates([FromQuery] string? language = null)
    {
        try
        {
            var templateService = _templateService as NotificationTemplateService;
            var templates = templateService?.GetAllTemplates() ?? new List<NotificationTemplate>();

            if (!string.IsNullOrEmpty(language))
            {
                templates = templates.Where(t => t.Language.Equals(language, StringComparison.OrdinalIgnoreCase));
            }

            var templateInfo = templates.Select(t => new
            {
                id = t.Id,
                name = t.Name,
                type = t.Type.ToString(),
                language = t.Language,
                subject = t.Subject,
                variables = t.Variables,
                isActive = t.IsActive,
                version = t.Version,
                createdAt = t.CreatedAt,
                updatedAt = t.UpdatedAt
            });

            return Ok(new
            {
                success = true,
                data = templateInfo
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des templates");
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Obtient un template spécifique
    /// </summary>
    /// <param name="templateName">Nom du template</param>
    /// <param name="language">Langue du template</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Template demandé</returns>
    [HttpGet("templates/{templateName}")]
    public async Task<IActionResult> GetTemplate(
        [FromRoute] string templateName,
        [FromQuery] string language = "fr",
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await _templateService.GetTemplateAsync(templateName, language);

            if (template == null)
            {
                return NotFound(new { success = false, message = "Template non trouvé" });
            }

            return Ok(new
            {
                success = true,
                data = template
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du template {TemplateName}", templateName);
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Crée ou met à jour un template
    /// </summary>
    /// <param name="template">Template à sauvegarder</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Template sauvegardé</returns>
    [HttpPost("templates")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> SaveTemplate(
        [FromBody] NotificationTemplate template,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validation du template
            var templateService = _templateService as NotificationTemplateService;
            var validationErrors = templateService?.ValidateTemplate(template) ?? new List<string>();

            if (validationErrors.Any())
            {
                return BadRequest(new
                {
                    success = false,
                    message = "Erreurs de validation",
                    errors = validationErrors
                });
            }

            var savedTemplate = await _templateService.SaveTemplateAsync(template, cancellationToken);

            return Ok(new
            {
                success = true,
                data = savedTemplate
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la sauvegarde du template {TemplateName}", template.Name);
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Teste un template avec des données d'exemple
    /// </summary>
    /// <param name="templateName">Nom du template</param>
    /// <param name="testData">Données de test</param>
    /// <param name="language">Langue du template</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Contenu rendu</returns>
    [HttpPost("templates/{templateName}/test")]
    public async Task<IActionResult> TestTemplate(
        [FromRoute] string templateName,
        [FromBody] Dictionary<string, object> testData,
        [FromQuery] string language = "fr",
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await _templateService.GetTemplateAsync(templateName, language);

            if (template == null)
            {
                return NotFound(new { success = false, message = "Template non trouvé" });
            }

            var renderedContent = _templateService.RenderTemplate(template, testData);
            var subjectTemplate = new NotificationTemplate
            {
                Id = template.Id,
                Name = template.Name,
                Type = template.Type,
                Language = template.Language,
                Subject = template.Subject,
                Content = template.Subject,
                Variables = template.Variables,
                IsActive = template.IsActive,
                CreatedAt = template.CreatedAt,
                UpdatedAt = template.UpdatedAt,
                Version = template.Version
            };
            var renderedSubject = _templateService.RenderTemplate(subjectTemplate, testData);

            return Ok(new
            {
                success = true,
                data = new
                {
                    templateName = templateName,
                    language = language,
                    renderedSubject = renderedSubject,
                    renderedContent = renderedContent,
                    testData = testData
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du test du template {TemplateName}", templateName);
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    #region Méthodes privées

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Utilisateur non authentifié");
        }
        return userId;
    }

    #endregion
}
