using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ServiceLink.Application.Services.Queries;
using ServiceLink.Application.Services.Commands;
using ServiceLink.Application.Common.Models;
using ServiceLink.Application.DTOs;
using MediatR;

namespace ServiceLink.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ServicesController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<ServicesController> _logger;

    public ServicesController(IMediator mediator, ILogger<ServicesController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Obtenir toutes les catégories de services
    /// </summary>
    [HttpGet("categories")]
    public async Task<ActionResult<List<ServiceCategoryDto>>> GetCategories()
    {
        try
        {
            var query = new GetServiceCategoriesQuery();
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des catégories");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir une catégorie par ID
    /// </summary>
    [HttpGet("categories/{id}")]
    public async Task<ActionResult<ServiceCategoryDto>> GetCategoryById(Guid id)
    {
        try
        {
            var query = new GetServiceCategoryByIdQuery { Id = id };
            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound($"Catégorie avec l'ID {id} non trouvée");
                
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la catégorie {CategoryId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Rechercher des services avec filtres et pagination
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PaginatedResult<ServiceDto>>> SearchServices(
        [FromQuery] SearchServicesQuery query)
    {
        try
        {
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de services");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir un service par ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ServiceDto>> GetServiceById(Guid id)
    {
        try
        {
            var query = new GetServiceByIdQuery { Id = id };
            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound($"Service avec l'ID {id} non trouvé");
                
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du service {ServiceId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les services populaires
    /// </summary>
    [HttpGet("popular")]
    public async Task<ActionResult<List<ServiceDto>>> GetPopularServices([FromQuery] int limit = 6)
    {
        try
        {
            var query = new GetPopularServicesQuery { Limit = limit };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des services populaires");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les services en vedette
    /// </summary>
    [HttpGet("featured")]
    public async Task<ActionResult<List<ServiceDto>>> GetFeaturedServices([FromQuery] int limit = 8)
    {
        try
        {
            var query = new GetFeaturedServicesQuery { Limit = limit };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des services en vedette");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les statistiques des services
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<ServiceStatsDto>> GetServiceStats()
    {
        try
        {
            var query = new GetServiceStatsQuery();
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les suggestions de recherche
    /// </summary>
    [HttpGet("suggestions")]
    public async Task<ActionResult<List<string>>> GetSearchSuggestions([FromQuery] string q)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(q))
                return BadRequest("Le paramètre de recherche 'q' est requis");

            var query = new GetSearchSuggestionsQuery { SearchTerm = q };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des suggestions");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les services à proximité
    /// </summary>
    [HttpGet("nearby")]
    public async Task<ActionResult<List<ServiceDto>>> GetNearbyServices(
        [FromQuery] double lat,
        [FromQuery] double lng,
        [FromQuery] int radius = 10,
        [FromQuery] int limit = 10)
    {
        try
        {
            var query = new GetNearbyServicesQuery 
            { 
                Latitude = lat, 
                Longitude = lng, 
                Radius = radius, 
                Limit = limit 
            };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des services à proximité");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Créer un nouveau service (pour les prestataires)
    /// </summary>
    [HttpPost]
    [Authorize]
    public async Task<ActionResult<ServiceDto>> CreateService([FromBody] CreateServiceCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetServiceById), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du service");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Mettre à jour un service
    /// </summary>
    [HttpPut("{id}")]
    [Authorize]
    public async Task<ActionResult<ServiceDto>> UpdateService(Guid id, [FromBody] UpdateServiceCommand command)
    {
        try
        {
            command.Id = id;
            var result = await _mediator.Send(command);
            
            if (result == null)
                return NotFound($"Service avec l'ID {id} non trouvé");
                
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du service {ServiceId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Supprimer un service
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize]
    public async Task<ActionResult> DeleteService(Guid id)
    {
        try
        {
            var command = new DeleteServiceCommand { Id = id };
            await _mediator.Send(command);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du service {ServiceId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Marquer/Démarquer un service comme favori
    /// </summary>
    [HttpPost("{id}/favorite")]
    [Authorize]
    public async Task<ActionResult<object>> ToggleFavorite(Guid id)
    {
        try
        {
            var command = new ToggleFavoriteServiceCommand { ServiceId = id };
            var result = await _mediator.Send(command);
            return Ok(new { isFavorite = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du toggle favori pour le service {ServiceId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les services favoris de l'utilisateur
    /// </summary>
    [HttpGet("favorites")]
    [Authorize]
    public async Task<ActionResult<List<ServiceDto>>> GetFavoriteServices()
    {
        try
        {
            var query = new GetFavoriteServicesQuery();
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des services favoris");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}
