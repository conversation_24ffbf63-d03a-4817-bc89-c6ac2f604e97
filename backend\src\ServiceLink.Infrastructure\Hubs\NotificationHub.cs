using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Infrastructure.Hubs;

/// <summary>
/// Hub SignalR pour les notifications générales
/// Gère les notifications système, alertes et messages informatifs
/// </summary>
[Authorize]
public class NotificationHub : BaseHub
{
    public NotificationHub(ILogger<NotificationHub> logger, ICacheService cacheService) 
        : base(logger, cacheService)
    {
    }

    /// <summary>
    /// Gère la connexion d'un utilisateur au hub de notifications
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        await ExecuteWithErrorHandlingAsync(nameof(OnConnectedAsync), async () =>
        {
            var userId = GetUserId();
            var userRole = GetUserRole();
            var userName = GetUserName();

            // Ajouter l'utilisateur au groupe correspondant à son rôle
            var roleGroup = SignalRGroups.GetRoleGroup(userRole);
            await Groups.AddToGroupAsync(Context.ConnectionId, roleGroup);

            _logger.LogInformation("Utilisateur {UserName} ({UserId}) connecté au NotificationHub avec le rôle {Role}",
                userName, userId, userRole);

            // Notifier les administrateurs de la connexion (sauf si c'est un admin lui-même)
            if (userRole != UserRole.Admin)
            {
                await Clients.Group(SignalRGroups.Administrators).SendAsync("UserConnected", new
                {
                    UserId = userId,
                    UserName = userName,
                    Role = userRole.ToString(),
                    ConnectedAt = DateTime.UtcNow
                });
            }

            // Envoyer un message de bienvenue personnalisé
            await Clients.Caller.SendAsync("Welcome", new
            {
                Message = $"Bienvenue {userName} ! Vous êtes connecté aux notifications en temps réel.",
                Role = userRole.ToString(),
                ConnectedAt = DateTime.UtcNow
            });

            await base.OnConnectedAsync();
        });
    }

    /// <summary>
    /// Gère la déconnexion d'un utilisateur du hub de notifications
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        await ExecuteWithErrorHandlingAsync(nameof(OnDisconnectedAsync), async () =>
        {
            var userId = GetUserId();
            var userRole = GetUserRole();
            var userName = GetUserName();

            // Notifier les administrateurs de la déconnexion (sauf si c'est un admin lui-même)
            if (userRole != UserRole.Admin)
            {
                await Clients.Group(SignalRGroups.Administrators).SendAsync("UserDisconnected", new
                {
                    UserId = userId,
                    UserName = userName,
                    Role = userRole.ToString(),
                    DisconnectedAt = DateTime.UtcNow,
                    Reason = exception?.Message
                });
            }

            _logger.LogInformation("Utilisateur {UserName} ({UserId}) déconnecté du NotificationHub",
                userName, userId);

            await base.OnDisconnectedAsync(exception);
        });
    }

    /// <summary>
    /// Permet à un utilisateur de rejoindre un groupe personnalisé
    /// </summary>
    /// <param name="groupName">Nom du groupe à rejoindre</param>
    [HubMethodName("JoinGroup")]
    public async Task JoinGroupAsync(string groupName)
    {
        await ExecuteWithErrorHandlingAsync(nameof(JoinGroupAsync), async () =>
        {
            ValidateInput(groupName, nameof(groupName));

            var userId = GetUserId();
            var userRole = GetUserRole();

            // Vérifier les permissions pour rejoindre certains groupes
            if (!CanJoinGroup(groupName, userRole))
            {
                await SendErrorAsync(nameof(JoinGroupAsync), $"Vous n'avez pas l'autorisation de rejoindre le groupe {groupName}");
                return;
            }

            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

            _logger.LogInformation("Utilisateur {UserId} a rejoint le groupe {GroupName}", userId, groupName);

            await SendSuccessAsync(nameof(JoinGroupAsync), $"Vous avez rejoint le groupe {groupName}");
        });
    }

    /// <summary>
    /// Permet à un utilisateur de quitter un groupe personnalisé
    /// </summary>
    /// <param name="groupName">Nom du groupe à quitter</param>
    [HubMethodName("LeaveGroup")]
    public async Task LeaveGroupAsync(string groupName)
    {
        await ExecuteWithErrorHandlingAsync(nameof(LeaveGroupAsync), async () =>
        {
            ValidateInput(groupName, nameof(groupName));

            var userId = GetUserId();

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

            _logger.LogInformation("Utilisateur {UserId} a quitté le groupe {GroupName}", userId, groupName);

            await SendSuccessAsync(nameof(LeaveGroupAsync), $"Vous avez quitté le groupe {groupName}");
        });
    }

    /// <summary>
    /// Envoie une notification personnelle à un utilisateur spécifique (admin seulement)
    /// </summary>
    /// <param name="targetUserId">ID de l'utilisateur destinataire</param>
    /// <param name="title">Titre de la notification</param>
    /// <param name="message">Message de la notification</param>
    /// <param name="priority">Priorité de la notification</param>
    [HubMethodName("SendPersonalNotification")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task SendPersonalNotificationAsync(Guid targetUserId, string title, string message, NotificationPriority priority = NotificationPriority.Normal)
    {
        await ExecuteWithErrorHandlingAsync(nameof(SendPersonalNotificationAsync), async () =>
        {
            ValidateInput(targetUserId, nameof(targetUserId));
            ValidateInput(title, nameof(title));
            ValidateInput(message, nameof(message));

            var senderId = GetUserId();
            var senderName = GetUserName();

            var notification = new NotificationDto
            {
                Type = "PersonalNotification",
                Title = title,
                Message = message,
                Priority = priority,
                Data = new
                {
                    SenderId = senderId,
                    SenderName = senderName
                }
            };

            // Envoyer à l'utilisateur spécifique
            await Clients.User(targetUserId.ToString()).SendAsync("ReceiveNotification", notification);

            _logger.LogInformation("Notification personnelle envoyée de {SenderId} vers {TargetUserId}: {Title}",
                senderId, targetUserId, title);

            await SendSuccessAsync(nameof(SendPersonalNotificationAsync), "Notification envoyée avec succès");
        });
    }

    /// <summary>
    /// Diffuse une annonce à tous les utilisateurs d'un rôle (admin seulement)
    /// </summary>
    /// <param name="targetRole">Rôle des utilisateurs destinataires</param>
    /// <param name="title">Titre de l'annonce</param>
    /// <param name="message">Message de l'annonce</param>
    /// <param name="priority">Priorité de l'annonce</param>
    [HubMethodName("BroadcastToRole")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task BroadcastToRoleAsync(UserRole targetRole, string title, string message, NotificationPriority priority = NotificationPriority.Normal)
    {
        await ExecuteWithErrorHandlingAsync(nameof(BroadcastToRoleAsync), async () =>
        {
            ValidateInput(title, nameof(title));
            ValidateInput(message, nameof(message));

            var senderId = GetUserId();
            var senderName = GetUserName();

            var announcement = new NotificationDto
            {
                Type = "RoleAnnouncement",
                Title = title,
                Message = message,
                Priority = priority,
                Data = new
                {
                    SenderId = senderId,
                    SenderName = senderName,
                    TargetRole = targetRole.ToString()
                }
            };

            var roleGroup = SignalRGroups.GetRoleGroup(targetRole);
            await Clients.Group(roleGroup).SendAsync("ReceiveNotification", announcement);

            _logger.LogInformation("Annonce diffusée par {SenderId} au rôle {TargetRole}: {Title}",
                senderId, targetRole, title);

            await SendSuccessAsync(nameof(BroadcastToRoleAsync), $"Annonce diffusée au rôle {targetRole}");
        });
    }

    /// <summary>
    /// Obtient la liste des utilisateurs connectés (admin seulement)
    /// </summary>
    [HubMethodName("GetConnectedUsers")]
    [Authorize(Roles = "Admin,Manager,Support")]
    public async Task GetConnectedUsersAsync()
    {
        await ExecuteWithErrorHandlingAsync(nameof(GetConnectedUsersAsync), async () =>
        {
            // Cette méthode nécessiterait une implémentation plus complexe
            // pour tracker toutes les connexions actives
            var connectedUsers = new List<ConnectedUserDto>();

            // Pour l'instant, on retourne une liste vide
            // Dans une implémentation complète, on utiliserait Redis ou une base de données
            // pour tracker les connexions actives

            await Clients.Caller.SendAsync("ConnectedUsersList", connectedUsers);
        });
    }

    /// <summary>
    /// Ping pour vérifier la connectivité
    /// </summary>
    [HubMethodName("Ping")]
    public async Task PingAsync()
    {
        await Clients.Caller.SendAsync("Pong", new
        {
            Timestamp = DateTime.UtcNow,
            ConnectionId = Context.ConnectionId,
            UserId = GetUserId()
        });
    }

    /// <summary>
    /// Vérifie si un utilisateur peut rejoindre un groupe spécifique
    /// </summary>
    private static bool CanJoinGroup(string groupName, UserRole userRole)
    {
        // Règles de sécurité pour les groupes
        return groupName switch
        {
            SignalRGroups.Administrators => userRole == UserRole.Admin,
            SignalRGroups.Managers => userRole is UserRole.Admin or UserRole.Manager,
            SignalRGroups.Support => userRole is UserRole.Admin or UserRole.Manager or UserRole.Support,
            SignalRGroups.Supervisors => userRole is UserRole.Admin or UserRole.Manager or UserRole.Supervisor,
            _ => true // Autres groupes accessibles à tous
        };
    }
}
