using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Infrastructure.Hubs;

/// <summary>
/// Hub SignalR pour la messagerie temps réel
/// Gère les conversations entre clients et prestataires
/// </summary>
[Authorize]
public class ChatHub : BaseHub
{
    public ChatHub(ILogger<ChatHub> logger, ICacheService cacheService) 
        : base(logger, cacheService)
    {
    }

    /// <summary>
    /// Gère la connexion d'un utilisateur au hub de chat
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        await ExecuteWithErrorHandlingAsync(nameof(OnConnectedAsync), async () =>
        {
            var userId = GetUserId();
            var userRole = GetUserRole();
            var userName = GetUserName();

            _logger.LogInformation("Utilisateur {UserName} ({UserId}) connecté au ChatHub avec le rôle {Role}",
                userName, userId, userRole);

            // Marquer l'utilisateur comme en ligne
            await SetUserOnlineStatusAsync(userId, true);

            // Notifier les contacts que l'utilisateur est en ligne
            await NotifyContactsOfStatusChangeAsync(userId, true);

            await base.OnConnectedAsync();
        });
    }

    /// <summary>
    /// Gère la déconnexion d'un utilisateur du hub de chat
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        await ExecuteWithErrorHandlingAsync(nameof(OnDisconnectedAsync), async () =>
        {
            var userId = GetUserId();

            // Marquer l'utilisateur comme hors ligne
            await SetUserOnlineStatusAsync(userId, false);

            // Notifier les contacts que l'utilisateur est hors ligne
            await NotifyContactsOfStatusChangeAsync(userId, false);

            await base.OnDisconnectedAsync(exception);
        });
    }

    /// <summary>
    /// Permet à un utilisateur de rejoindre une conversation
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    [HubMethodName("JoinConversation")]
    public async Task JoinConversationAsync(Guid conversationId)
    {
        await ExecuteWithErrorHandlingAsync(nameof(JoinConversationAsync), async () =>
        {
            ValidateInput(conversationId, nameof(conversationId));

            var userId = GetUserId();
            var userRole = GetUserRole();

            // Vérifier que l'utilisateur a accès à cette conversation
            if (!await CanAccessConversationAsync(userId, conversationId, userRole))
            {
                await SendErrorAsync(nameof(JoinConversationAsync), "Vous n'avez pas accès à cette conversation");
                return;
            }

            var groupName = SignalRGroups.GetConversationGroup(conversationId);
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

            _logger.LogInformation("Utilisateur {UserId} a rejoint la conversation {ConversationId}", userId, conversationId);

            // Notifier les autres participants que l'utilisateur a rejoint
            await Clients.OthersInGroup(groupName).SendAsync("UserJoinedConversation", new
            {
                UserId = userId,
                UserName = GetUserName(),
                ConversationId = conversationId,
                JoinedAt = DateTime.UtcNow
            });

            await SendSuccessAsync(nameof(JoinConversationAsync), $"Vous avez rejoint la conversation {conversationId}");
        });
    }

    /// <summary>
    /// Permet à un utilisateur de quitter une conversation
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    [HubMethodName("LeaveConversation")]
    public async Task LeaveConversationAsync(Guid conversationId)
    {
        await ExecuteWithErrorHandlingAsync(nameof(LeaveConversationAsync), async () =>
        {
            ValidateInput(conversationId, nameof(conversationId));

            var userId = GetUserId();
            var groupName = SignalRGroups.GetConversationGroup(conversationId);
            
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

            _logger.LogInformation("Utilisateur {UserId} a quitté la conversation {ConversationId}", userId, conversationId);

            // Notifier les autres participants que l'utilisateur a quitté
            await Clients.Group(groupName).SendAsync("UserLeftConversation", new
            {
                UserId = userId,
                UserName = GetUserName(),
                ConversationId = conversationId,
                LeftAt = DateTime.UtcNow
            });

            await SendSuccessAsync(nameof(LeaveConversationAsync), $"Vous avez quitté la conversation {conversationId}");
        });
    }

    /// <summary>
    /// Envoie un message dans une conversation
    /// </summary>
    /// <param name="message">Message à envoyer</param>
    [HubMethodName("SendMessage")]
    public async Task SendMessageAsync(ChatMessageDto message)
    {
        await ExecuteWithErrorHandlingAsync(nameof(SendMessageAsync), async () =>
        {
            ValidateInput(message, nameof(message));
            ValidateInput(message.Content, nameof(message.Content));

            var userId = GetUserId();
            var userName = GetUserName();

            // Vérifier que l'utilisateur est l'expéditeur du message
            if (message.SenderId != userId)
            {
                await SendErrorAsync(nameof(SendMessageAsync), "Vous ne pouvez envoyer des messages qu'en votre nom");
                return;
            }

            // Vérifier l'accès à la conversation
            if (!await CanAccessConversationAsync(userId, message.ConversationId, GetUserRole()))
            {
                await SendErrorAsync(nameof(SendMessageAsync), "Vous n'avez pas accès à cette conversation");
                return;
            }

            // Compléter les informations du message
            message.Id = Guid.NewGuid();
            message.SenderName = userName;
            message.SentAt = DateTime.UtcNow;
            message.IsRead = false;

            var groupName = SignalRGroups.GetConversationGroup(message.ConversationId);

            // Envoyer le message à tous les participants de la conversation
            await Clients.Group(groupName).SendAsync("ReceiveMessage", message);

            _logger.LogInformation("Message envoyé par {UserId} dans la conversation {ConversationId}",
                userId, message.ConversationId);

            await SendSuccessAsync(nameof(SendMessageAsync), "Message envoyé");
        });
    }

    /// <summary>
    /// Marque un message comme lu
    /// </summary>
    /// <param name="messageId">ID du message</param>
    /// <param name="conversationId">ID de la conversation</param>
    [HubMethodName("MarkMessageAsRead")]
    public async Task MarkMessageAsReadAsync(Guid messageId, Guid conversationId)
    {
        await ExecuteWithErrorHandlingAsync(nameof(MarkMessageAsReadAsync), async () =>
        {
            ValidateInput(messageId, nameof(messageId));
            ValidateInput(conversationId, nameof(conversationId));

            var userId = GetUserId();
            var userName = GetUserName();

            var groupName = SignalRGroups.GetConversationGroup(conversationId);

            // Notifier les autres participants que le message a été lu
            await Clients.OthersInGroup(groupName).SendAsync("MessageMarkedAsRead", new
            {
                MessageId = messageId,
                ConversationId = conversationId,
                ReadBy = userId,
                ReadByName = userName,
                ReadAt = DateTime.UtcNow
            });

            _logger.LogInformation("Message {MessageId} marqué comme lu par {UserId} dans la conversation {ConversationId}",
                messageId, userId, conversationId);
        });
    }

    /// <summary>
    /// Indique qu'un utilisateur est en train de taper
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    [HubMethodName("StartTyping")]
    public async Task StartTypingAsync(Guid conversationId)
    {
        await ExecuteWithErrorHandlingAsync(nameof(StartTypingAsync), async () =>
        {
            ValidateInput(conversationId, nameof(conversationId));

            var userId = GetUserId();
            var userName = GetUserName();

            var groupName = SignalRGroups.GetConversationGroup(conversationId);

            // Notifier les autres participants que l'utilisateur tape
            await Clients.OthersInGroup(groupName).SendAsync("UserStartedTyping", new
            {
                UserId = userId,
                UserName = userName,
                ConversationId = conversationId,
                StartedAt = DateTime.UtcNow
            });

            _logger.LogDebug("Utilisateur {UserId} a commencé à taper dans la conversation {ConversationId}",
                userId, conversationId);
        });
    }

    /// <summary>
    /// Indique qu'un utilisateur a arrêté de taper
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    [HubMethodName("StopTyping")]
    public async Task StopTypingAsync(Guid conversationId)
    {
        await ExecuteWithErrorHandlingAsync(nameof(StopTypingAsync), async () =>
        {
            ValidateInput(conversationId, nameof(conversationId));

            var userId = GetUserId();
            var userName = GetUserName();

            var groupName = SignalRGroups.GetConversationGroup(conversationId);

            // Notifier les autres participants que l'utilisateur a arrêté de taper
            await Clients.OthersInGroup(groupName).SendAsync("UserStoppedTyping", new
            {
                UserId = userId,
                UserName = userName,
                ConversationId = conversationId,
                StoppedAt = DateTime.UtcNow
            });

            _logger.LogDebug("Utilisateur {UserId} a arrêté de taper dans la conversation {ConversationId}",
                userId, conversationId);
        });
    }

    /// <summary>
    /// Obtient la liste des utilisateurs en ligne dans une conversation
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    [HubMethodName("GetOnlineUsers")]
    public async Task GetOnlineUsersAsync(Guid conversationId)
    {
        await ExecuteWithErrorHandlingAsync(nameof(GetOnlineUsersAsync), async () =>
        {
            ValidateInput(conversationId, nameof(conversationId));

            // Dans une implémentation complète, on récupérerait la liste des utilisateurs en ligne
            // Pour l'instant, on retourne une liste vide
            var onlineUsers = new List<ConnectedUserDto>();

            await Clients.Caller.SendAsync("OnlineUsersList", new
            {
                ConversationId = conversationId,
                OnlineUsers = onlineUsers,
                Timestamp = DateTime.UtcNow
            });
        });
    }

    /// <summary>
    /// Met à jour le statut en ligne d'un utilisateur
    /// </summary>
    private async Task SetUserOnlineStatusAsync(Guid userId, bool isOnline)
    {
        try
        {
            var cacheKey = $"user:online:{userId}";
            if (isOnline)
            {
                await _cacheService.SetAsync(cacheKey, new
                {
                    UserId = userId,
                    IsOnline = true,
                    LastSeen = DateTime.UtcNow
                }, TimeSpan.FromHours(1));
            }
            else
            {
                await _cacheService.RemoveAsync(cacheKey);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du statut en ligne pour l'utilisateur {UserId}", userId);
        }
    }

    /// <summary>
    /// Notifie les contacts du changement de statut d'un utilisateur
    /// </summary>
    private async Task NotifyContactsOfStatusChangeAsync(Guid userId, bool isOnline)
    {
        try
        {
            // Dans une implémentation complète, on récupérerait la liste des contacts
            // et on les notifierait du changement de statut
            _logger.LogDebug("Notification de changement de statut pour l'utilisateur {UserId}: {Status}",
                userId, isOnline ? "en ligne" : "hors ligne");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la notification de changement de statut pour l'utilisateur {UserId}", userId);
        }
    }

    /// <summary>
    /// Vérifie si un utilisateur peut accéder à une conversation
    /// </summary>
    private async Task<bool> CanAccessConversationAsync(Guid userId, Guid conversationId, UserRole userRole)
    {
        // Dans une implémentation complète, on vérifierait en base de données
        // Pour l'instant, on autorise selon le rôle
        return userRole switch
        {
            UserRole.Admin or UserRole.Manager or UserRole.Support => true,
            UserRole.Client or UserRole.Provider => true, // On vérifierait que c'est leur conversation
            _ => false
        };
    }
}
