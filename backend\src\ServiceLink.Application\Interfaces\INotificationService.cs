using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.Interfaces;

/// <summary>
/// Interface pour le service de notifications temps réel
/// Gère les notifications via SignalR pour tous les types d'utilisateurs
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// Envoie une notification à un utilisateur spécifique
    /// </summary>
    /// <param name="userId">ID de l'utilisateur destinataire</param>
    /// <param name="notification">Notification à envoyer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task SendToUserAsync(Guid userId, NotificationDto notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie une notification à tous les utilisateurs d'un rôle spécifique
    /// </summary>
    /// <param name="role">Rôle des utilisateurs destinataires</param>
    /// <param name="notification">Notification à envoyer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task SendToRoleAsync(UserRole role, NotificationDto notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie une notification à un groupe d'utilisateurs
    /// </summary>
    /// <param name="groupName">Nom du groupe</param>
    /// <param name="notification">Notification à envoyer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task SendToGroupAsync(string groupName, NotificationDto notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ajoute un utilisateur à un groupe
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="groupName">Nom du groupe</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task AddToGroupAsync(Guid userId, string groupName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retire un utilisateur d'un groupe
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="groupName">Nom du groupe</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task RemoveFromGroupAsync(Guid userId, string groupName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les utilisateurs connectés
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs connectés</returns>
    Task<IEnumerable<ConnectedUserDto>> GetConnectedUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie si un utilisateur est connecté
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'utilisateur est connecté</returns>
    Task<bool> IsUserConnectedAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface pour le service de chat temps réel
/// </summary>
public interface IChatService
{
    /// <summary>
    /// Envoie un message dans une conversation
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    /// <param name="message">Message à envoyer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task SendMessageAsync(Guid conversationId, ChatMessageDto message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Marque un message comme lu
    /// </summary>
    /// <param name="messageId">ID du message</param>
    /// <param name="userId">ID de l'utilisateur qui lit</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task MarkMessageAsReadAsync(Guid messageId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Indique qu'un utilisateur est en train de taper
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    /// <param name="userId">ID de l'utilisateur qui tape</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task UserTypingAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Indique qu'un utilisateur a arrêté de taper
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task UserStoppedTypingAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface pour le service de réservations temps réel
/// </summary>
public interface IBookingRealtimeService
{
    /// <summary>
    /// Notifie une nouvelle réservation
    /// </summary>
    /// <param name="booking">Données de la réservation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task NotifyNewBookingAsync(BookingNotificationDto booking, CancellationToken cancellationToken = default);

    /// <summary>
    /// Notifie un changement de statut de réservation
    /// </summary>
    /// <param name="bookingId">ID de la réservation</param>
    /// <param name="newStatus">Nouveau statut</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task NotifyBookingStatusChangeAsync(Guid bookingId, string newStatus, CancellationToken cancellationToken = default);

    /// <summary>
    /// Notifie l'annulation d'une réservation
    /// </summary>
    /// <param name="bookingId">ID de la réservation</param>
    /// <param name="reason">Raison de l'annulation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task NotifyBookingCancellationAsync(Guid bookingId, string reason, CancellationToken cancellationToken = default);
}

/// <summary>
/// DTO pour les notifications
/// </summary>
public class NotificationDto
{
    /// <summary>
    /// ID unique de la notification
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Type de notification
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Titre de la notification
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Message de la notification
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Données additionnelles (JSON)
    /// </summary>
    public object? Data { get; set; }

    /// <summary>
    /// Priorité de la notification
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

    /// <summary>
    /// Date de création
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// URL d'action (optionnelle)
    /// </summary>
    public string? ActionUrl { get; set; }

    /// <summary>
    /// Icône de la notification
    /// </summary>
    public string? Icon { get; set; }
}

/// <summary>
/// DTO pour les utilisateurs connectés
/// </summary>
public class ConnectedUserDto
{
    /// <summary>
    /// ID de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Nom complet de l'utilisateur
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Rôle de l'utilisateur
    /// </summary>
    public UserRole Role { get; set; }

    /// <summary>
    /// Date de connexion
    /// </summary>
    public DateTime ConnectedAt { get; set; }

    /// <summary>
    /// ID de la connexion SignalR
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour les messages de chat
/// </summary>
public class ChatMessageDto
{
    /// <summary>
    /// ID du message
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// ID de la conversation
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// ID de l'expéditeur
    /// </summary>
    public Guid SenderId { get; set; }

    /// <summary>
    /// Nom de l'expéditeur
    /// </summary>
    public string SenderName { get; set; } = string.Empty;

    /// <summary>
    /// Contenu du message
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Type de message (text, image, file, etc.)
    /// </summary>
    public string MessageType { get; set; } = "text";

    /// <summary>
    /// Date d'envoi
    /// </summary>
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Indique si le message a été lu
    /// </summary>
    public bool IsRead { get; set; } = false;
}

/// <summary>
/// DTO pour les notifications de réservation
/// </summary>
public class BookingNotificationDto
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du client
    /// </summary>
    public Guid ClientId { get; set; }

    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Nom du service
    /// </summary>
    public string ServiceName { get; set; } = string.Empty;

    /// <summary>
    /// Date de la réservation
    /// </summary>
    public DateTime BookingDate { get; set; }

    /// <summary>
    /// Statut de la réservation
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Prix de la réservation
    /// </summary>
    public decimal Price { get; set; }
}

/// <summary>
/// Priorité des notifications
/// </summary>
public enum NotificationPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}
