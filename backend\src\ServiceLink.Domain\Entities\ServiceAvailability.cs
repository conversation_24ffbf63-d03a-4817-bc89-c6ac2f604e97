using ServiceLink.Domain.Enums;

namespace ServiceLink.Domain.Entities;

/// <summary>
/// Entité représentant la disponibilité d'un service
/// </summary>
public class ServiceAvailability : BaseEntity
{
    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; private set; }

    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; private set; }

    /// <summary>
    /// Date de début de disponibilité
    /// </summary>
    public DateTime StartDate { get; private set; }

    /// <summary>
    /// Date de fin de disponibilité
    /// </summary>
    public DateTime EndDate { get; private set; }

    /// <summary>
    /// Heure de début (format HH:mm)
    /// </summary>
    public TimeOnly StartTime { get; private set; }

    /// <summary>
    /// Heure de fin (format HH:mm)
    /// </summary>
    public TimeOnly EndTime { get; private set; }

    /// <summary>
    /// Jours de la semaine (lundi = 1, dimanche = 7)
    /// </summary>
    public string DaysOfWeek { get; private set; } = string.Empty;

    /// <summary>
    /// Type de disponibilité
    /// </summary>
    public AvailabilityType Type { get; private set; }

    /// <summary>
    /// Indique si c'est une disponibilité récurrente
    /// </summary>
    public bool IsRecurring { get; private set; }

    /// <summary>
    /// Pattern de récurrence (JSON)
    /// </summary>
    public string? RecurrencePattern { get; private set; }

    /// <summary>
    /// Capacité maximale (nombre de créneaux simultanés)
    /// </summary>
    public int MaxCapacity { get; private set; }

    /// <summary>
    /// Capacité actuelle utilisée
    /// </summary>
    public int CurrentCapacity { get; private set; }

    /// <summary>
    /// Indique si la disponibilité est active
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// Raison de l'indisponibilité (si applicable)
    /// </summary>
    public string? UnavailabilityReason { get; private set; }

    /// <summary>
    /// Priorité de la disponibilité (1 = haute, 5 = basse)
    /// </summary>
    public int Priority { get; private set; }

    /// <summary>
    /// Métadonnées additionnelles (JSON)
    /// </summary>
    public string? Metadata { get; private set; }

    /// <summary>
    /// Navigation vers le service
    /// </summary>
    public virtual Service? Service { get; set; }

    /// <summary>
    /// Navigation vers le prestataire
    /// </summary>
    public virtual User? Provider { get; set; }

    /// <summary>
    /// Constructeur privé pour EF Core
    /// </summary>
    private ServiceAvailability() { }

    /// <summary>
    /// Constructeur pour créer une nouvelle disponibilité
    /// </summary>
    public ServiceAvailability(
        Guid serviceId,
        Guid providerId,
        DateTime startDate,
        DateTime endDate,
        TimeOnly startTime,
        TimeOnly endTime,
        AvailabilityType type = AvailabilityType.Available,
        int maxCapacity = 1)
    {
        if (startDate > endDate)
            throw new ArgumentException("Start date cannot be after end date");

        if (startTime >= endTime)
            throw new ArgumentException("Start time cannot be after or equal to end time");

        ServiceId = serviceId;
        ProviderId = providerId;
        StartDate = startDate.Date;
        EndDate = endDate.Date;
        StartTime = startTime;
        EndTime = endTime;
        Type = type;
        MaxCapacity = maxCapacity;
        CurrentCapacity = 0;
        IsActive = true;
        Priority = 3; // Priorité normale par défaut
    }

    /// <summary>
    /// Constructeur pour disponibilité récurrente
    /// </summary>
    public ServiceAvailability(
        Guid serviceId,
        Guid providerId,
        TimeOnly startTime,
        TimeOnly endTime,
        IEnumerable<DayOfWeek> daysOfWeek,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int maxCapacity = 1)
    {
        if (startTime >= endTime)
            throw new ArgumentException("Start time cannot be after or equal to end time");

        ServiceId = serviceId;
        ProviderId = providerId;
        StartDate = startDate?.Date ?? DateTime.Today;
        EndDate = endDate?.Date ?? DateTime.Today.AddYears(1);
        StartTime = startTime;
        EndTime = endTime;
        Type = AvailabilityType.Available;
        IsRecurring = true;
        MaxCapacity = maxCapacity;
        CurrentCapacity = 0;
        IsActive = true;
        Priority = 3;

        SetDaysOfWeek(daysOfWeek);
    }

    /// <summary>
    /// Met à jour les dates de disponibilité
    /// </summary>
    public void UpdateDates(DateTime startDate, DateTime endDate)
    {
        if (startDate > endDate)
            throw new ArgumentException("Start date cannot be after end date");

        StartDate = startDate.Date;
        EndDate = endDate.Date;
    }

    /// <summary>
    /// Met à jour les heures de disponibilité
    /// </summary>
    public void UpdateTimes(TimeOnly startTime, TimeOnly endTime)
    {
        if (startTime >= endTime)
            throw new ArgumentException("Start time cannot be after or equal to end time");

        StartTime = startTime;
        EndTime = endTime;
    }

    /// <summary>
    /// Met à jour les jours de la semaine
    /// </summary>
    public void SetDaysOfWeek(IEnumerable<DayOfWeek> days)
    {
        var dayNumbers = days.Select(d => (int)d == 0 ? 7 : (int)d).OrderBy(d => d);
        DaysOfWeek = string.Join(",", dayNumbers);
    }

    /// <summary>
    /// Obtient les jours de la semaine
    /// </summary>
    public IEnumerable<DayOfWeek> GetDaysOfWeek()
    {
        if (string.IsNullOrWhiteSpace(DaysOfWeek))
            return Enumerable.Empty<DayOfWeek>();

        return DaysOfWeek.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(int.Parse)
            .Select(d => d == 7 ? DayOfWeek.Sunday : (DayOfWeek)d);
    }

    /// <summary>
    /// Met à jour la capacité
    /// </summary>
    public void UpdateCapacity(int maxCapacity)
    {
        if (maxCapacity < 1)
            throw new ArgumentException("Max capacity must be at least 1");

        if (maxCapacity < CurrentCapacity)
            throw new ArgumentException("Max capacity cannot be less than current capacity");

        MaxCapacity = maxCapacity;
    }

    /// <summary>
    /// Réserve un créneau
    /// </summary>
    public bool TryReserveSlot()
    {
        if (CurrentCapacity >= MaxCapacity || Type != AvailabilityType.Available || !IsActive)
            return false;

        CurrentCapacity++;
        return true;
    }

    /// <summary>
    /// Libère un créneau
    /// </summary>
    public void ReleaseSlot()
    {
        if (CurrentCapacity > 0)
            CurrentCapacity--;
    }

    /// <summary>
    /// Marque comme indisponible
    /// </summary>
    public void MarkAsUnavailable(string reason)
    {
        Type = AvailabilityType.Unavailable;
        UnavailabilityReason = reason;
    }

    /// <summary>
    /// Marque comme disponible
    /// </summary>
    public void MarkAsAvailable()
    {
        Type = AvailabilityType.Available;
        UnavailabilityReason = null;
    }

    /// <summary>
    /// Active ou désactive la disponibilité
    /// </summary>
    public void SetActive(bool isActive)
    {
        IsActive = isActive;
    }

    /// <summary>
    /// Met à jour la priorité
    /// </summary>
    public void UpdatePriority(int priority)
    {
        if (priority < 1 || priority > 5)
            throw new ArgumentException("Priority must be between 1 and 5");

        Priority = priority;
    }

    /// <summary>
    /// Vérifie si la disponibilité est valide pour une date/heure donnée
    /// </summary>
    public bool IsValidForDateTime(DateTime dateTime)
    {
        if (!IsActive || Type != AvailabilityType.Available)
            return false;

        var date = dateTime.Date;
        var time = TimeOnly.FromDateTime(dateTime);

        // Vérifier la plage de dates
        if (date < StartDate || date > EndDate)
            return false;

        // Vérifier l'heure
        if (time < StartTime || time >= EndTime)
            return false;

        // Vérifier le jour de la semaine pour les récurrences
        if (IsRecurring)
        {
            var dayOfWeek = dateTime.DayOfWeek;
            var validDays = GetDaysOfWeek();
            if (!validDays.Contains(dayOfWeek))
                return false;
        }

        // Vérifier la capacité
        return CurrentCapacity < MaxCapacity;
    }

    /// <summary>
    /// Obtient la durée de la disponibilité en minutes
    /// </summary>
    public int GetDurationMinutes()
    {
        return (int)(EndTime - StartTime).TotalMinutes;
    }

    /// <summary>
    /// Vérifie si la disponibilité chevauche avec une autre
    /// </summary>
    public bool OverlapsWith(ServiceAvailability other)
    {
        if (other.ServiceId != ServiceId)
            return false;

        // Vérifier le chevauchement des dates
        if (EndDate < other.StartDate || StartDate > other.EndDate)
            return false;

        // Vérifier le chevauchement des heures
        if (EndTime <= other.StartTime || StartTime >= other.EndTime)
            return false;

        // Pour les récurrences, vérifier les jours de la semaine
        if (IsRecurring && other.IsRecurring)
        {
            var thisDays = GetDaysOfWeek();
            var otherDays = other.GetDaysOfWeek();
            return thisDays.Intersect(otherDays).Any();
        }

        return true;
    }

    /// <summary>
    /// Calcule le nombre de créneaux disponibles pour une durée donnée
    /// </summary>
    public int GetAvailableSlots(int durationMinutes)
    {
        var totalDuration = GetDurationMinutes();
        var slotsPerPeriod = totalDuration / durationMinutes;
        var availableCapacity = MaxCapacity - CurrentCapacity;
        
        return Math.Min(slotsPerPeriod, availableCapacity);
    }
}
