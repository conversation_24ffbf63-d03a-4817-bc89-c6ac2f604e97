# 🛠️ ServiceLink - Guide de Configuration Développement

## 📋 Table des Matières
- [<PERSON>rérequis](#prérequis)
- [Installation](#installation)
- [Configuration Git](#configuration-git)
- [Scripts d'Aide](#scripts-daide)
- [Workflow de Développement](#workflow-de-développement)
- [Commandes Utiles](#commandes-utiles)

## 🔧 Prérequis

### Logiciels Requis
- **.NET 9 SDK** - [Télécharger](https://dotnet.microsoft.com/download/dotnet/9.0)
- **Node.js 18+** - [Télécharger](https://nodejs.org/)
- **Docker Desktop** - [Télécharger](https://www.docker.com/products/docker-desktop)
- **Git** - [Télécharger](https://git-scm.com/)
- **Visual Studio Code** ou **Visual Studio 2022**

### Extensions VS Code Recommandées
```json
{
  "recommendations": [
    "ms-dotnettools.csharp",
    "ms-dotnettools.vscode-dotnet-runtime",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.powershell",
    "eamodio.gitlens"
  ]
}
```

## 🚀 Installation

### 1. Cloner le Repository
```bash
git clone <repository-url>
cd ServiceLink
```

### 2. Configuration Git
```bash
# Configuration utilisateur
git config user.name "Votre Nom"
git config user.email "<EMAIL>"

# Configuration du template de commit
git config commit.template .gitmessage

# Basculer sur la branche develop
git checkout develop
```

### 3. Installation des Dépendances

#### Backend (.NET)
```bash
cd backend
dotnet restore
dotnet build
```

#### Frontend (React)
```bash
cd frontend
npm install
```

### 4. Configuration de la Base de Données
```bash
# Démarrer PostgreSQL avec Docker
docker-compose up -d postgres

# Appliquer les migrations (quand elles seront créées)
cd backend
dotnet ef database update --project src/ServiceLink.Infrastructure
```

## ⚙️ Configuration Git

### GitFlow Configuré
Le projet utilise GitFlow avec les branches suivantes :
- **main** : Production (protégée)
- **develop** : Intégration (protégée)
- **feature/** : Nouvelles fonctionnalités
- **hotfix/** : Corrections urgentes
- **release/** : Préparation de versions

### Conventions de Commits
Format : `<type>[optional scope]: <description>`

Types disponibles :
- `feat` : Nouvelle fonctionnalité
- `fix` : Correction de bug
- `docs` : Documentation
- `style` : Formatage
- `refactor` : Refactoring
- `perf` : Performance
- `test` : Tests
- `build` : Build system
- `ci` : CI/CD
- `chore` : Maintenance

### Protection des Branches
- **main** : Merge uniquement via PR + 2 reviews + tests passants
- **develop** : Merge uniquement via PR + 1 review + tests passants

## 🛠️ Scripts d'Aide

### Windows (PowerShell)
```powershell
# Charger les scripts d'aide
. .\scripts\git-helpers.ps1

# Voir l'aide
Show-GitHelp

# Créer une feature branch
New-FeatureBranch -FeatureName "user-authentication" -Scope "auth"

# Commit conventionnel
Invoke-ConventionalCommit -Type "feat" -Scope "auth" -Description "add JWT authentication"

# Synchroniser avec develop
Sync-WithDevelop

# Statut du projet
Show-ProjectStatus
```

### Linux/Mac (Bash)
```bash
# Charger les scripts d'aide
source scripts/git-helpers.sh

# Voir l'aide
show_git_help

# Créer une feature branch
new_feature_branch "user-authentication" "auth"

# Commit conventionnel
conventional_commit "feat" "auth" "add JWT authentication"

# Synchroniser avec develop
sync_with_develop

# Statut du projet
show_project_status
```

## 🔄 Workflow de Développement

### 1. Nouvelle Fonctionnalité
```bash
# 1. Créer une feature branch
git checkout develop
git pull origin develop
git checkout -b feature/auth/jwt-implementation

# 2. Développer la fonctionnalité
# ... code ...

# 3. Commits réguliers
git add .
git commit -m "feat(auth): add JWT token generation"

# 4. Synchroniser avec develop (si nécessaire)
git checkout develop
git pull origin develop
git checkout feature/auth/jwt-implementation
git rebase develop

# 5. Push et créer PR
git push origin feature/auth/jwt-implementation
# Créer PR via GitHub interface
```

### 2. Correction de Bug (Hotfix)
```bash
# 1. Créer hotfix depuis main
git checkout main
git pull origin main
git checkout -b hotfix/security/sql-injection

# 2. Corriger le bug
# ... fix ...

# 3. Commit
git add .
git commit -m "fix(security): prevent SQL injection in user queries"

# 4. Push et créer PR vers main ET develop
git push origin hotfix/security/sql-injection
```

### 3. Préparation de Release
```bash
# 1. Créer release branch
git checkout develop
git pull origin develop
git checkout -b release/v1.0.0

# 2. Finaliser la version
# - Mettre à jour les numéros de version
# - Finaliser la documentation
# - Tests finaux

# 3. Commit final
git add .
git commit -m "chore(release): bump version to 1.0.0"

# 4. Merge vers main et develop
git checkout main
git merge release/v1.0.0
git tag v1.0.0
git push origin main --tags

git checkout develop
git merge release/v1.0.0
git push origin develop
```

## 🚀 Commandes Utiles

### Développement Local
```bash
# Démarrer tous les services
docker-compose up -d postgres redis

# Backend
cd backend && dotnet run --project src/ServiceLink.API

# Frontend
cd frontend && npm run dev

# Tests backend
cd backend && dotnet test

# Tests frontend
cd frontend && npm test

# Build production
cd backend && dotnet build --configuration Release
cd frontend && npm run build
```

### Git Maintenance
```bash
# Nettoyer les branches mergées
git branch --merged | grep -v -E "(main|develop|\*)" | xargs git branch -d

# Mettre à jour toutes les branches
git fetch --all --prune

# Voir l'historique graphique
git log --graph --oneline --all

# Voir les différences entre branches
git diff develop..feature/my-feature

# Annuler le dernier commit (garder les changements)
git reset --soft HEAD~1

# Annuler le dernier commit (supprimer les changements)
git reset --hard HEAD~1
```

### Docker
```bash
# Voir les logs des services
docker-compose logs -f postgres
docker-compose logs -f redis

# Redémarrer un service
docker-compose restart postgres

# Nettoyer les volumes Docker
docker-compose down -v
docker system prune -f

# Accéder à PostgreSQL
docker-compose exec postgres psql -U servicelink_user -d servicelink_dev
```

### Debugging
```bash
# Voir les processus en cours
# Windows
Get-Process | Where-Object {$_.ProcessName -like "*dotnet*" -or $_.ProcessName -like "*node*"}

# Linux/Mac
ps aux | grep -E "(dotnet|node)"

# Tuer un processus sur un port
# Windows
netstat -ano | findstr :5173
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:5173 | xargs kill -9
```

## 🔍 Troubleshooting

### Problèmes Courants

#### 1. Port déjà utilisé
```bash
# Changer le port dans vite.config.ts ou launchSettings.json
# Ou tuer le processus existant
```

#### 2. Base de données non accessible
```bash
# Vérifier que Docker est démarré
docker-compose ps

# Redémarrer PostgreSQL
docker-compose restart postgres

# Vérifier les logs
docker-compose logs postgres
```

#### 3. Dépendances manquantes
```bash
# Backend
cd backend && dotnet restore

# Frontend
cd frontend && npm install

# Nettoyer et réinstaller
cd frontend && rm -rf node_modules package-lock.json && npm install
```

#### 4. Conflits Git
```bash
# Annuler un rebase en cours
git rebase --abort

# Résoudre les conflits manuellement puis
git add .
git rebase --continue

# Ou utiliser un outil de merge
git mergetool
```

## 📚 Ressources

### Documentation
- [Git Workflow](./git-workflow.md)
- [API Documentation](./api.md) (à créer)
- [Frontend Guide](./frontend-guide.md) (à créer)
- [Deployment Guide](./deployment.md) (à créer)

### Liens Utiles
- [Conventional Commits](https://www.conventionalcommits.org/)
- [GitFlow](https://nvie.com/posts/a-successful-git-branching-model/)
- [.NET 9 Documentation](https://docs.microsoft.com/en-us/dotnet/)
- [React Documentation](https://reactjs.org/docs/)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)

---

**💡 Conseil** : Utilisez les scripts d'aide fournis pour automatiser les tâches répétitives et maintenir la cohérence du workflow Git.
