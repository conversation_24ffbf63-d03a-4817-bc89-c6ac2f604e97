using MediatR;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Interfaces;

namespace ServiceLink.Application.Handlers;

public class ResetPasswordCommandHandler : IRequestHandler<ResetPasswordCommand, bool>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordService _passwordService;
    private readonly IPasswordResetTokenRepository _passwordResetTokenRepository;

    public ResetPasswordCommandHandler(IUserRepository userRepository, IPasswordService passwordService, IPasswordResetTokenRepository passwordResetTokenRepository)
    {
        _userRepository = userRepository;
        _passwordService = passwordService;
        _passwordResetTokenRepository = passwordResetTokenRepository;
    }

    public async Task<bool> Handle(ResetPasswordCommand request, CancellationToken cancellationToken)
    {
        // 1. Vérifier le token
        var user = await _userRepository.GetByEmailAsync(request.Email);
        if (user == null || !user.IsActive)
            return false;
        var tokenValid = await _passwordResetTokenRepository.ValidateTokenAsync(user.Id, request.Token);
        if (!tokenValid)
            return false;

        // 2. Changer le mot de passe
        var (hash, salt) = _passwordService.HashPassword(request.NewPassword);

        // Use the SetPassword method to update the password hash and salt
        user.SetPassword(hash, salt);
        //user.LastPasswordChangeAt = DateTime.UtcNow;
        await _userRepository.UpdateAsync(user);

        // 3. Invalider le token
        await _passwordResetTokenRepository.InvalidateTokenAsync(user.Id, request.Token);
        return true;
    }
}
