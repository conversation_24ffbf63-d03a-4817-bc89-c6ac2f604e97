namespace ServiceLink.Domain.Entities;

/// <summary>
/// Entité représentant un avis client
/// </summary>
public class Review : BaseEntity
{
    /// <summary>
    /// ID de la réservation associée
    /// </summary>
    public Guid BookingId { get; private set; }

    /// <summary>
    /// ID du client qui donne l'avis
    /// </summary>
    public Guid ClientId { get; private set; }

    /// <summary>
    /// ID du prestataire évalué
    /// </summary>
    public Guid ProviderId { get; private set; }

    /// <summary>
    /// ID du service évalué
    /// </summary>
    public Guid ServiceId { get; private set; }

    /// <summary>
    /// Note globale (1-5)
    /// </summary>
    public int Rating { get; private set; }

    /// <summary>
    /// Note pour la qualité du service (1-5)
    /// </summary>
    public int QualityRating { get; private set; }

    /// <summary>
    /// Note pour la ponctualité (1-5)
    /// </summary>
    public int PunctualityRating { get; private set; }

    /// <summary>
    /// Note pour la communication (1-5)
    /// </summary>
    public int CommunicationRating { get; private set; }

    /// <summary>
    /// Note pour le rapport qualité/prix (1-5)
    /// </summary>
    public int ValueRating { get; private set; }

    /// <summary>
    /// Titre de l'avis
    /// </summary>
    public string Title { get; private set; } = string.Empty;

    /// <summary>
    /// Commentaire détaillé
    /// </summary>
    public string Comment { get; private set; } = string.Empty;

    /// <summary>
    /// Points positifs
    /// </summary>
    public string? PositivePoints { get; private set; }

    /// <summary>
    /// Points d'amélioration
    /// </summary>
    public string? ImprovementPoints { get; private set; }

    /// <summary>
    /// Indique si le client recommande le prestataire
    /// </summary>
    public bool WouldRecommend { get; private set; }

    /// <summary>
    /// Indique si l'avis est vérifié
    /// </summary>
    public bool IsVerified { get; private set; }

    /// <summary>
    /// Indique si l'avis est publié
    /// </summary>
    public bool IsPublished { get; private set; }

    /// <summary>
    /// Date de publication
    /// </summary>
    public DateTime? PublishedAt { get; private set; }

    /// <summary>
    /// Réponse du prestataire
    /// </summary>
    public string? ProviderResponse { get; private set; }

    /// <summary>
    /// Date de réponse du prestataire
    /// </summary>
    public DateTime? ProviderResponseAt { get; private set; }

    /// <summary>
    /// Nombre de likes
    /// </summary>
    public int LikesCount { get; private set; }

    /// <summary>
    /// Nombre de signalements
    /// </summary>
    public int ReportsCount { get; private set; }

    /// <summary>
    /// Indique si l'avis est signalé comme inapproprié
    /// </summary>
    public bool IsFlagged { get; private set; }

    /// <summary>
    /// Raison du signalement
    /// </summary>
    public string? FlagReason { get; private set; }

    /// <summary>
    /// Photos associées à l'avis (URLs séparées par des virgules)
    /// </summary>
    public string? Photos { get; private set; }

    /// <summary>
    /// Tags associés à l'avis
    /// </summary>
    public string? Tags { get; private set; }

    /// <summary>
    /// Navigation vers la réservation
    /// </summary>
    public virtual Booking? Booking { get; set; }

    /// <summary>
    /// Navigation vers le client
    /// </summary>
    public virtual User? Client { get; set; }

    /// <summary>
    /// Navigation vers le prestataire
    /// </summary>
    public virtual User? Provider { get; set; }

    /// <summary>
    /// Navigation vers le service
    /// </summary>
    public virtual Service? Service { get; set; }

    /// <summary>
    /// Constructeur privé pour EF Core
    /// </summary>
    private Review() { }

    /// <summary>
    /// Constructeur pour créer un nouvel avis
    /// </summary>
    public Review(
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        Guid serviceId,
        int rating,
        string title,
        string comment,
        bool wouldRecommend = true)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException("Rating must be between 1 and 5", nameof(rating));

        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        ServiceId = serviceId;
        Rating = rating;
        Title = title;
        Comment = comment;
        WouldRecommend = wouldRecommend;
        
        // Par défaut, toutes les notes détaillées sont égales à la note globale
        QualityRating = rating;
        PunctualityRating = rating;
        CommunicationRating = rating;
        ValueRating = rating;
        
        IsVerified = true; // Vérifié car lié à une réservation
        IsPublished = true; // Publié par défaut
        PublishedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Met à jour les notes détaillées
    /// </summary>
    public void UpdateDetailedRatings(
        int qualityRating,
        int punctualityRating,
        int communicationRating,
        int valueRating)
    {
        ValidateRating(qualityRating, nameof(qualityRating));
        ValidateRating(punctualityRating, nameof(punctualityRating));
        ValidateRating(communicationRating, nameof(communicationRating));
        ValidateRating(valueRating, nameof(valueRating));

        QualityRating = qualityRating;
        PunctualityRating = punctualityRating;
        CommunicationRating = communicationRating;
        ValueRating = valueRating;

        // Recalculer la note globale
        Rating = (int)Math.Round((qualityRating + punctualityRating + communicationRating + valueRating) / 4.0);
    }

    /// <summary>
    /// Met à jour le contenu de l'avis
    /// </summary>
    public void UpdateContent(string title, string comment, string? positivePoints = null, string? improvementPoints = null)
    {
        Title = title;
        Comment = comment;
        PositivePoints = positivePoints;
        ImprovementPoints = improvementPoints;
    }

    /// <summary>
    /// Ajoute une réponse du prestataire
    /// </summary>
    public void AddProviderResponse(string response)
    {
        ProviderResponse = response;
        ProviderResponseAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Met à jour la réponse du prestataire
    /// </summary>
    public void UpdateProviderResponse(string response)
    {
        ProviderResponse = response;
        ProviderResponseAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Supprime la réponse du prestataire
    /// </summary>
    public void RemoveProviderResponse()
    {
        ProviderResponse = null;
        ProviderResponseAt = null;
    }

    /// <summary>
    /// Publie l'avis
    /// </summary>
    public void Publish()
    {
        IsPublished = true;
        PublishedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Dépublie l'avis
    /// </summary>
    public void Unpublish()
    {
        IsPublished = false;
        PublishedAt = null;
    }

    /// <summary>
    /// Signale l'avis comme inapproprié
    /// </summary>
    public void Flag(string reason)
    {
        IsFlagged = true;
        FlagReason = reason;
        ReportsCount++;
    }

    /// <summary>
    /// Retire le signalement
    /// </summary>
    public void Unflag()
    {
        IsFlagged = false;
        FlagReason = null;
    }

    /// <summary>
    /// Ajoute un like
    /// </summary>
    public void AddLike()
    {
        LikesCount++;
    }

    /// <summary>
    /// Retire un like
    /// </summary>
    public void RemoveLike()
    {
        if (LikesCount > 0)
            LikesCount--;
    }

    /// <summary>
    /// Met à jour les photos
    /// </summary>
    public void UpdatePhotos(IEnumerable<string> photoUrls)
    {
        Photos = string.Join(",", photoUrls.Where(url => !string.IsNullOrWhiteSpace(url)));
    }

    /// <summary>
    /// Met à jour les tags
    /// </summary>
    public void UpdateTags(IEnumerable<string> tags)
    {
        Tags = string.Join(",", tags.Where(tag => !string.IsNullOrWhiteSpace(tag)));
    }

    /// <summary>
    /// Obtient la liste des photos
    /// </summary>
    public IEnumerable<string> GetPhotos()
    {
        return string.IsNullOrWhiteSpace(Photos) 
            ? Enumerable.Empty<string>() 
            : Photos.Split(',', StringSplitOptions.RemoveEmptyEntries);
    }

    /// <summary>
    /// Obtient la liste des tags
    /// </summary>
    public IEnumerable<string> GetTags()
    {
        return string.IsNullOrWhiteSpace(Tags) 
            ? Enumerable.Empty<string>() 
            : Tags.Split(',', StringSplitOptions.RemoveEmptyEntries);
    }

    /// <summary>
    /// Calcule la note moyenne des critères détaillés
    /// </summary>
    public decimal GetAverageDetailedRating()
    {
        return (QualityRating + PunctualityRating + CommunicationRating + ValueRating) / 4.0m;
    }

    /// <summary>
    /// Vérifie si l'avis peut être modifié
    /// </summary>
    public bool CanBeModified()
    {
        // L'avis peut être modifié dans les 7 jours suivant sa création
        return DateTime.UtcNow <= CreatedAt.AddDays(7);
    }

    /// <summary>
    /// Valide une note
    /// </summary>
    private static void ValidateRating(int rating, string paramName)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException("Rating must be between 1 and 5", paramName);
    }
}
