using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ServiceLink.Application.Interfaces;
using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Configuration pour le service d'email
/// </summary>
public class EmailSettings
{
    /// <summary>
    /// Serveur SMTP
    /// </summary>
    public string SmtpServer { get; set; } = "localhost";

    /// <summary>
    /// Port SMTP
    /// </summary>
    public int SmtpPort { get; set; } = 587;

    /// <summary>
    /// Utiliser SSL/TLS
    /// </summary>
    public bool EnableSsl { get; set; } = true;

    /// <summary>
    /// Nom d'utilisateur SMTP
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// Mot de passe SMTP
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Adresse email de l'expéditeur
    /// </summary>
    public string FromEmail { get; set; } = "<EMAIL>";

    /// <summary>
    /// Nom de l'expéditeur
    /// </summary>
    public string FromName { get; set; } = "ServiceLink";

    /// <summary>
    /// URL de base de l'application
    /// </summary>
    public string BaseUrl { get; set; } = "https://localhost:5001";

    /// <summary>
    /// Domaines email autorisés (vide = tous autorisés)
    /// </summary>
    public string[] AllowedDomains { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Domaines email interdits
    /// </summary>
    public string[] BlockedDomains { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Mode développement (log au lieu d'envoyer)
    /// </summary>
    public bool DevelopmentMode { get; set; } = true;
}

/// <summary>
/// Service pour l'envoi d'emails
/// </summary>
public class EmailService : IEmailService
{
    private readonly EmailSettings _settings;
    private readonly ILogger<EmailService> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="settings">Configuration email</param>
    /// <param name="logger">Logger</param>
    public EmailService(IOptions<EmailSettings> settings, ILogger<EmailService> logger)
    {
        _settings = settings.Value;
        _logger = logger;
    }

    /// <summary>
    /// Envoie un email de confirmation d'inscription
    /// </summary>
    public async Task<bool> SendWelcomeEmailAsync(string email, string firstName, string confirmationToken, CancellationToken cancellationToken = default)
    {
        var confirmationUrl = $"{_settings.BaseUrl}/api/auth/confirm-email?token={Uri.EscapeDataString(confirmationToken)}";
        
        var subject = "Bienvenue sur ServiceLink - Confirmez votre email";
        var htmlBody = $@"
            <h2>Bienvenue sur ServiceLink, {firstName} !</h2>
            <p>Merci de vous être inscrit sur notre plateforme. Pour activer votre compte, veuillez confirmer votre adresse email en cliquant sur le lien ci-dessous :</p>
            <p><a href=""{confirmationUrl}"" style=""background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"">Confirmer mon email</a></p>
            <p>Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
            <p>{confirmationUrl}</p>
            <p>Ce lien expire dans 24 heures.</p>
            <hr>
            <p><small>Si vous n'avez pas créé de compte sur ServiceLink, ignorez cet email.</small></p>
        ";

        var textBody = $@"
            Bienvenue sur ServiceLink, {firstName} !
            
            Merci de vous être inscrit sur notre plateforme. Pour activer votre compte, veuillez confirmer votre adresse email en visitant ce lien :
            
            {confirmationUrl}
            
            Ce lien expire dans 24 heures.
            
            Si vous n'avez pas créé de compte sur ServiceLink, ignorez cet email.
        ";

        return await SendEmailAsync(email, subject, htmlBody, textBody, cancellationToken);
    }

    /// <summary>
    /// Envoie un email de confirmation d'adresse email
    /// </summary>
    public async Task<bool> SendEmailConfirmationAsync(string email, string firstName, string confirmationToken, CancellationToken cancellationToken = default)
    {
        var confirmationUrl = $"{_settings.BaseUrl}/auth/confirm-email?token={Uri.EscapeDataString(confirmationToken)}";
        
        var subject = "Confirmez votre nouvelle adresse email";
        var htmlBody = $@"
            <h2>Confirmation d'adresse email</h2>
            <p>Bonjour {firstName},</p>
            <p>Vous avez demandé à modifier votre adresse email. Pour confirmer cette nouvelle adresse, cliquez sur le lien ci-dessous :</p>
            <p><a href=""{confirmationUrl}"" style=""background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"">Confirmer mon email</a></p>
            <p>Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
            <p>{confirmationUrl}</p>
            <p>Ce lien expire dans 24 heures.</p>
        ";

        return await SendEmailAsync(email, subject, htmlBody, null, cancellationToken);
    }

    /// <summary>
    /// Envoie un email de réinitialisation de mot de passe
    /// </summary>
    public async Task<bool> SendPasswordResetEmailAsync(string email, string firstName, string resetToken, CancellationToken cancellationToken = default)
    {
        var resetUrl = $"{_settings.BaseUrl}/auth/reset-password?token={Uri.EscapeDataString(resetToken)}";
        
        var subject = "Réinitialisation de votre mot de passe";
        var htmlBody = $@"
            <h2>Réinitialisation de mot de passe</h2>
            <p>Bonjour {firstName},</p>
            <p>Vous avez demandé la réinitialisation de votre mot de passe. Cliquez sur le lien ci-dessous pour créer un nouveau mot de passe :</p>
            <p><a href=""{resetUrl}"" style=""background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"">Réinitialiser mon mot de passe</a></p>
            <p>Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
            <p>{resetUrl}</p>
            <p>Ce lien expire dans 1 heure.</p>
            <p><strong>Si vous n'avez pas demandé cette réinitialisation, ignorez cet email et votre mot de passe restera inchangé.</strong></p>
        ";

        return await SendEmailAsync(email, subject, htmlBody, null, cancellationToken);
    }

    /// <summary>
    /// Envoie un email de notification de changement de mot de passe
    /// </summary>
    public async Task<bool> SendPasswordChangedNotificationAsync(string email, string firstName, CancellationToken cancellationToken = default)
    {
        var subject = "Votre mot de passe a été modifié";
        var htmlBody = $@"
            <h2>Mot de passe modifié</h2>
            <p>Bonjour {firstName},</p>
            <p>Votre mot de passe ServiceLink a été modifié avec succès le {DateTime.UtcNow:dd/MM/yyyy à HH:mm} UTC.</p>
            <p>Si vous n'êtes pas à l'origine de cette modification, contactez immédiatement notre support.</p>
            <p>Pour votre sécurité, nous vous recommandons de :</p>
            <ul>
                <li>Vérifier que personne d'autre n'a accès à votre compte</li>
                <li>Activer l'authentification à deux facteurs si ce n'est pas déjà fait</li>
                <li>Utiliser un mot de passe unique et fort</li>
            </ul>
        ";

        return await SendEmailAsync(email, subject, htmlBody, null, cancellationToken);
    }

    /// <summary>
    /// Envoie un email de notification de verrouillage de compte
    /// </summary>
    public async Task<bool> SendAccountLockedNotificationAsync(string email, string firstName, DateTime lockedUntil, CancellationToken cancellationToken = default)
    {
        var subject = "Votre compte a été temporairement verrouillé";
        var htmlBody = $@"
            <h2>Compte temporairement verrouillé</h2>
            <p>Bonjour {firstName},</p>
            <p>Votre compte ServiceLink a été temporairement verrouillé en raison de tentatives de connexion répétées avec un mot de passe incorrect.</p>
            <p>Votre compte sera automatiquement déverrouillé le {lockedUntil:dd/MM/yyyy à HH:mm} UTC.</p>
            <p>Si vous pensez que votre compte a été compromis, contactez immédiatement notre support.</p>
        ";

        return await SendEmailAsync(email, subject, htmlBody, null, cancellationToken);
    }

    /// <summary>
    /// Envoie un email de notification de déverrouillage de compte
    /// </summary>
    public async Task<bool> SendAccountUnlockedNotificationAsync(string email, string firstName, CancellationToken cancellationToken = default)
    {
        var subject = "Votre compte a été déverrouillé";
        var htmlBody = $@"
            <h2>Compte déverrouillé</h2>
            <p>Bonjour {firstName},</p>
            <p>Votre compte ServiceLink a été déverrouillé. Vous pouvez maintenant vous connecter normalement.</p>
            <p>Pour votre sécurité, nous vous recommandons d'activer l'authentification à deux facteurs.</p>
        ";

        return await SendEmailAsync(email, subject, htmlBody, null, cancellationToken);
    }

    /// <summary>
    /// Envoie un email de notification d'activation de 2FA
    /// </summary>
    public async Task<bool> SendTwoFactorEnabledNotificationAsync(string email, string firstName, CancellationToken cancellationToken = default)
    {
        var subject = "Authentification à deux facteurs activée";
        var htmlBody = $@"
            <h2>Authentification à deux facteurs activée</h2>
            <p>Bonjour {firstName},</p>
            <p>L'authentification à deux facteurs a été activée sur votre compte ServiceLink.</p>
            <p>Votre compte est maintenant plus sécurisé. Conservez vos codes de récupération en lieu sûr.</p>
            <p>Si vous n'avez pas activé cette fonctionnalité, contactez immédiatement notre support.</p>
        ";

        return await SendEmailAsync(email, subject, htmlBody, null, cancellationToken);
    }

    /// <summary>
    /// Envoie un email de notification de désactivation de 2FA
    /// </summary>
    public async Task<bool> SendTwoFactorDisabledNotificationAsync(string email, string firstName, CancellationToken cancellationToken = default)
    {
        var subject = "Authentification à deux facteurs désactivée";
        var htmlBody = $@"
            <h2>Authentification à deux facteurs désactivée</h2>
            <p>Bonjour {firstName},</p>
            <p>L'authentification à deux facteurs a été désactivée sur votre compte ServiceLink.</p>
            <p>Si vous n'avez pas désactivé cette fonctionnalité, contactez immédiatement notre support et réactivez-la.</p>
        ";

        return await SendEmailAsync(email, subject, htmlBody, null, cancellationToken);
    }

    /// <summary>
    /// Envoie un email générique
    /// </summary>
    public async Task<bool> SendEmailAsync(string to, string subject, string htmlBody, string? textBody = null, CancellationToken cancellationToken = default)
    {
        if (!IsValidEmail(to))
        {
            _logger.LogWarning("Tentative d'envoi d'email à une adresse invalide: {Email}", to);
            return false;
        }

        if (!IsAllowedEmailDomain(to))
        {
            _logger.LogWarning("Tentative d'envoi d'email à un domaine non autorisé: {Email}", to);
            return false;
        }

        if (_settings.DevelopmentMode)
        {
            _logger.LogInformation("EMAIL (Mode développement) - To: {To}, Subject: {Subject}, Body: {Body}", 
                to, subject, htmlBody);
            return true;
        }

        try
        {
            using var client = new SmtpClient(_settings.SmtpServer, _settings.SmtpPort)
            {
                EnableSsl = _settings.EnableSsl,
                UseDefaultCredentials = false,
                Credentials = new NetworkCredential(_settings.Username, _settings.Password)
            };

            using var message = new MailMessage
            {
                From = new MailAddress(_settings.FromEmail, _settings.FromName),
                Subject = subject,
                IsBodyHtml = true,
                Body = htmlBody
            };

            message.To.Add(to);

            if (!string.IsNullOrEmpty(textBody))
            {
                var textView = AlternateView.CreateAlternateViewFromString(textBody, null, "text/plain");
                message.AlternateViews.Add(textView);
            }

            await client.SendMailAsync(message, cancellationToken);
            
            _logger.LogInformation("Email envoyé avec succès à {Email}", to);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de l'email à {Email}", to);
            return false;
        }
    }

    /// <summary>
    /// Envoie un email avec template
    /// </summary>
    public async Task<bool> SendTemplatedEmailAsync(string to, string templateName, object templateData, CancellationToken cancellationToken = default)
    {
        // TODO: Implémenter un système de templates plus sophistiqué
        _logger.LogWarning("SendTemplatedEmailAsync non implémenté. Template: {TemplateName}", templateName);
        return false;
    }

    /// <summary>
    /// Valide une adresse email
    /// </summary>
    public bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
            return emailRegex.IsMatch(email) && email.Length <= 254;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Vérifie si un domaine email est autorisé
    /// </summary>
    public bool IsAllowedEmailDomain(string email)
    {
        if (!IsValidEmail(email))
            return false;

        var domain = email.Split('@')[1].ToLowerInvariant();

        // Vérifier les domaines interdits
        if (_settings.BlockedDomains.Any(blocked => domain.Equals(blocked, StringComparison.OrdinalIgnoreCase)))
            return false;

        // Si aucun domaine autorisé n'est spécifié, tous sont autorisés (sauf les interdits)
        if (_settings.AllowedDomains.Length == 0)
            return true;

        // Vérifier les domaines autorisés
        return _settings.AllowedDomains.Any(allowed => domain.Equals(allowed, StringComparison.OrdinalIgnoreCase));
    }
}
