using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using ServiceLink.Infrastructure.Configuration;
using System.Text;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour gérer les webhooks des providers de paiement
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class WebhooksController : ControllerBase
{
    private readonly IPaymentServiceFactory _paymentServiceFactory;
    private readonly ILogger<WebhooksController> _logger;

    public WebhooksController(
        IPaymentServiceFactory paymentServiceFactory,
        ILogger<WebhooksController> logger)
    {
        _paymentServiceFactory = paymentServiceFactory;
        _logger = logger;
    }

    /// <summary>
    /// Webhook pour Stripe
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du traitement</returns>
    [HttpPost("stripe")]
    public async Task<IActionResult> StripeWebhook(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Réception d'un webhook Stripe");

            // Lire le contenu brut de la requête
            using var reader = new StreamReader(Request.Body);
            var payload = await reader.ReadToEndAsync(cancellationToken);

            // Récupérer la signature Stripe
            var signature = Request.Headers["Stripe-Signature"].FirstOrDefault();
            if (string.IsNullOrEmpty(signature))
            {
                _logger.LogWarning("Webhook Stripe reçu sans signature");
                return BadRequest("Signature manquante");
            }

            // Obtenir le service Stripe
            var stripeService = _paymentServiceFactory.CreatePaymentService(PaymentProvider.Stripe);

            // Valider la signature
            var webhookSecret = "whsec_..."; // À récupérer depuis la configuration
            if (!stripeService.ValidateWebhookSignature(payload, signature, webhookSecret))
            {
                _logger.LogWarning("Signature webhook Stripe invalide");
                return BadRequest("Signature invalide");
            }

            // Traiter l'événement
            var webhookEvent = new WebhookEventDto
            {
                EventId = Guid.NewGuid().ToString(),
                EventType = "stripe_webhook", // À parser depuis le payload
                Provider = PaymentProvider.Stripe,
                Data = new Dictionary<string, object> { ["payload"] = payload },
                CreatedAt = DateTime.UtcNow
            };

            var result = await stripeService.ProcessWebhookEventAsync(webhookEvent, cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation("Webhook Stripe traité avec succès: {Message}", result.Message);
                return Ok(new { status = "success", message = result.Message });
            }
            else
            {
                _logger.LogError("Erreur lors du traitement du webhook Stripe: {Errors}", 
                    string.Join(", ", result.Errors));
                return BadRequest(new { status = "error", errors = result.Errors });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook Stripe");
            return StatusCode(500, new { status = "error", message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Webhook pour PayPal
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du traitement</returns>
    [HttpPost("paypal")]
    public async Task<IActionResult> PayPalWebhook(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Réception d'un webhook PayPal");

            // Lire le contenu brut de la requête
            using var reader = new StreamReader(Request.Body);
            var payload = await reader.ReadToEndAsync(cancellationToken);

            // Récupérer les headers PayPal
            var authAlgo = Request.Headers["PAYPAL-AUTH-ALGO"].FirstOrDefault();
            var transmission = Request.Headers["PAYPAL-TRANSMISSION-ID"].FirstOrDefault();
            var certId = Request.Headers["PAYPAL-CERT-ID"].FirstOrDefault();
            var signature = Request.Headers["PAYPAL-TRANSMISSION-SIG"].FirstOrDefault();
            var timestamp = Request.Headers["PAYPAL-TRANSMISSION-TIME"].FirstOrDefault();

            if (string.IsNullOrEmpty(signature))
            {
                _logger.LogWarning("Webhook PayPal reçu sans signature");
                return BadRequest("Signature manquante");
            }

            // Obtenir le service PayPal
            var paypalService = _paymentServiceFactory.CreatePaymentService(PaymentProvider.PayPal);

            // Valider la signature (implémentation simplifiée)
            var webhookSecret = "webhook_secret"; // À récupérer depuis la configuration
            if (!paypalService.ValidateWebhookSignature(payload, signature, webhookSecret))
            {
                _logger.LogWarning("Signature webhook PayPal invalide");
                return BadRequest("Signature invalide");
            }

            // Traiter l'événement
            var webhookEvent = new WebhookEventDto
            {
                EventId = transmission ?? Guid.NewGuid().ToString(),
                EventType = "paypal_webhook", // À parser depuis le payload
                Provider = PaymentProvider.PayPal,
                Data = new Dictionary<string, object> 
                { 
                    ["payload"] = payload,
                    ["auth_algo"] = authAlgo ?? string.Empty,
                    ["cert_id"] = certId ?? string.Empty,
                    ["timestamp"] = timestamp ?? string.Empty
                },
                CreatedAt = DateTime.UtcNow
            };

            var result = await paypalService.ProcessWebhookEventAsync(webhookEvent, cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation("Webhook PayPal traité avec succès: {Message}", result.Message);
                return Ok(new { status = "success", message = result.Message });
            }
            else
            {
                _logger.LogError("Erreur lors du traitement du webhook PayPal: {Errors}", 
                    string.Join(", ", result.Errors));
                return BadRequest(new { status = "error", errors = result.Errors });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook PayPal");
            return StatusCode(500, new { status = "error", message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Webhook pour Flutterwave
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du traitement</returns>
    [HttpPost("flutterwave")]
    public async Task<IActionResult> FlutterwaveWebhook(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Réception d'un webhook Flutterwave");

            // Lire le contenu brut de la requête
            using var reader = new StreamReader(Request.Body);
            var payload = await reader.ReadToEndAsync(cancellationToken);

            // Récupérer la signature Flutterwave
            var signature = Request.Headers["verif-hash"].FirstOrDefault();
            if (string.IsNullOrEmpty(signature))
            {
                _logger.LogWarning("Webhook Flutterwave reçu sans signature");
                return BadRequest("Signature manquante");
            }

            // Obtenir le service Flutterwave
            var flutterwaveService = _paymentServiceFactory.CreatePaymentService(PaymentProvider.Flutterwave);

            // Valider la signature
            var webhookSecret = "webhook_hash"; // À récupérer depuis la configuration
            if (!flutterwaveService.ValidateWebhookSignature(payload, signature, webhookSecret))
            {
                _logger.LogWarning("Signature webhook Flutterwave invalide");
                return BadRequest("Signature invalide");
            }

            // Traiter l'événement
            var webhookEvent = new WebhookEventDto
            {
                EventId = Guid.NewGuid().ToString(),
                EventType = "flutterwave_webhook", // À parser depuis le payload
                Provider = PaymentProvider.Flutterwave,
                Data = new Dictionary<string, object> { ["payload"] = payload },
                CreatedAt = DateTime.UtcNow
            };

            var result = await flutterwaveService.ProcessWebhookEventAsync(webhookEvent, cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation("Webhook Flutterwave traité avec succès: {Message}", result.Message);
                return Ok(new { status = "success", message = result.Message });
            }
            else
            {
                _logger.LogError("Erreur lors du traitement du webhook Flutterwave: {Errors}", 
                    string.Join(", ", result.Errors));
                return BadRequest(new { status = "error", errors = result.Errors });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook Flutterwave");
            return StatusCode(500, new { status = "error", message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Endpoint générique pour tester les webhooks
    /// </summary>
    /// <param name="provider">Provider de paiement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du test</returns>
    [HttpPost("test/{provider}")]
    public async Task<IActionResult> TestWebhook(
        [FromRoute] string provider,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Test webhook pour le provider {Provider}", provider);

            if (!Enum.TryParse<PaymentProvider>(provider, true, out var paymentProvider))
            {
                return BadRequest(new { status = "error", message = "Provider invalide" });
            }

            // Créer un événement de test
            var testEvent = new WebhookEventDto
            {
                EventId = Guid.NewGuid().ToString(),
                EventType = "test_event",
                Provider = paymentProvider,
                Data = new Dictionary<string, object> 
                { 
                    ["test"] = true,
                    ["timestamp"] = DateTime.UtcNow.ToString("O")
                },
                CreatedAt = DateTime.UtcNow
            };

            var service = _paymentServiceFactory.CreatePaymentService(paymentProvider);
            var result = await service.ProcessWebhookEventAsync(testEvent, cancellationToken);

            return Ok(new 
            { 
                status = "success", 
                provider = provider,
                result = new
                {
                    success = result.Success,
                    message = result.Message,
                    actions = result.ActionsPerformed,
                    errors = result.Errors
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du test webhook pour {Provider}", provider);
            return StatusCode(500, new { status = "error", message = ex.Message });
        }
    }

    /// <summary>
    /// Endpoint pour obtenir les statistiques des webhooks
    /// </summary>
    /// <returns>Statistiques des webhooks</returns>
    [HttpGet("stats")]
    public IActionResult GetWebhookStats()
    {
        try
        {
            var stats = new
            {
                providers = new[]
                {
                    new { name = "Stripe", endpoint = "/api/webhooks/stripe", status = "active" },
                    new { name = "PayPal", endpoint = "/api/webhooks/paypal", status = "active" },
                    new { name = "Flutterwave", endpoint = "/api/webhooks/flutterwave", status = "active" }
                },
                total_providers = 3,
                last_updated = DateTime.UtcNow.ToString("O")
            };

            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques webhooks");
            return StatusCode(500, new { status = "error", message = "Erreur interne du serveur" });
        }
    }
}
