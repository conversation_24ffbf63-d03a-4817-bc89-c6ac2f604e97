# 🔄 ServiceLink - Git Workflow & Conventions

## 📋 Table des Matières
- [GitFlow Strategy](#gitflow-strategy)
- [Conventions de Commits](#conventions-de-commits)
- [Pull Request Guidelines](#pull-request-guidelines)
- [Branch Naming](#branch-naming)
- [Code Review Process](#code-review-process)

## 🌊 GitFlow Strategy

### Structure des Branches

```
main (production)
├── develop (integration)
│   ├── feature/auth-system
│   ├── feature/payment-integration
│   ├── feature/user-dashboard
│   └── hotfix/critical-bug-fix
├── release/v1.0.0
└── hotfix/security-patch
```

### Types de Branches

#### 🏠 **main** (Production)
- **Objectif** : Code en production, stable et testé
- **Protection** : Branche protégée, merge uniquement via PR
- **Déploiement** : Automatique vers l'environnement de production
- **Règles** :
  - Tous les commits doivent être signés
  - Tests obligatoires avant merge
  - Review obligatoire par au moins 2 développeurs

#### 🔧 **develop** (Intégration)
- **Objectif** : Branche d'intégration pour le développement
- **Source** : Toutes les features sont mergées ici
- **Déploiement** : Automatique vers l'environnement de staging
- **Règles** :
  - Tests automatiques obligatoires
  - Review obligatoire par au moins 1 développeur

#### ⭐ **feature/** (Fonctionnalités)
- **Objectif** : Développement de nouvelles fonctionnalités
- **Source** : Créée depuis `develop`
- **Destination** : Mergée vers `develop`
- **Durée de vie** : Courte (1-2 semaines max)

#### 🚀 **release/** (Préparation de version)
- **Objectif** : Préparation d'une nouvelle version
- **Source** : Créée depuis `develop`
- **Destination** : Mergée vers `main` et `develop`
- **Contenu** : Bug fixes, documentation, version bumping

#### 🔥 **hotfix/** (Corrections urgentes)
- **Objectif** : Corrections critiques en production
- **Source** : Créée depuis `main`
- **Destination** : Mergée vers `main` et `develop`
- **Priorité** : Très haute, déploiement immédiat

## 📝 Conventions de Commits

### Format Standard (Conventional Commits)

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types de Commits

| Type | Description | Exemple |
|------|-------------|---------|
| `feat` | Nouvelle fonctionnalité | `feat(auth): add JWT authentication` |
| `fix` | Correction de bug | `fix(api): resolve null reference exception` |
| `docs` | Documentation | `docs(readme): update installation guide` |
| `style` | Formatage, style | `style(frontend): fix linting issues` |
| `refactor` | Refactoring | `refactor(services): extract payment logic` |
| `perf` | Amélioration performance | `perf(db): optimize user queries` |
| `test` | Tests | `test(auth): add unit tests for login` |
| `build` | Build system | `build(docker): update dockerfile` |
| `ci` | CI/CD | `ci(github): add automated testing` |
| `chore` | Maintenance | `chore(deps): update dependencies` |
| `revert` | Annulation | `revert: feat(auth): add JWT authentication` |

### Scopes Recommandés

| Scope | Description |
|-------|-------------|
| `auth` | Authentification et autorisation |
| `api` | API backend |
| `frontend` | Interface utilisateur |
| `db` | Base de données |
| `payment` | Système de paiement |
| `booking` | Système de réservation |
| `user` | Gestion des utilisateurs |
| `provider` | Gestion des prestataires |
| `admin` | Interface d'administration |
| `docker` | Configuration Docker |
| `config` | Configuration |

### Exemples de Commits

```bash
# ✅ Bons exemples
feat(auth): implement JWT token refresh mechanism
fix(booking): resolve booking cancellation bug
docs(api): add swagger documentation for user endpoints
test(payment): add integration tests for stripe payment
refactor(user): extract user validation logic to service layer

# ❌ Mauvais exemples
fix bug
update code
WIP
asdf
```

### Règles de Rédaction

1. **Impératif** : Utilisez l'impératif ("add" pas "added")
2. **Minuscules** : Pas de majuscule au début de la description
3. **Pas de point** : Pas de point final dans la description
4. **50 caractères max** : Pour la description courte
5. **Corps explicatif** : Si nécessaire, expliquez le "pourquoi"

## 🔄 Pull Request Guidelines

### Template de PR

```markdown
## 📋 Description
Brief description of changes

## 🎯 Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## 🧪 Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] No regression identified

## 📸 Screenshots (if applicable)
Add screenshots for UI changes

## 📝 Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is commented where necessary
- [ ] Documentation updated
- [ ] No console errors
- [ ] Responsive design verified (for frontend)

## 🔗 Related Issues
Closes #123
Related to #456
```

### Règles de PR

1. **Titre descriptif** : Utilisez les conventions de commits
2. **Description complète** : Expliquez le contexte et les changements
3. **Tests obligatoires** : Tous les tests doivent passer
4. **Taille raisonnable** : Max 400 lignes de code modifiées
5. **Review obligatoire** : Au moins 1 reviewer pour develop, 2 pour main
6. **Pas de merge de sa propre PR** : Sauf cas exceptionnel

## 🏷️ Branch Naming

### Convention de Nommage

```
<type>/<scope>/<short-description>
```

### Exemples

```bash
# Features
feature/auth/jwt-implementation
feature/payment/stripe-integration
feature/booking/calendar-view
feature/admin/user-management

# Bug fixes
fix/auth/login-validation
fix/payment/amount-calculation
fix/booking/date-conflict

# Hotfixes
hotfix/security/sql-injection
hotfix/payment/transaction-failure

# Releases
release/v1.0.0
release/v1.1.0-beta

# Documentation
docs/api/swagger-setup
docs/deployment/docker-guide
```

## 👀 Code Review Process

### Checklist du Reviewer

#### 🔍 **Code Quality**
- [ ] Code lisible et bien structuré
- [ ] Respect des conventions de nommage
- [ ] Pas de code dupliqué
- [ ] Gestion d'erreurs appropriée
- [ ] Performance acceptable

#### 🛡️ **Sécurité**
- [ ] Pas de données sensibles exposées
- [ ] Validation des entrées utilisateur
- [ ] Authentification/autorisation correcte
- [ ] Pas de vulnérabilités évidentes

#### 🧪 **Tests**
- [ ] Tests unitaires présents et pertinents
- [ ] Couverture de code acceptable
- [ ] Tests d'intégration si nécessaire
- [ ] Pas de tests cassés

#### 📚 **Documentation**
- [ ] Code commenté si nécessaire
- [ ] Documentation API mise à jour
- [ ] README mis à jour si applicable

### Types de Commentaires

```bash
# 🔴 Bloquant (doit être corrigé)
BLOCKER: This creates a security vulnerability

# 🟡 Suggestion (amélioration recommandée)
SUGGESTION: Consider using a more descriptive variable name

# 💡 Nitpick (détail mineur)
NITPICK: Missing space after comma

# ❓ Question (demande de clarification)
QUESTION: Why did you choose this approach over X?

# 👍 Praise (encouragement)
PRAISE: Great implementation of the error handling!
```

## 🚀 Workflow Commands

### Setup Initial

```bash
# Clone et setup
git clone <repository-url>
cd ServiceLink
git checkout develop

# Configuration utilisateur
git config user.name "Your Name"
git config user.email "<EMAIL>"
```

### Développement Feature

```bash
# Créer une nouvelle feature
git checkout develop
git pull origin develop
git checkout -b feature/auth/jwt-implementation

# Développement avec commits réguliers
git add .
git commit -m "feat(auth): add JWT token generation"

# Push et création PR
git push origin feature/auth/jwt-implementation
# Créer PR via GitHub/GitLab interface
```

### Hotfix Urgent

```bash
# Créer hotfix depuis main
git checkout main
git pull origin main
git checkout -b hotfix/security/sql-injection

# Fix et commit
git add .
git commit -m "fix(security): prevent SQL injection in user queries"

# Push et merge rapide
git push origin hotfix/security/sql-injection
# Créer PR avec priorité haute
```

### Release Process

```bash
# Créer release branch
git checkout develop
git pull origin develop
git checkout -b release/v1.0.0

# Bump version et finalisation
npm version 1.0.0  # ou dotnet version update
git commit -m "chore(release): bump version to 1.0.0"

# Merge vers main et develop
git checkout main
git merge release/v1.0.0
git tag v1.0.0
git push origin main --tags

git checkout develop
git merge release/v1.0.0
git push origin develop
```

## 📊 Métriques et Monitoring

### KPIs Git

- **Lead Time** : Temps entre création et merge de PR
- **Cycle Time** : Temps de développement effectif
- **Review Time** : Temps de review des PR
- **Deployment Frequency** : Fréquence des déploiements
- **Change Failure Rate** : Taux d'échec des changements

### Outils Recommandés

- **GitHub Actions** : CI/CD automatisé
- **SonarQube** : Qualité de code
- **Dependabot** : Mise à jour des dépendances
- **Conventional Changelog** : Génération automatique de changelog

---

**📌 Note** : Ces conventions sont évolutives et peuvent être adaptées selon les besoins de l'équipe.
