using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Interfaces;

namespace ServiceLink.Infrastructure.Repositories;

public class RefreshTokenRepository : IRefreshTokenRepository
{
    private readonly ServiceLinkDbContext _context;

    public RefreshTokenRepository(ServiceLinkDbContext context)
    {
        _context = context;
    }

    public async Task SaveAsync(RefreshToken token)
    {
        await _context.RefreshTokens.AddAsync(token);
        await _context.SaveChangesAsync();
    }

    public async Task<RefreshToken?> GetByTokenAsync(string token)
    {
        return await _context.RefreshTokens.FirstOrDefaultAsync(rt => rt.Token == token && !rt.IsRevoked);
    }

    public async Task InvalidateAsync(string token)
    {
        var refreshToken = await _context.RefreshTokens.FirstOrDefaultAsync(rt => rt.Token == token);
        if (refreshToken != null)
        {
            refreshToken.IsRevoked = true;
            refreshToken.RevokedAt = System.DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }
}
