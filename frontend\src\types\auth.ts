export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: 'Client' | 'ServiceProvider' | 'Admin'
  isEmailConfirmed: boolean
  createdAt: string
  updatedAt?: string
}

export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface LoginResponse {
  token: string
  refreshToken: string
  user: User
}

export interface RegisterRequest {
  email: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  acceptTerms: boolean
  userType: 'Client' | 'Provider'
  // Champs spécifiques aux prestataires
  companyName?: string
  siret?: string
  description?: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string
  country?: string
}

export interface RegisterResponse {
  message: string
  user: User
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface RefreshTokenResponse {
  token: string
  refreshToken: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  token: string
  email: string
  password: string
  confirmPassword: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface UpdateProfileRequest {
  firstName: string
  lastName: string
  email: string
}

export interface VerificationDocument {
  id: string
  fileName: string
  fileType: string
  fileSize: number
  uploadedAt: string
  status: 'Pending' | 'Approved' | 'Rejected'
  rejectionReason?: string
}

export interface ProviderVerificationRequest {
  documents: File[]
  documentTypes: string[]
}

export interface AuthContextType {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginRequest) => Promise<void>
  register: (data: RegisterRequest) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  updateProfile: (data: UpdateProfileRequest) => Promise<void>
}
