import { create } from 'zustand';

export type BookingStatus = 
  | 'Pending' 
  | 'Confirmed' 
  | 'Rejected' 
  | 'InProgress' 
  | 'Completed' 
  | 'Cancelled' 
  | 'Expired';

export interface Booking {
  id: string;
  clientId: string;
  providerId: string;
  serviceId: string;
  status: BookingStatus;
  scheduledDate: Date;
  scheduledEndDate: Date;
  durationMinutes: number;
  baseAmount: number;
  urgentSurcharge: number;
  totalAmount: number;
  commissionAmount: number;
  isUrgent: boolean;
  notes?: string;
  providerNotes?: string;
  cancellationReason?: string;
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date;
  
  // Related entities
  client?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
    avatar?: string;
  };
  provider?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
    avatar?: string;
    rating: number;
  };
  service?: {
    id: string;
    name: string;
    description: string;
    basePrice: number;
    pricingUnit: string;
    category?: {
      id: string;
      name: string;
    };
  };
  review?: {
    id: string;
    rating: number;
    comment: string;
    createdAt: Date;
  };
}

export interface CreateBookingRequest {
  serviceId: string;
  scheduledDate: Date;
  durationMinutes: number;
  isUrgent: boolean;
  notes?: string;
}

export interface BookingFilters {
  status?: BookingStatus[];
  startDate?: Date;
  endDate?: Date;
  serviceId?: string;
  providerId?: string;
}

export interface BookingStats {
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  pendingBookings: number;
  totalRevenue: number;
  averageRating: number;
  totalReviews: number;
  completionRate: number;
  cancellationRate: number;
}

export interface BookingState {
  // State
  bookings: Booking[];
  clientBookings: Booking[];
  providerBookings: Booking[];
  selectedBooking: Booking | null;
  bookingStats: BookingStats | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  createBooking: (request: CreateBookingRequest) => Promise<Booking>;
  getBooking: (id: string) => Promise<Booking>;
  getClientBookings: (filters?: BookingFilters) => Promise<void>;
  getProviderBookings: (filters?: BookingFilters) => Promise<void>;
  confirmBooking: (id: string, notes?: string) => Promise<void>;
  rejectBooking: (id: string, reason: string) => Promise<void>;
  startService: (id: string) => Promise<void>;
  completeService: (id: string, actualDuration?: number) => Promise<void>;
  cancelBooking: (id: string, reason: string) => Promise<void>;
  getBookingStats: (startDate?: Date, endDate?: Date) => Promise<void>;
  setSelectedBooking: (booking: Booking | null) => void;
  clearError: () => void;
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7001/api';

export const useBookingStore = create<BookingState>((set, get) => ({
  // Initial state
  bookings: [],
  clientBookings: [],
  providerBookings: [],
  selectedBooking: null,
  bookingStats: null,
  isLoading: false,
  error: null,

  // Actions
  createBooking: async (request: CreateBookingRequest) => {
    set({ isLoading: true, error: null });

    try {
      // Get auth token from auth store
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/booking`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create booking');
      }

      const booking: Booking = await response.json();
      
      set((state) => ({
        bookings: [booking, ...state.bookings],
        clientBookings: [booking, ...state.clientBookings],
        selectedBooking: booking,
        isLoading: false,
        error: null,
      }));

      return booking;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create booking',
      });
      throw error;
    }
  },

  getBooking: async (id: string) => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/booking/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Booking not found');
      }

      const booking: Booking = await response.json();
      
      set({
        selectedBooking: booking,
        isLoading: false,
        error: null,
      });

      return booking;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load booking',
      });
      throw error;
    }
  },

  getClientBookings: async (filters?: BookingFilters) => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const queryParams = new URLSearchParams();
      
      if (filters?.status) {
        filters.status.forEach(status => queryParams.append('status', status));
      }
      if (filters?.startDate) {
        queryParams.append('startDate', filters.startDate.toISOString());
      }
      if (filters?.endDate) {
        queryParams.append('endDate', filters.endDate.toISOString());
      }

      const response = await fetch(`${API_BASE_URL}/booking/my-bookings?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load bookings');
      }

      const result = await response.json();
      const bookings: Booking[] = result.items || result;
      
      set({
        clientBookings: bookings,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load bookings',
      });
    }
  },

  getProviderBookings: async (filters?: BookingFilters) => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const queryParams = new URLSearchParams();
      
      if (filters?.status) {
        filters.status.forEach(status => queryParams.append('status', status));
      }
      if (filters?.startDate) {
        queryParams.append('startDate', filters.startDate.toISOString());
      }
      if (filters?.endDate) {
        queryParams.append('endDate', filters.endDate.toISOString());
      }

      const response = await fetch(`${API_BASE_URL}/booking/provider-bookings?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load provider bookings');
      }

      const result = await response.json();
      const bookings: Booking[] = result.items || result;
      
      set({
        providerBookings: bookings,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load provider bookings',
      });
    }
  },

  confirmBooking: async (id: string, notes?: string) => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/booking/${id}/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(notes),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to confirm booking');
      }

      const updatedBooking: Booking = await response.json();
      
      set((state) => ({
        providerBookings: state.providerBookings.map(booking =>
          booking.id === id ? updatedBooking : booking
        ),
        selectedBooking: state.selectedBooking?.id === id ? updatedBooking : state.selectedBooking,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to confirm booking',
      });
      throw error;
    }
  },

  rejectBooking: async (id: string, reason: string) => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/booking/${id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(reason),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to reject booking');
      }

      const updatedBooking: Booking = await response.json();
      
      set((state) => ({
        providerBookings: state.providerBookings.map(booking =>
          booking.id === id ? updatedBooking : booking
        ),
        selectedBooking: state.selectedBooking?.id === id ? updatedBooking : state.selectedBooking,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to reject booking',
      });
      throw error;
    }
  },

  startService: async (id: string) => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/booking/${id}/start`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to start service');
      }

      const updatedBooking: Booking = await response.json();
      
      set((state) => ({
        providerBookings: state.providerBookings.map(booking =>
          booking.id === id ? updatedBooking : booking
        ),
        selectedBooking: state.selectedBooking?.id === id ? updatedBooking : state.selectedBooking,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to start service',
      });
      throw error;
    }
  },

  completeService: async (id: string, actualDuration?: number) => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/booking/${id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(actualDuration),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to complete service');
      }

      const updatedBooking: Booking = await response.json();
      
      set((state) => ({
        providerBookings: state.providerBookings.map(booking =>
          booking.id === id ? updatedBooking : booking
        ),
        selectedBooking: state.selectedBooking?.id === id ? updatedBooking : state.selectedBooking,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to complete service',
      });
      throw error;
    }
  },

  cancelBooking: async (id: string, reason: string) => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/booking/${id}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ reason }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to cancel booking');
      }

      const updatedBooking: Booking = await response.json();
      
      set((state) => ({
        clientBookings: state.clientBookings.map(booking =>
          booking.id === id ? updatedBooking : booking
        ),
        providerBookings: state.providerBookings.map(booking =>
          booking.id === id ? updatedBooking : booking
        ),
        selectedBooking: state.selectedBooking?.id === id ? updatedBooking : state.selectedBooking,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to cancel booking',
      });
      throw error;
    }
  },

  getBookingStats: async (startDate?: Date, endDate?: Date) => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const queryParams = new URLSearchParams();
      if (startDate) {
        queryParams.append('startDate', startDate.toISOString());
      }
      if (endDate) {
        queryParams.append('endDate', endDate.toISOString());
      }

      const response = await fetch(`${API_BASE_URL}/booking/stats?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load booking stats');
      }

      const stats: BookingStats = await response.json();
      
      set({
        bookingStats: stats,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load booking stats',
      });
    }
  },

  setSelectedBooking: (booking: Booking | null) => {
    set({ selectedBooking: booking });
  },

  clearError: () => {
    set({ error: null });
  },
}));
