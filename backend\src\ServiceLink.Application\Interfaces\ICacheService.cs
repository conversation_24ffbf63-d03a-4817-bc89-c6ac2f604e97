using System.Text.Json;

namespace ServiceLink.Application.Interfaces;

/// <summary>
/// Interface pour le service de cache Redis
/// Implémente les stratégies Cache-aside, Write-through et Cache warming
/// </summary>
public interface ICacheService
{
    /// <summary>
    /// Récupère une valeur du cache par sa clé
    /// </summary>
    /// <typeparam name="T">Type de l'objet à récupérer</typeparam>
    /// <param name="key">Clé de cache</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'objet mis en cache ou null si non trouvé</returns>
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Met en cache une valeur avec une durée de vie spécifiée
    /// </summary>
    /// <typeparam name="T">Type de l'objet à mettre en cache</typeparam>
    /// <param name="key">Clé de cache</param>
    /// <param name="value">Valeur à mettre en cache</param>
    /// <param name="expiration">Durée de vie du cache</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task SetAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Met en cache une valeur avec une durée de vie par défaut
    /// </summary>
    /// <typeparam name="T">Type de l'objet à mettre en cache</typeparam>
    /// <param name="key">Clé de cache</param>
    /// <param name="value">Valeur à mettre en cache</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task SetAsync<T>(string key, T value, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Supprime une entrée du cache
    /// </summary>
    /// <param name="key">Clé de cache à supprimer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Supprime plusieurs entrées du cache par pattern
    /// </summary>
    /// <param name="pattern">Pattern de clés à supprimer (ex: "user:*")</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie si une clé existe dans le cache
    /// </summary>
    /// <param name="key">Clé à vérifier</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si la clé existe, false sinon</returns>
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Récupère ou crée une valeur en cache (Cache-aside pattern)
    /// </summary>
    /// <typeparam name="T">Type de l'objet</typeparam>
    /// <param name="key">Clé de cache</param>
    /// <param name="factory">Fonction pour créer la valeur si elle n'existe pas en cache</param>
    /// <param name="expiration">Durée de vie du cache</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'objet du cache ou créé par la factory</returns>
    Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan expiration, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Récupère ou crée une valeur en cache avec durée par défaut
    /// </summary>
    /// <typeparam name="T">Type de l'objet</typeparam>
    /// <param name="key">Clé de cache</param>
    /// <param name="factory">Fonction pour créer la valeur si elle n'existe pas en cache</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'objet du cache ou créé par la factory</returns>
    Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Rafraîchit le cache avec une nouvelle valeur (Cache warming)
    /// </summary>
    /// <typeparam name="T">Type de l'objet</typeparam>
    /// <param name="key">Clé de cache</param>
    /// <param name="factory">Fonction pour récupérer la nouvelle valeur</param>
    /// <param name="expiration">Durée de vie du cache</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    Task RefreshAsync<T>(string key, Func<Task<T?>> factory, TimeSpan expiration, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Obtient des statistiques sur l'utilisation du cache
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Informations sur le cache</returns>
    Task<CacheStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistiques du cache Redis
/// </summary>
public class CacheStatistics
{
    /// <summary>
    /// Nombre total de clés dans le cache
    /// </summary>
    public long TotalKeys { get; set; }

    /// <summary>
    /// Mémoire utilisée par Redis (en bytes)
    /// </summary>
    public long UsedMemory { get; set; }

    /// <summary>
    /// Nombre de connexions actives
    /// </summary>
    public int ConnectedClients { get; set; }

    /// <summary>
    /// Nombre total de commandes traitées
    /// </summary>
    public long TotalCommandsProcessed { get; set; }

    /// <summary>
    /// Taux de hit du cache (pourcentage)
    /// </summary>
    public double HitRatio { get; set; }

    /// <summary>
    /// Temps de fonctionnement de Redis (en secondes)
    /// </summary>
    public long UptimeInSeconds { get; set; }
}

/// <summary>
/// Constantes pour les clés de cache et TTL
/// </summary>
public static class CacheKeys
{
    // Préfixes pour organiser les clés
    public const string USER_PREFIX = "user:";
    public const string SERVICE_PREFIX = "service:";
    public const string BOOKING_PREFIX = "booking:";
    public const string CATEGORY_PREFIX = "category:";
    public const string SEARCH_PREFIX = "search:";
    public const string STATS_PREFIX = "stats:";

    // Clés spécifiques
    public static string UserProfile(Guid userId) => $"{USER_PREFIX}profile:{userId}";
    public static string UserByEmail(string email) => $"{USER_PREFIX}email:{email}";
    public static string ServiceSearch(string query, string location) => $"{SEARCH_PREFIX}services:{query}:{location}";
    public static string ServicesByCategory(Guid categoryId) => $"{SERVICE_PREFIX}category:{categoryId}";
    public static string ServiceCategories() => $"{CATEGORY_PREFIX}all";
    public static string AdminDashboardStats() => $"{STATS_PREFIX}admin:dashboard";
    public static string ProviderStats(Guid providerId) => $"{STATS_PREFIX}provider:{providerId}";
}

/// <summary>
/// Durées de vie par défaut pour différents types de données
/// </summary>
public static class CacheTTL
{
    /// <summary>
    /// Résultats de recherche de services (5 minutes)
    /// </summary>
    public static readonly TimeSpan SearchResults = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Profils utilisateurs (15 minutes)
    /// </summary>
    public static readonly TimeSpan UserProfiles = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Catégories de services (1 heure)
    /// </summary>
    public static readonly TimeSpan ServiceCategories = TimeSpan.FromHours(1);

    /// <summary>
    /// Statistiques dashboard (1 heure)
    /// </summary>
    public static readonly TimeSpan DashboardStats = TimeSpan.FromHours(1);

    /// <summary>
    /// Données de configuration (24 heures)
    /// </summary>
    public static readonly TimeSpan Configuration = TimeSpan.FromHours(24);

    /// <summary>
    /// TTL par défaut (30 minutes)
    /// </summary>
    public static readonly TimeSpan Default = TimeSpan.FromMinutes(30);
}
