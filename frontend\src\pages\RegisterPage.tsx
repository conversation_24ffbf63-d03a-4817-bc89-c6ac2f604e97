import React from 'react'
import { UserTypeSelection } from '../components/auth/UserTypeSelection'
import { ClientRegisterForm } from '../components/auth/ClientRegisterForm'
import { ProviderRegisterForm } from '../components/auth/ProviderRegisterForm'
import { DocumentUpload } from '../components/auth/DocumentUpload'
import { CheckCircle } from 'lucide-react'
import { Button } from '../components/ui/Button'

type RegistrationStep = 'type-selection' | 'client-form' | 'provider-form' | 'document-upload' | 'success'

export const RegisterPage: React.FC = () => {
  const [currentStep, setCurrentStep] = React.useState<RegistrationStep>('type-selection')
  const [selectedUserType, setSelectedUserType] = React.useState<'User' | 'Provider' | null>(null)

  const handleUserTypeSelect = (type: 'User' | 'Provider') => {
    setSelectedUserType(type)
  }

  const handleUserTypeContinue = () => {
    if (selectedUserType === 'User') {
      setCurrentStep('client-form')
    } else if (selectedUserType === 'Provider') {
      setCurrentStep('provider-form')
    }
  }

  const handleClientRegistrationSuccess = () => {
    setCurrentStep('success')
  }

  const handleProviderRegistrationSuccess = () => {
    setCurrentStep('document-upload')
  }

  const handleDocumentUpload = async (documents: any[]) => {
    // TODO: Implémenter l'upload des documents vers l'API
    console.log('Documents à uploader:', documents)
    
    // Simuler l'upload
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setCurrentStep('success')
  }

  const handleBackToTypeSelection = () => {
    setCurrentStep('type-selection')
    setSelectedUserType(null)
  }

  const renderStepIndicator = () => {
    const steps = [
      { id: 'type-selection', label: 'Type de compte' },
      { 
        id: selectedUserType === 'User' ? 'client-form' : 'provider-form', 
        label: 'Informations' 
      },
      ...(selectedUserType === 'Provider' ? [{ id: 'document-upload', label: 'Documents' }] : []),
      { id: 'success', label: 'Terminé' },
    ]

    const currentStepIndex = steps.findIndex(step => step.id === currentStep)

    return (
      <div className="mb-8">
        <div className="flex items-center justify-center">
          {steps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div className="flex items-center">
                <div
                  className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                    ${index <= currentStepIndex
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                    }
                  `}
                >
                  {index + 1}
                </div>
                <span
                  className={`
                    ml-2 text-sm font-medium
                    ${index <= currentStepIndex ? 'text-blue-600' : 'text-gray-500'}
                  `}
                >
                  {step.label}
                </span>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`
                    w-16 h-0.5 mx-4
                    ${index < currentStepIndex ? 'bg-blue-600' : 'bg-gray-200'}
                  `}
                />
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {currentStep !== 'type-selection' && renderStepIndicator()}

        {currentStep === 'type-selection' && (
          <UserTypeSelection
            selectedType={selectedUserType}
            onSelect={handleUserTypeSelect}
            onContinue={handleUserTypeContinue}
          />
        )}

        {currentStep === 'client-form' && (
          <ClientRegisterForm
            onSuccess={handleClientRegistrationSuccess}
            onBack={handleBackToTypeSelection}
          />
        )}

        {currentStep === 'provider-form' && (
          <ProviderRegisterForm
            onSuccess={handleProviderRegistrationSuccess}
            onBack={handleBackToTypeSelection}
          />
        )}

        {currentStep === 'document-upload' && (
          <DocumentUpload
            onUpload={handleDocumentUpload}
          />
        )}

        {currentStep === 'success' && (
          <div className="w-full max-w-md mx-auto">
            <div className="bg-white shadow-lg rounded-lg p-8 text-center">
              <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-6" />
              
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {selectedUserType === 'User' 
                  ? 'Inscription réussie !' 
                  : 'Documents envoyés !'}
              </h2>
              
              {selectedUserType === 'User' ? (
                <div>
                  <p className="text-gray-600 mb-6">
                    Votre compte client a été créé avec succès. 
                    Vérifiez votre email pour confirmer votre adresse.
                  </p>
                  <Button
                    onClick={() => window.location.href = '/login'}
                    className="w-full"
                  >
                    Se connecter
                  </Button>
                </div>
              ) : (
                <div>
                  <p className="text-gray-600 mb-6">
                    Vos documents ont été envoyés avec succès. 
                    Notre équipe les examinera sous 48h et vous contactera par email.
                  </p>
                  <div className="space-y-3">
                    <Button
                      onClick={() => window.location.href = '/login'}
                      className="w-full"
                    >
                      Se connecter
                    </Button>
                    <p className="text-sm text-gray-500">
                      Vous pourrez proposer vos services une fois votre compte validé
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
