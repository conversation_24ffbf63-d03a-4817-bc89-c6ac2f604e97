{"projet": "ServiceLink", "description": "Plateforme de mise en relation entre particuliers et prestataires de services à domicile", "slogan": "Des services de qualité à portée de main", "architecture_technique": {"approche": "Architecture en microservices avec Clean Architecture, CQRS et Repository Pattern", "backend": {"framework": ".NET Core 9+", "patterns": ["Microservices", "Clean Architecture", "CQRS (Command Query Responsibility Segregation)", "Repository Pattern"], "orm": "Entity Framework Core", "base_de_donnees": "PostgreSQL", "authentification": {"type": "JWT (JSON Web Tokens)", "securite_avancee": "MFA (Multi-Factor Authentication)", "controle_acces": "RBAC (Role-Based Access Control)"}, "audit": "Logs d'accès et horodatage complets"}, "frontend": {"framework": "React.js", "bundler": "Vite", "styles": "TailwindCSS", "composants": "Shadcn UI", "type": "Single Page Application (SPA)"}, "mobile": {"statut": "Version à venir", "technologie_prevue": "Flutter"}}, "fonctionnalites_backend": {"authentification_autorisation": {"inscription_connexion": "Système sécurisé avec validation", "gestion_roles": ["Admin Global (Super Administrateur)", "Manager", "Support", "Superviseur", "Client", "Presta<PERSON>"], "verification_profils": "Validation documents et certifications prestataires", "multi_factor_auth": "Authentification à deux facteurs"}, "gestion_utilisateurs": {"clients": {"profil_personnel": "Gestion complète du profil", "historique_services": "Suivi des prestations effectuées", "notation_avis": "Système d'évaluation des prestataires", "reservation_tiers": "Possibilité de réserver pour un autre client"}, "prestataires": {"profil_detaille": "Photos, certifications, tarifs, disponibilités", "gestion_calendrier": "Planning des missions et disponibilités", "messagerie": "Communication avec les clients", "tresorerie": "Suivi des paiements et revenus", "evaluation_clients": "Système de notation des clients"}}, "gestion_services": {"categories": ["<PERSON><PERSON><PERSON>", "Bricolage", "Jardinage", "<PERSON><PERSON><PERSON><PERSON>", "Électricité", "Cours particuliers", "Aide aux seniors", "Beauté et bien-être", "<PERSON><PERSON><PERSON>", "Déménagement", "Réparation informatique", "Garde d'animaux", "Cuisine à domicile", "Coaching sportif", "Services automobiles", "Autres"], "moderation": "Validation et modération des annonces", "matching_intelligent": "Algorithme de recommandation basé sur localisation, avis, disponibilité", "recherche_avancee": "Filtres multiples (prix, avis, localisation, disponibilité)"}, "systeme_reservations": {"types": ["Réservation instantanée", "Réservation sur devis", "Mode urgence (prestataire immédiatement disponible)"], "suivi_temps_reel": "Notifications et statut des prestations", "gps_tracking": "Suivi en temps réel pour certaines catégories"}, "systeme_paiements": {"passerelles_internationales": ["Stripe", "PayPal", "MasterCard", "VisaCard"], "paiements_mobiles_afrique": ["MTN Mobile Money", "Orange Money", "Flutterwave"], "fonctionnalites": ["Paiement sécurisé intégré", "Commission automatique (10-20%)", "Paie<PERSON> différé (Buy Now, Pay Later)", "Gestion des transactions", "Système de facturation", "Historique des paiements"]}, "administration": {"admin_global": ["Configuration plateforme complète", "Gestion des comptes et rôles", "Gestion types et catégories de services", "Supervision paiements et finances"], "support": ["Accès statistiques globales", "Gestion chat et ticketing", "Support client"], "manager": ["Fonctionnalités du support", "Gestion réclamations et litiges"], "superviseur": ["Vérification et validation documents", "Suspension comptes non-conformes"]}, "fonctionnalites_avancees": {"abonnements_premium": "Boost de visibilité pour prestataires", "fidelisation": "Cashback et réductions clients réguliers", "assurance_integree": "Protection pour les prestations", "notifications": "Système de notifications en temps réel", "conformite": "Respect RGPD"}}, "fonctionnalites_frontend": {"interface_client": {"pages_principales": ["Page d'accueil avec recherche", "Résultats de recherche avec filtres", "Profils des prestataires", "Processus de réservation", "Espace personnel", "Historique des services", "Système de paiement"], "fonctionnalites": ["Inscription/connexion rapide", "Recherche avancée avec filtres multiples", "Comparaison des profils prestataires", "Réservation instantanée ou sur devis", "Option réservation pour tiers", "Suivi temps réel des prestations", "Paiement sécurisé intégré", "Gestion du profil personnel"]}, "interface_prestataire": {"pages_principales": ["Dashboard prestataire", "Gestion du profil", "Calendrier des missions", "Messagerie clients", "Suivi financier", "Gestion des avis"], "fonctionnalites": ["Création profil détaillé avec média", "Gestion disponibilités et calendrier", "Gestion demandes de service", "Messagerie intégrée avec clients", "Suivi paiements et trésorerie", "Système d'évaluation mutuelle"]}, "interface_administration": {"dashboards_specifiques": ["Dashboard Admin Global", "Dashboard Support", "Dashboard Manager", "Dashboard Superviseur"], "fonctionnalites_communes": ["Statistiques et analytics", "Gestion des utilisateurs", "Modération des contenus", "Gestion des litiges", "Suivi des paiements"]}}, "specifications_techniques": {"securite": {"authentification": "JWT avec refresh tokens", "autorisation": "RBAC granulaire", "chiffrement": "HTTPS obligatoire", "validation": "Validation côté client et serveur", "audit": "Logs complets des actions utilisateurs"}, "performance": {"caching": "Mise en cache des données fréquentes", "optimisation": "Lazy loading et pagination", "cdn": "Distribution de contenu optimisée"}, "environnements": {"developpement": "Configuration locale avec base de données de test", "test": "Environnement de tests automatisés", "production": "Déploiement sécurisé avec monitoring"}}, "modele_economique": {"sources_revenus": ["Commission automatique sur missions (10-20%)", "Abonnements premium prestataires", "Paiement différé avec frais intégrés", "Partenariats et offres sponsorisées", "Fidélisation avec cashback"], "objectifs_annee_1": {"utilisateurs_clients": "5 000 utilisateurs actifs", "prestataires": "1 000 prestataires actifs", "revenus_prevus": "30 000 €", "investissement": "80 000 €"}}, "plan_developpement": {"phase_1": {"duree": "Mois 1-3", "focus": "Développement complet API Backend", "livrables": ["API complète avec tous les endpoints", "Système d'authentification et autorisation", "Gestion complète des utilisateurs", "Système de paiements intégré", "Tests complets sur chaque fonctionnalité", "Documentation détaillée de l'API"]}, "phase_2": {"duree": "<PERSON><PERSON> 4-6", "focus": "Développement Frontend Web", "livrables": ["Interface web complète (React.js)", "Toutes les fonctionnalités opérationnelles", "Design professionnel et responsive", "Intégration complète avec l'API", "Tests utilisateurs"]}, "phase_3": {"duree": "<PERSON><PERSON> 7-12", "focus": "Fonctionnalités avancées et lancement", "livrables": ["Application mobile (Flutter)", "Fonctionnalités GPS et urgence", "Système d'assurance intégré", "Campagne de lancement", "Acquisition premiers utilisateurs"]}}, "stack_technique_detaille": {"backend": {"runtime": ".NET Core 9+", "base_donnees": "PostgreSQL avec Entity Framework Core", "authentification": "JWT + MFA + RBAC", "api": "RESTful API avec Swagger documentation", "architecture": "Clean Architecture + CQRS + Repository Pattern", "tests": "xUnit pour tests unitaires et d'intégration", "logging": "Serilog pour audit et monitoring"}, "frontend": {"framework": "React.js 18+", "bundler": "Vite pour performance optimale", "styling": "TailwindCSS pour design system", "composants": "Shadcn UI pour composants réutilisables", "state_management": "<PERSON><PERSON> ou <PERSON>", "routing": "React Router DOM", "forms": "React Hook Form avec validation", "http_client": "Axios pour appels API"}, "outils_developpement": {"versionning": "Git avec GitFlow", "ci_cd": "GitHub Actions ou Azure DevOps", "monitoring": "Application Insights", "deploiement": "Azure App Service ou AWS"}}, "exigences_qualite": {"code": ["Documentation complète de chaque ligne de code", "Utilisation des versions de packages récentes et stables", "Vérification de la documentation officielle avant implémentation", "Tests unitaires et d'intégration obligatoires", "Code review systématique"], "fonctionnel": ["100% des fonctionnalités opérationnelles", "Tous les boutons cliquables et fonctionnels", "Expérience utilisateur fluide", "Design professionnel et responsive", "Sécurité <PERSON>e"], "performance": ["Temps de chargement optimisés", "Gestion efficace des données", "Scalabilité horizontale", "Monitoring des performances"]}, "notes_importantes": ["Développement en deux phases distinctes : Backend complet puis Frontend", "Pas de fake data ou dummy data - données réelles uniquement", "Privilégier les méthodes simples et à jour", "Application 100% opérationnelle et sécurisée avant livraison", "Tous les fichiers accessibles à tous les membres de l'équipe", "Suivi strict de l'ordre de développement spécifié"]}