using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using ServiceLink.Infrastructure.Configuration;
using ServiceLink.Infrastructure.Services;
using Xunit;

namespace ServiceLink.Infrastructure.Tests.Services;

/// <summary>
/// Tests unitaires pour PaymentServiceFactory
/// </summary>
public class PaymentServiceFactoryTests
{
    private readonly Mock<IServiceProvider> _serviceProviderMock;
    private readonly Mock<IConfiguration> _configurationMock;
    private readonly Mock<ILogger<PaymentServiceFactory>> _loggerMock;
    private readonly PaymentServiceFactory _factory;

    public PaymentServiceFactoryTests()
    {
        _serviceProviderMock = new Mock<IServiceProvider>();
        _configurationMock = new Mock<IConfiguration>();
        _loggerMock = new Mock<ILogger<PaymentServiceFactory>>();

        // Configuration par défaut
        var paymentSection = new Mock<IConfigurationSection>();
        paymentSection.Setup(x => x["DefaultProvider"]).Returns("Stripe");
        paymentSection.Setup(x => x["EnabledProviders:0"]).Returns("Stripe");
        paymentSection.Setup(x => x["EnabledProviders:1"]).Returns("PayPal");
        paymentSection.Setup(x => x["EnabledProviders:2"]).Returns("Flutterwave");

        _configurationMock.Setup(x => x.GetSection("Payment")).Returns(paymentSection.Object);

        _factory = new PaymentServiceFactory(_serviceProviderMock.Object, _configurationMock.Object, _loggerMock.Object);
    }

    [Fact]
    public void CreatePaymentService_WithStripeProvider_ReturnsStripeService()
    {
        // Arrange
        var stripeService = new Mock<StripePaymentService>(
            _configurationMock.Object, 
            Mock.Of<ILogger<StripePaymentService>>()).Object;
        
        _serviceProviderMock.Setup(x => x.GetService(typeof(StripePaymentService)))
            .Returns(stripeService);

        // Act
        var result = _factory.CreatePaymentService(PaymentProvider.Stripe);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(PaymentProvider.Stripe, result.Provider);
    }

    [Fact]
    public void CreatePaymentService_WithInvalidProvider_ThrowsArgumentException()
    {
        // Arrange
        var invalidProvider = (PaymentProvider)999;

        // Act & Assert
        Assert.Throws<ArgumentException>(() => _factory.CreatePaymentService(invalidProvider));
    }

    [Fact]
    public void GetOptimalPaymentServiceForCurrency_WithXOF_ReturnsFlutterwave()
    {
        // Arrange
        var flutterwaveService = new Mock<FlutterwavePaymentService>(
            _configurationMock.Object,
            Mock.Of<ILogger<FlutterwavePaymentService>>(),
            Mock.Of<HttpClient>()).Object;

        _serviceProviderMock.Setup(x => x.GetService(typeof(FlutterwavePaymentService)))
            .Returns(flutterwaveService);

        // Act
        var result = _factory.GetOptimalPaymentServiceForCurrency("XOF");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(PaymentProvider.Flutterwave, result.Provider);
    }

    [Fact]
    public void GetOptimalPaymentServiceForMethod_WithMobileMoney_ReturnsFlutterwave()
    {
        // Arrange
        var flutterwaveService = new Mock<FlutterwavePaymentService>(
            _configurationMock.Object,
            Mock.Of<ILogger<FlutterwavePaymentService>>(),
            Mock.Of<HttpClient>()).Object;

        _serviceProviderMock.Setup(x => x.GetService(typeof(FlutterwavePaymentService)))
            .Returns(flutterwaveService);

        // Act
        var result = _factory.GetOptimalPaymentServiceForMethod(PaymentMethodType.MobileMoney);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(PaymentProvider.Flutterwave, result.Provider);
    }

    [Fact]
    public void GetOptimalPaymentServiceForMethod_WithPayPal_ReturnsPayPal()
    {
        // Arrange
        var paypalService = new Mock<PayPalPaymentService>(
            _configurationMock.Object,
            Mock.Of<ILogger<PayPalPaymentService>>()).Object;

        _serviceProviderMock.Setup(x => x.GetService(typeof(PayPalPaymentService)))
            .Returns(paypalService);

        // Act
        var result = _factory.GetOptimalPaymentServiceForMethod(PaymentMethodType.PayPal);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(PaymentProvider.PayPal, result.Provider);
    }

    [Theory]
    [InlineData("EUR")]
    [InlineData("USD")]
    [InlineData("GBP")]
    public void GetOptimalPaymentServiceForCurrency_WithInternationalCurrency_ReturnsStripe(string currency)
    {
        // Arrange
        var stripeService = new Mock<StripePaymentService>(
            _configurationMock.Object,
            Mock.Of<ILogger<StripePaymentService>>()).Object;

        _serviceProviderMock.Setup(x => x.GetService(typeof(StripePaymentService)))
            .Returns(stripeService);

        // Act
        var result = _factory.GetOptimalPaymentServiceForCurrency(currency);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(PaymentProvider.Stripe, result.Provider);
    }

    [Theory]
    [InlineData("XOF")]
    [InlineData("NGN")]
    [InlineData("GHS")]
    public void GetOptimalPaymentServiceForCurrency_WithAfricanCurrency_ReturnsFlutterwave(string currency)
    {
        // Arrange
        var flutterwaveService = new Mock<FlutterwavePaymentService>(
            _configurationMock.Object,
            Mock.Of<ILogger<FlutterwavePaymentService>>(),
            Mock.Of<HttpClient>()).Object;

        _serviceProviderMock.Setup(x => x.GetService(typeof(FlutterwavePaymentService)))
            .Returns(flutterwaveService);

        // Act
        var result = _factory.GetOptimalPaymentServiceForCurrency(currency);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(PaymentProvider.Flutterwave, result.Provider);
    }

    [Fact]
    public void GetDefaultPaymentService_ReturnsConfiguredDefaultProvider()
    {
        // Arrange
        var stripeService = new Mock<StripePaymentService>(
            _configurationMock.Object,
            Mock.Of<ILogger<StripePaymentService>>()).Object;

        _serviceProviderMock.Setup(x => x.GetService(typeof(StripePaymentService)))
            .Returns(stripeService);

        // Act
        var result = _factory.GetDefaultPaymentService();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(PaymentProvider.Stripe, result.Provider);
    }

    [Fact]
    public void IsProviderAvailable_WithEnabledProvider_ReturnsTrue()
    {
        // Arrange
        var stripeSection = new Mock<IConfigurationSection>();
        stripeSection.Setup(x => x["SecretKey"]).Returns("sk_test_123");
        stripeSection.Setup(x => x["PublishableKey"]).Returns("pk_test_123");
        _configurationMock.Setup(x => x.GetSection("Stripe")).Returns(stripeSection.Object);

        // Act
        var result = _factory.IsProviderAvailable(PaymentProvider.Stripe);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsProviderAvailable_WithDisabledProvider_ReturnsFalse()
    {
        // Arrange
        var paymentSection = new Mock<IConfigurationSection>();
        paymentSection.Setup(x => x["DefaultProvider"]).Returns("Stripe");
        paymentSection.Setup(x => x["EnabledProviders:0"]).Returns("Stripe");
        // PayPal n'est pas dans la liste des providers activés

        _configurationMock.Setup(x => x.GetSection("Payment")).Returns(paymentSection.Object);

        var factoryWithLimitedProviders = new PaymentServiceFactory(
            _serviceProviderMock.Object, 
            _configurationMock.Object, 
            _loggerMock.Object);

        // Act
        var result = factoryWithLimitedProviders.IsProviderAvailable(PaymentProvider.PayPal);

        // Assert
        Assert.False(result);
    }
}

/// <summary>
/// Tests d'intégration pour les services de paiement
/// </summary>
public class PaymentServicesIntegrationTests : IClassFixture<PaymentTestFixture>
{
    private readonly PaymentTestFixture _fixture;

    public PaymentServicesIntegrationTests(PaymentTestFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreatePaymentIntent_WithValidRequest_ReturnsSuccessfulResponse()
    {
        // Arrange
        var factory = _fixture.ServiceProvider.GetRequiredService<IPaymentServiceFactory>();
        var paymentService = factory.GetDefaultPaymentService();

        var request = new CreatePaymentIntentRequest
        {
            Amount = 1500, // 15.00 EUR
            Currency = "EUR",
            UserId = Guid.NewGuid(),
            BookingId = Guid.NewGuid(),
            Description = "Test payment",
            AllowedPaymentMethods = new List<PaymentMethodType> { PaymentMethodType.Card }
        };

        // Act
        var result = await paymentService.CreatePaymentIntentAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.PaymentIntentId);
        Assert.Equal(request.Amount, result.Amount);
        Assert.Equal(request.Currency, result.Currency);
    }

    [Fact]
    public async Task GetPaymentStatus_WithValidPaymentId_ReturnsStatus()
    {
        // Arrange
        var factory = _fixture.ServiceProvider.GetRequiredService<IPaymentServiceFactory>();
        var paymentService = factory.GetDefaultPaymentService();

        // Créer d'abord un paiement
        var createRequest = new CreatePaymentIntentRequest
        {
            Amount = 1000,
            Currency = "EUR",
            UserId = Guid.NewGuid(),
            BookingId = Guid.NewGuid(),
            Description = "Test payment for status check"
        };

        var paymentIntent = await paymentService.CreatePaymentIntentAsync(createRequest);

        // Act
        var result = await paymentService.GetPaymentStatusAsync(paymentIntent.PaymentIntentId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(paymentIntent.PaymentIntentId, result.PaymentId);
        Assert.Equal(createRequest.Amount, result.Amount);
        Assert.Equal(createRequest.Currency, result.Currency);
    }

    [Fact]
    public void ValidateWebhookSignature_WithValidSignature_ReturnsTrue()
    {
        // Arrange
        var factory = _fixture.ServiceProvider.GetRequiredService<IPaymentServiceFactory>();
        var paymentService = factory.GetDefaultPaymentService();

        var payload = "{\"test\": \"data\"}";
        var signature = "valid_signature";
        var secret = "webhook_secret";

        // Act
        var result = paymentService.ValidateWebhookSignature(payload, signature, secret);

        // Assert
        // Note: Le résultat dépend de l'implémentation du service
        // Pour Stripe, cela devrait valider correctement
        // Pour les autres, l'implémentation peut varier
        Assert.IsType<bool>(result);
    }
}

/// <summary>
/// Fixture pour les tests d'intégration des paiements
/// </summary>
public class PaymentTestFixture : IDisposable
{
    public IServiceProvider ServiceProvider { get; private set; }

    public PaymentTestFixture()
    {
        var services = new ServiceCollection();
        
        // Configuration de test
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Payment:DefaultProvider"] = "Stripe",
                ["Payment:EnabledProviders:0"] = "Stripe",
                ["Payment:EnabledProviders:1"] = "PayPal",
                ["Payment:EnabledProviders:2"] = "Flutterwave",
                ["Stripe:SecretKey"] = "sk_test_123",
                ["Stripe:PublishableKey"] = "pk_test_123",
                ["Stripe:WebhookSecret"] = "whsec_test_123",
                ["PayPal:ClientId"] = "test_client_id",
                ["PayPal:ClientSecret"] = "test_client_secret",
                ["PayPal:Environment"] = "sandbox",
                ["Flutterwave:SecretKey"] = "FLWSECK_TEST-123",
                ["Flutterwave:PublicKey"] = "FLWPUBK_TEST-123",
                ["Flutterwave:Environment"] = "sandbox"
            })
            .Build();

        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging();

        // Ajouter les services de paiement
        services.AddPaymentServices(configuration);

        ServiceProvider = services.BuildServiceProvider();
    }

    public void Dispose()
    {
        if (ServiceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}
