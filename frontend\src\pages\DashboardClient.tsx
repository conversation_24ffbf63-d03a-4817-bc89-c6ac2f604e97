import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Search, Star, Heart, MapPin, Clock, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ServiceCard } from '@/components/services/ServiceCard';
import { BookingCard } from '@/components/booking/BookingCard';
import { useServiceStore, useBookingStore } from '@/stores';
import { useAuthStore } from '@/stores/authStore';

export const DashboardClient: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    featuredServices, 
    loadFeaturedServices 
  } = useServiceStore();
  const { 
    clientBookings, 
    getClientBookings 
  } = useBookingStore();

  useEffect(() => {
    loadFeaturedServices();
    if (user) {
      getClientBookings({ status: ['Pending', 'Confirmed', 'InProgress'] });
    }
  }, [user, loadFeaturedServices, getClientBookings]);

  const recentBookings = clientBookings.slice(0, 3);
  const pendingBookings = clientBookings.filter(b => b.status === 'Pending');
  const completedBookings = clientBookings.filter(b => b.status === 'Completed');

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 -mx-6 px-6 py-8 rounded-lg">
        <h1 className="text-3xl font-bold mb-2 text-green-800">
          Bienvenue, {user?.firstName} !
        </h1>
        <p className="text-green-700 mb-6">
          Découvrez des services de qualité avec paiement sécurisé et prestataires vérifiés
        </p>
        
        {/* Client Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Calendar className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-800">{clientBookings.length}</div>
            <p className="text-sm text-green-600">Réservations totales</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <CheckCircle className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-800">{completedBookings.length}</div>
            <p className="text-sm text-blue-600">Services terminés</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-800">{pendingBookings.length}</div>
            <p className="text-sm text-orange-600">En attente</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Actions rapides
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/services" className="block">
              <Button className="h-auto p-6 flex-col space-y-3 w-full bg-green-600 hover:bg-green-700">
                <Search className="h-8 w-8" />
                <span className="text-lg">Parcourir les services</span>
                <span className="text-sm opacity-90">Trouvez le prestataire parfait</span>
              </Button>
            </Link>
            
            <Link to="/bookings" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-green-200 hover:bg-green-50">
                <Calendar className="h-8 w-8 text-green-600" />
                <span className="text-lg">Mes réservations</span>
                <span className="text-sm text-muted-foreground">Gérez vos demandes</span>
              </Button>
            </Link>
            
            <Link to="/profile" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-blue-200 hover:bg-blue-50">
                <Heart className="h-8 w-8 text-blue-600" />
                <span className="text-lg">Mon profil</span>
                <span className="text-sm text-muted-foreground">Paramètres et préférences</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Recent Bookings */}
      {recentBookings.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Mes dernières réservations
              </CardTitle>
              <Link to="/bookings">
                <Button variant="outline" size="sm">
                  Voir tout
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <BookingCard
                  key={booking.id}
                  booking={booking}
                  userRole="Client"
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Featured Services */}
      {featuredServices.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Services recommandés pour vous
              </CardTitle>
              <Link to="/services">
                <Button variant="outline" size="sm">
                  Voir tout
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredServices.slice(0, 3).map((service) => (
                <ServiceCard key={service.id} service={service} />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tips for Clients */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50">
        <CardHeader>
          <CardTitle className="text-green-800">💡 Conseils pour bien utiliser ServiceLink</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-1" />
              <div>
                <p className="font-medium text-green-800">Vérifiez les avis</p>
                <p className="text-sm text-green-600">Consultez les évaluations avant de réserver</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <MapPin className="h-5 w-5 text-blue-600 mt-1" />
              <div>
                <p className="font-medium text-blue-800">Précisez votre localisation</p>
                <p className="text-sm text-blue-600">Pour trouver des prestataires près de chez vous</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
