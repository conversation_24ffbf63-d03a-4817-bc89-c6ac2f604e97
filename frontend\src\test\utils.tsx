import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'
import { AuthProvider } from '../contexts/AuthContext'

// Configuration du QueryClient pour les tests
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

// Interface pour les options de rendu personnalisées
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
  initialEntries?: string[]
  user?: {
    id: string
    email: string
    firstName: string
    lastName: string
    role: string
    isEmailConfirmed: boolean
  } | null
}

// Wrapper personnalisé avec tous les providers nécessaires
const AllTheProviders = ({
  children,
  queryClient,
  initialEntries = ['/'],
  user = null,
}: {
  children: React.ReactNode
  queryClient: QueryClient
  initialEntries?: string[]
  user?: CustomRenderOptions['user']
}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider initialUser={user}>
          {children}
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  )
}

// Fonction de rendu personnalisée
const customRender = (
  ui: ReactElement,
  {
    queryClient = createTestQueryClient(),
    initialEntries,
    user,
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <AllTheProviders
      queryClient={queryClient}
      initialEntries={initialEntries}
      user={user}
    >
      {children}
    </AllTheProviders>
  )

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  }
}

// Fonction pour créer un utilisateur de test
export const createTestUser = (overrides = {}) => ({
  id: '1',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'User',
  isEmailConfirmed: true,
  ...overrides,
})

// Fonction pour créer un admin de test
export const createTestAdmin = (overrides = {}) => ({
  id: '1',
  email: '<EMAIL>',
  firstName: 'Admin',
  lastName: 'User',
  role: 'Admin',
  isEmailConfirmed: true,
  ...overrides,
})

// Fonction pour attendre que les requêtes se terminent
export const waitForLoadingToFinish = () =>
  new Promise((resolve) => setTimeout(resolve, 0))

// Mock pour localStorage
export const mockLocalStorage = () => {
  const store: Record<string, string> = {}

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value
    },
    removeItem: (key: string) => {
      delete store[key]
    },
    clear: () => {
      Object.keys(store).forEach((key) => delete store[key])
    },
  }
}

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }
