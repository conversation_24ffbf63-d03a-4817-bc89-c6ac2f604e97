using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using ServiceLink.Infrastructure.Configuration;
using System.Security.Claims;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour gérer les paiements
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PaymentController : ControllerBase
{
    private readonly IPaymentServiceFactory _paymentServiceFactory;
    private readonly ILogger<PaymentController> _logger;

    public PaymentController(
        IPaymentServiceFactory paymentServiceFactory,
        ILogger<PaymentController> logger)
    {
        _paymentServiceFactory = paymentServiceFactory;
        _logger = logger;
    }

    /// <summary>
    /// Crée une intention de paiement
    /// </summary>
    /// <param name="request">Détails de la demande de paiement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Intention de paiement créée</returns>
    [HttpPost("intent")]
    public async Task<IActionResult> CreatePaymentIntent(
        [FromBody] CreatePaymentIntentRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            request.UserId = userId;

            _logger.LogInformation("Création d'une intention de paiement pour l'utilisateur {UserId}, montant: {Amount} {Currency}", 
                userId, request.Amount, request.Currency);

            // Sélectionner le service de paiement optimal
            IPaymentService paymentService;
            if (request.AllowedPaymentMethods.Any())
            {
                var optimalMethod = request.AllowedPaymentMethods.First();
                paymentService = _paymentServiceFactory.GetOptimalPaymentServiceForMethod(optimalMethod);
            }
            else
            {
                paymentService = _paymentServiceFactory.GetOptimalPaymentServiceForCurrency(request.Currency);
            }

            var result = await paymentService.CreatePaymentIntentAsync(request, cancellationToken);

            _logger.LogInformation("Intention de paiement créée: {PaymentIntentId} via {Provider}", 
                result.PaymentIntentId, paymentService.Provider);

            return Ok(new
            {
                success = true,
                data = result,
                provider = paymentService.Provider.ToString()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de l'intention de paiement");
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Confirme un paiement
    /// </summary>
    /// <param name="request">Détails de confirmation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de la confirmation</returns>
    [HttpPost("confirm")]
    public async Task<IActionResult> ConfirmPayment(
        [FromBody] ConfirmPaymentRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Confirmation du paiement {PaymentIntentId} via {Provider}", 
                request.PaymentIntentId, request.Provider);

            var paymentService = _paymentServiceFactory.CreatePaymentService(request.Provider);
            var result = await paymentService.ConfirmPaymentAsync(
                request.PaymentIntentId, 
                request.PaymentMethodId, 
                cancellationToken);

            _logger.LogInformation("Paiement {PaymentIntentId} confirmé: {Success}", 
                request.PaymentIntentId, result.Success);

            return Ok(new
            {
                success = result.Success,
                data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la confirmation du paiement {PaymentIntentId}", 
                request.PaymentIntentId);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Récupère le statut d'un paiement
    /// </summary>
    /// <param name="paymentId">ID du paiement</param>
    /// <param name="provider">Provider de paiement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Statut du paiement</returns>
    [HttpGet("{paymentId}/status")]
    public async Task<IActionResult> GetPaymentStatus(
        [FromRoute] string paymentId,
        [FromQuery] PaymentProvider provider,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Récupération du statut du paiement {PaymentId} via {Provider}", 
                paymentId, provider);

            var paymentService = _paymentServiceFactory.CreatePaymentService(provider);
            var result = await paymentService.GetPaymentStatusAsync(paymentId, cancellationToken);

            return Ok(new
            {
                success = true,
                data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du statut du paiement {PaymentId}", paymentId);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Effectue un remboursement
    /// </summary>
    /// <param name="request">Détails du remboursement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du remboursement</returns>
    [HttpPost("refund")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<IActionResult> RefundPayment(
        [FromBody] RefundPaymentRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Remboursement du paiement {PaymentId} via {Provider}", 
                request.PaymentId, request.Provider);

            var paymentService = _paymentServiceFactory.CreatePaymentService(request.Provider);
            
            var refundRequest = new Application.Interfaces.RefundRequest
            {
                PaymentId = request.PaymentId,
                Amount = request.Amount,
                Reason = request.Reason,
                Metadata = request.Metadata ?? new Dictionary<string, string>()
            };

            var result = await paymentService.RefundPaymentAsync(refundRequest, cancellationToken);

            _logger.LogInformation("Remboursement {PaymentId} traité: {Success}", 
                request.PaymentId, result.Success);

            return Ok(new
            {
                success = result.Success,
                data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du remboursement du paiement {PaymentId}", request.PaymentId);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Récupère l'historique des transactions d'un utilisateur
    /// </summary>
    /// <param name="limit">Nombre maximum de transactions</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des transactions</returns>
    [HttpGet("transactions")]
    public async Task<IActionResult> GetUserTransactions(
        [FromQuery] int limit = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("Récupération des transactions pour l'utilisateur {UserId}", userId);

            var allTransactions = new List<PaymentTransactionDto>();

            // Récupérer les transactions de tous les providers
            var availableServices = _paymentServiceFactory.GetAvailablePaymentServices();
            
            foreach (var service in availableServices)
            {
                try
                {
                    var transactions = await service.GetUserTransactionsAsync(userId, limit, cancellationToken);
                    allTransactions.AddRange(transactions);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Erreur lors de la récupération des transactions pour {Provider}", 
                        service.Provider);
                }
            }

            // Trier par date décroissante
            var sortedTransactions = allTransactions
                .OrderByDescending(t => t.CreatedAt)
                .Take(limit)
                .ToList();

            return Ok(new
            {
                success = true,
                data = sortedTransactions,
                total = sortedTransactions.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des transactions utilisateur");
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Récupère les providers de paiement disponibles
    /// </summary>
    /// <returns>Liste des providers disponibles</returns>
    [HttpGet("providers")]
    public IActionResult GetAvailableProviders()
    {
        try
        {
            var stats = _paymentServiceFactory.GetProvidersStats();
            
            return Ok(new
            {
                success = true,
                data = stats
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des providers");
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Récupère les méthodes de paiement supportées par un provider
    /// </summary>
    /// <param name="provider">Provider de paiement</param>
    /// <returns>Méthodes supportées</returns>
    [HttpGet("providers/{provider}/methods")]
    public IActionResult GetSupportedMethods([FromRoute] PaymentProvider provider)
    {
        try
        {
            var supportedMethods = provider.GetSupportedMethods();
            
            return Ok(new
            {
                success = true,
                provider = provider.ToString(),
                methods = supportedMethods.Select(m => new
                {
                    type = m.ToString(),
                    description = m.GetDescription()
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des méthodes pour {Provider}", provider);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    #region Méthodes privées

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Utilisateur non authentifié");
        }
        return userId;
    }

    #endregion
}

/// <summary>
/// Demande de confirmation de paiement
/// </summary>
public class ConfirmPaymentRequest
{
    /// <summary>
    /// ID de l'intention de paiement
    /// </summary>
    public string PaymentIntentId { get; set; } = string.Empty;

    /// <summary>
    /// ID de la méthode de paiement
    /// </summary>
    public string PaymentMethodId { get; set; } = string.Empty;

    /// <summary>
    /// Provider de paiement
    /// </summary>
    public PaymentProvider Provider { get; set; }
}

/// <summary>
/// Demande de remboursement
/// </summary>
public class RefundPaymentRequest
{
    /// <summary>
    /// ID du paiement à rembourser
    /// </summary>
    public string PaymentId { get; set; } = string.Empty;

    /// <summary>
    /// Provider de paiement
    /// </summary>
    public PaymentProvider Provider { get; set; }

    /// <summary>
    /// Montant à rembourser (null = remboursement total)
    /// </summary>
    public long? Amount { get; set; }

    /// <summary>
    /// Raison du remboursement
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// Métadonnées additionnelles
    /// </summary>
    public Dictionary<string, string>? Metadata { get; set; }
}
