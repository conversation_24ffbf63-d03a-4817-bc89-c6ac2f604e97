export interface ServiceCategory {
  id: string
  name: string
  description: string
  icon?: string
  parentId?: string
  children?: ServiceCategory[]
  serviceCount: number
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
}

export interface ServiceProvider {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  avatar?: string
  bio?: string
  rating: number
  reviewCount: number
  completedBookings: number
  responseTime: string
  isVerified: boolean
  isOnline: boolean
  joinedAt: string
  location?: {
    address: string
    city: string
    postalCode: string
    latitude: number
    longitude: number
  }
  specialties: string[]
  languages: string[]
  certifications: string[]
}

export interface Service {
  id: string
  name: string
  description: string
  shortDescription?: string
  categoryId: string
  category?: ServiceCategory
  providerId: string
  provider?: ServiceProvider
  basePrice: number
  pricingUnit: 'Fixed' | 'Hourly' | 'Daily'
  currency: string
  duration?: number // en minutes
  isActive: boolean
  isUrgent: boolean
  isFeatured: boolean
  rating: number
  reviewCount: number
  bookingCount: number
  images: string[]
  tags: string[]
  requirements?: string[]
  included?: string[]
  excluded?: string[]
  location: {
    address: string
    city: string
    postalCode: string
    latitude: number
    longitude: number
    serviceRadius: number // rayon de service en km
  }
  availability: {
    monday: boolean
    tuesday: boolean
    wednesday: boolean
    thursday: boolean
    friday: boolean
    saturday: boolean
    sunday: boolean
    startTime: string // HH:mm
    endTime: string // HH:mm
    timeZone: string
  }
  createdAt: string
  updatedAt: string
}

export interface ServiceFilters {
  categoryId?: string
  location?: string
  minPrice?: number
  maxPrice?: number
  rating?: number
  availability?: string // 'today', 'tomorrow', 'this_week', 'custom'
  urgent?: boolean
  radius?: number // en km
  latitude?: number
  longitude?: number
  tags?: string[]
  pricingUnit?: 'Fixed' | 'Hourly' | 'Daily'
  sortBy?: 'price' | 'rating' | 'distance' | 'popularity' | 'newest'
  sortDirection?: 'asc' | 'desc'
}

export interface CreateServiceRequest {
  name: string
  description: string
  shortDescription?: string
  categoryId: string
  basePrice: number
  pricingUnit: 'Fixed' | 'Hourly' | 'Daily'
  currency: string
  duration?: number
  isUrgent?: boolean
  images?: string[]
  tags?: string[]
  requirements?: string[]
  included?: string[]
  excluded?: string[]
  location: {
    address: string
    city: string
    postalCode: string
    latitude: number
    longitude: number
    serviceRadius: number
  }
  availability: {
    monday: boolean
    tuesday: boolean
    wednesday: boolean
    thursday: boolean
    friday: boolean
    saturday: boolean
    sunday: boolean
    startTime: string
    endTime: string
    timeZone: string
  }
}

export interface UpdateServiceRequest extends Partial<CreateServiceRequest> {
  isActive?: boolean
  isFeatured?: boolean
}

export interface ServiceReview {
  id: string
  serviceId: string
  bookingId: string
  clientId: string
  client?: {
    firstName: string
    lastName: string
    avatar?: string
  }
  rating: number
  comment?: string
  images?: string[]
  isVerified: boolean
  response?: {
    comment: string
    createdAt: string
  }
  createdAt: string
  updatedAt: string
}

export interface ServiceAvailability {
  id: string
  serviceId: string
  date: string // YYYY-MM-DD
  startTime: string // HH:mm
  endTime: string // HH:mm
  isAvailable: boolean
  isBooked: boolean
  price?: number // prix spécifique pour ce créneau
  notes?: string
}

export interface ServiceBookingSlot {
  date: string
  startTime: string
  endTime: string
  isAvailable: boolean
  price: number
  duration: number
}
