// Système de permissions basé sur le PRD ServiceLink
export type UserRole = 
  | 'Admin Global'
  | 'Manager' 
  | 'Support'
  | 'Superviseur'
  | 'Client'
  | 'Prestataire'
  | 'Admin'      // Alias pour Admin Global
  | 'Provider'   // Alias pour Prestataire
  | 'ServiceProvider'
  | 'Supervisor'; // Alias pour Superviseur

export interface Permission {
  id: string;
  name: string;
  description: string;
}

export interface RolePermissions {
  role: UserRole;
  description: string;
  permissions: Permission[];
}

// Définition des permissions selon le PRD
export const PERMISSIONS = {
  // Admin Global
  PLATFORM_CONFIG: { id: 'platform_config', name: 'Configuration plateforme complète', description: 'Accès à toute la configuration' },
  MANAGE_ACCOUNTS: { id: 'manage_accounts', name: 'Gestion des comptes et rôles', description: 'Créer/modifier/supprimer comptes' },
  MANAGE_SERVICES: { id: 'manage_services', name: 'Gestion types et catégories de services', description: 'Configuration des services' },
  SUPERVISE_PAYMENTS: { id: 'supervise_payments', name: 'Supervision paiements et finances', description: 'Accès aux finances' },

  // Manager
  SUPPORT_FUNCTIONS: { id: 'support_functions', name: 'Fonctionnalités du support', description: 'Accès aux outils support' },
  MANAGE_COMPLAINTS: { id: 'manage_complaints', name: 'Gestion réclamations et litiges', description: 'Traiter réclamations' },
  ADVANCED_REPORTS: { id: 'advanced_reports', name: 'Accès rapports avancés', description: 'Rapports détaillés' },

  // Support
  GLOBAL_STATS: { id: 'global_stats', name: 'Accès statistiques globales', description: 'Voir statistiques' },
  CHAT_TICKETING: { id: 'chat_ticketing', name: 'Gestion chat et ticketing', description: 'Support client' },
  CLIENT_SUPPORT: { id: 'client_support', name: 'Support client', description: 'Assistance utilisateurs' },

  // Superviseur
  VERIFY_DOCUMENTS: { id: 'verify_documents', name: 'Vérification et validation documents', description: 'Valider documents' },
  SUSPEND_ACCOUNTS: { id: 'suspend_accounts', name: 'Suspension comptes non-conformes', description: 'Suspendre comptes' },

  // Client
  SEARCH_BOOK: { id: 'search_book', name: 'Recherche et réservation de services', description: 'Réserver services' },
  MANAGE_PROFILE: { id: 'manage_profile', name: 'Gestion profil personnel', description: 'Modifier profil' },
  RATE_PROVIDERS: { id: 'rate_providers', name: 'Évaluation des prestataires', description: 'Noter prestataires' },

  // Prestataire
  MANAGE_PROFESSIONAL_PROFILE: { id: 'manage_professional_profile', name: 'Gestion profil professionnel', description: 'Profil pro' },
  MANAGE_CALENDAR: { id: 'manage_calendar', name: 'Gestion calendrier et disponibilités', description: 'Planning' },
  FINANCIAL_TRACKING: { id: 'financial_tracking', name: 'Suivi financier et paiements', description: 'Finances' },
} as const;

// Configuration des rôles selon le PRD
export const ROLE_PERMISSIONS: Record<string, RolePermissions> = {
  'Admin Global': {
    role: 'Admin Global',
    description: 'Super Administrateur avec tous les droits',
    permissions: [
      PERMISSIONS.PLATFORM_CONFIG,
      PERMISSIONS.MANAGE_ACCOUNTS,
      PERMISSIONS.MANAGE_SERVICES,
      PERMISSIONS.SUPERVISE_PAYMENTS,
    ]
  },
  'Admin': {
    role: 'Admin',
    description: 'Super Administrateur avec tous les droits',
    permissions: [
      PERMISSIONS.PLATFORM_CONFIG,
      PERMISSIONS.MANAGE_ACCOUNTS,
      PERMISSIONS.MANAGE_SERVICES,
      PERMISSIONS.SUPERVISE_PAYMENTS,
    ]
  },
  'Manager': {
    role: 'Manager',
    description: 'Gestionnaire avec droits étendus',
    permissions: [
      PERMISSIONS.SUPPORT_FUNCTIONS,
      PERMISSIONS.MANAGE_COMPLAINTS,
      PERMISSIONS.ADVANCED_REPORTS,
    ]
  },
  'Support': {
    role: 'Support',
    description: 'Support client',
    permissions: [
      PERMISSIONS.GLOBAL_STATS,
      PERMISSIONS.CHAT_TICKETING,
      PERMISSIONS.CLIENT_SUPPORT,
    ]
  },
  'Superviseur': {
    role: 'Superviseur',
    description: 'Superviseur de qualité',
    permissions: [
      PERMISSIONS.VERIFY_DOCUMENTS,
      PERMISSIONS.SUSPEND_ACCOUNTS,
    ]
  },
  'Supervisor': {
    role: 'Supervisor',
    description: 'Superviseur de qualité',
    permissions: [
      PERMISSIONS.VERIFY_DOCUMENTS,
      PERMISSIONS.SUSPEND_ACCOUNTS,
    ]
  },
  'Client': {
    role: 'Client',
    description: 'Utilisateur final demandeur de services',
    permissions: [
      PERMISSIONS.SEARCH_BOOK,
      PERMISSIONS.MANAGE_PROFILE,
      PERMISSIONS.RATE_PROVIDERS,
    ]
  },
  'Prestataire': {
    role: 'Prestataire',
    description: 'Fournisseur de services',
    permissions: [
      PERMISSIONS.MANAGE_PROFESSIONAL_PROFILE,
      PERMISSIONS.MANAGE_CALENDAR,
      PERMISSIONS.FINANCIAL_TRACKING,
    ]
  },
  'Provider': {
    role: 'Provider',
    description: 'Fournisseur de services',
    permissions: [
      PERMISSIONS.MANAGE_PROFESSIONAL_PROFILE,
      PERMISSIONS.MANAGE_CALENDAR,
      PERMISSIONS.FINANCIAL_TRACKING,
    ]
  },
  'ServiceProvider': {
    role: 'ServiceProvider',
    description: 'Fournisseur de services',
    permissions: [
      PERMISSIONS.MANAGE_PROFESSIONAL_PROFILE,
      PERMISSIONS.MANAGE_CALENDAR,
      PERMISSIONS.FINANCIAL_TRACKING,
    ]
  },
};

// Fonctions utilitaires
export function getUserPermissions(role: string): Permission[] {
  const roleConfig = ROLE_PERMISSIONS[role];
  return roleConfig ? roleConfig.permissions : [];
}

export function hasPermission(userRole: string, permissionId: string): boolean {
  const permissions = getUserPermissions(userRole);
  return permissions.some(p => p.id === permissionId);
}

export function canAccessRoute(userRole: string, requiredPermissions: string[]): boolean {
  if (requiredPermissions.length === 0) return true;
  return requiredPermissions.some(permission => hasPermission(userRole, permission));
}

// Mapping des rôles pour normalisation
export function normalizeRole(role: string): string {
  const roleMap: Record<string, string> = {
    'Admin': 'Admin Global',
    'Provider': 'Prestataire',
    'ServiceProvider': 'Prestataire',
    'Supervisor': 'Superviseur',
  };
  
  return roleMap[role] || role;
}
