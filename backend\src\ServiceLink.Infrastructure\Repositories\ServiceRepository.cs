using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.ValueObjects;
using ServiceLink.Infrastructure.Data;

namespace ServiceLink.Infrastructure.Repositories;

/// <summary>
/// Implémentation du repository pour les services
/// </summary>
public class ServiceRepository : Repository<Service>, IServiceRepository
{
    public ServiceRepository(ServiceLinkDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient les services d'un prestataire
    /// </summary>
    public async Task<IEnumerable<Service>> GetByProviderIdAsync(Guid providerId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Service>()
            .Where(s => s.ProviderId == providerId)
            .Include(s => s.Provider)
            .Include(s => s.Category)
            .Include(s => s.Availabilities)
            .OrderBy(s => s.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les services d'une catégorie
    /// </summary>
    public async Task<IEnumerable<Service>> GetByCategoryIdAsync(Guid categoryId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Service>()
            .Where(s => s.CategoryId == categoryId && s.IsActive)
            .Include(s => s.Provider)
            .Include(s => s.Category)
            .OrderByDescending(s => s.AverageRating)
            .ThenBy(s => s.BasePrice)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Recherche des services par critères
    /// </summary>
    public async Task<IEnumerable<Service>> SearchAsync(
        string? searchTerm, 
        Guid? categoryId, 
        ServiceAddress? location, 
        double? radiusKm, 
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Service>()
            .Where(s => s.IsActive)
            .Include(s => s.Provider)
            .Include(s => s.Category)
            .AsQueryable();

        // Filtrage par terme de recherche
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchLower = searchTerm.ToLowerInvariant();
            query = query.Where(s => 
                s.Name.ToLower().Contains(searchLower) ||
                s.Description.ToLower().Contains(searchLower) ||
                s.Tags.ToLower().Contains(searchLower) ||
                s.Category!.Name.ToLower().Contains(searchLower));
        }

        // Filtrage par catégorie
        if (categoryId.HasValue)
        {
            query = query.Where(s => s.CategoryId == categoryId.Value);
        }

        // Filtrage par localisation (simulation - en production, utiliser une extension spatiale)
        if (location != null && radiusKm.HasValue && location.HasCoordinates)
        {
            // Pour une implémentation complète, utiliser PostGIS ou une extension spatiale
            // Ici, on fait une approximation simple
            query = query.Where(s => s.ServiceRadiusKm >= radiusKm.Value);
        }

        return await query
            .OrderByDescending(s => s.AverageRating)
            .ThenBy(s => s.BasePrice)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Vérifie la disponibilité d'un service
    /// </summary>
    public async Task<bool> IsAvailableAsync(Guid serviceId, DateTime dateTime, int durationMinutes, CancellationToken cancellationToken = default)
    {
        var service = await Context.Set<Service>()
            .Include(s => s.Availabilities)
            .FirstOrDefaultAsync(s => s.Id == serviceId, cancellationToken);

        if (service == null || !service.IsActive)
            return false;

        // Vérifier les contraintes de préavis
        var minAdvanceTime = DateTime.UtcNow.AddHours(service.MinAdvanceHours);
        var maxAdvanceTime = DateTime.UtcNow.AddDays(service.MaxAdvanceDays);

        if (dateTime < minAdvanceTime || dateTime > maxAdvanceTime)
            return false;

        // Vérifier la durée
        if (durationMinutes < service.MinDurationMinutes || durationMinutes > service.MaxDurationMinutes)
            return false;

        // Vérifier les disponibilités
        var endDateTime = dateTime.AddMinutes(durationMinutes);
        var availabilities = service.Availabilities
            .Where(a => a.IsActive && a.IsValidForDateTime(dateTime))
            .ToList();

        if (!availabilities.Any())
            return false;

        // Vérifier les conflits avec les réservations existantes
        var hasConflict = await Context.Set<Booking>()
            .AnyAsync(b => b.ServiceId == serviceId &&
                          b.Status != Domain.Enums.BookingStatus.Cancelled &&
                          b.Status != Domain.Enums.BookingStatus.Rejected &&
                          b.Status != Domain.Enums.BookingStatus.Expired &&
                          b.ScheduledDate < endDateTime &&
                          b.ScheduledEndDate > dateTime, cancellationToken);

        return !hasConflict;
    }

    /// <summary>
    /// Obtient l'adresse du prestataire
    /// </summary>
    public async Task<ServiceAddress?> GetProviderAddressAsync(Guid providerId, CancellationToken cancellationToken = default)
    {
        // TODO: Implémenter une fois que l'entité User aura les propriétés d'adresse
        // ou créer une table d'adresses séparée
        await Task.Delay(1, cancellationToken);
        return null;
    }

    /// <summary>
    /// Obtient les créneaux disponibles
    /// </summary>
    public async Task<IEnumerable<DateTime>> GetAvailableSlotsAsync(
        Guid serviceId, 
        DateTime startDate, 
        DateTime endDate, 
        int durationMinutes, 
        CancellationToken cancellationToken = default)
    {
        var service = await Context.Set<Service>()
            .Include(s => s.Availabilities)
            .FirstOrDefaultAsync(s => s.Id == serviceId, cancellationToken);

        if (service == null || !service.IsActive)
            return Enumerable.Empty<DateTime>();

        var availableSlots = new List<DateTime>();
        var currentDate = startDate.Date;

        while (currentDate <= endDate.Date)
        {
            var dayAvailabilities = service.Availabilities
                .Where(a => a.IsActive && a.IsValidForDateTime(currentDate))
                .ToList();

            foreach (var availability in dayAvailabilities)
            {
                var slotStart = currentDate.Add(availability.StartTime.ToTimeSpan());
                var slotEnd = currentDate.Add(availability.EndTime.ToTimeSpan());

                // Générer des créneaux de la durée demandée
                var currentSlot = slotStart;
                while (currentSlot.AddMinutes(durationMinutes) <= slotEnd)
                {
                    if (await IsAvailableAsync(serviceId, currentSlot, durationMinutes, cancellationToken))
                    {
                        availableSlots.Add(currentSlot);
                    }
                    currentSlot = currentSlot.AddMinutes(30); // Créneaux de 30 minutes
                }
            }

            currentDate = currentDate.AddDays(1);
        }

        return availableSlots.OrderBy(s => s);
    }

    /// <summary>
    /// Obtient les services avec pagination
    /// </summary>
    public async Task<(IEnumerable<Service> Items, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        string? searchTerm = null,
        Guid? categoryId = null,
        Guid? providerId = null,
        bool? isActive = null,
        ServiceAddress? location = null,
        double? radiusKm = null,
        string sortBy = "Name",
        string sortDirection = "asc",
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Service>()
            .Include(s => s.Provider)
            .Include(s => s.Category)
            .AsQueryable();

        // Filtres
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchLower = searchTerm.ToLowerInvariant();
            query = query.Where(s => 
                s.Name.ToLower().Contains(searchLower) ||
                s.Description.ToLower().Contains(searchLower) ||
                s.Tags.ToLower().Contains(searchLower));
        }

        if (categoryId.HasValue)
            query = query.Where(s => s.CategoryId == categoryId.Value);

        if (providerId.HasValue)
            query = query.Where(s => s.ProviderId == providerId.Value);

        if (isActive.HasValue)
            query = query.Where(s => s.IsActive == isActive.Value);

        // Tri
        query = sortBy.ToLowerInvariant() switch
        {
            "name" => sortDirection.ToLowerInvariant() == "asc" 
                ? query.OrderBy(s => s.Name)
                : query.OrderByDescending(s => s.Name),
            "price" => sortDirection.ToLowerInvariant() == "asc"
                ? query.OrderBy(s => s.BasePrice)
                : query.OrderByDescending(s => s.BasePrice),
            "rating" => sortDirection.ToLowerInvariant() == "asc"
                ? query.OrderBy(s => s.AverageRating)
                : query.OrderByDescending(s => s.AverageRating),
            "createdat" => sortDirection.ToLowerInvariant() == "asc"
                ? query.OrderBy(s => s.CreatedAt)
                : query.OrderByDescending(s => s.CreatedAt),
            _ => query.OrderBy(s => s.Name)
        };

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    /// <summary>
    /// Obtient les services populaires
    /// </summary>
    public async Task<IEnumerable<Service>> GetPopularServicesAsync(int limit = 10, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Service>()
            .Where(s => s.IsActive)
            .Include(s => s.Provider)
            .Include(s => s.Category)
            .OrderByDescending(s => s.TotalBookings)
            .ThenByDescending(s => s.AverageRating)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les services recommandés pour un utilisateur
    /// </summary>
    public async Task<IEnumerable<Service>> GetRecommendedServicesAsync(Guid userId, int limit = 10, CancellationToken cancellationToken = default)
    {
        // Logique de recommandation basée sur l'historique des réservations
        var userBookings = await Context.Set<Booking>()
            .Where(b => b.ClientId == userId)
            .Select(b => b.ServiceId)
            .ToListAsync(cancellationToken);

        var userCategories = await Context.Set<Service>()
            .Where(s => userBookings.Contains(s.Id))
            .Select(s => s.CategoryId)
            .Distinct()
            .ToListAsync(cancellationToken);

        return await Context.Set<Service>()
            .Where(s => s.IsActive && userCategories.Contains(s.CategoryId))
            .Include(s => s.Provider)
            .Include(s => s.Category)
            .OrderByDescending(s => s.AverageRating)
            .ThenByDescending(s => s.TotalBookings)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient un service avec toutes ses relations
    /// </summary>
    public override async Task<Service?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Service>()
            .Include(s => s.Provider)
            .Include(s => s.Category)
            .Include(s => s.Availabilities)
            .Include(s => s.Reviews)
                .ThenInclude(r => r.Client)
            .FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }
}
