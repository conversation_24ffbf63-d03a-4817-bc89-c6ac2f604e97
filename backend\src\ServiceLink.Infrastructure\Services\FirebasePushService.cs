using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Service d'envoi de push notifications via Firebase Cloud Messaging
/// </summary>
public class FirebasePushService : IExternalNotificationService
{
    private readonly ILogger<FirebasePushService> _logger;
    private readonly FirebaseSettings _settings;
    private readonly INotificationTemplateService _templateService;
    private readonly FirebaseMessaging _messaging;

    public FirebasePushService(
        ILogger<FirebasePushService> logger,
        IConfiguration configuration,
        INotificationTemplateService templateService)
    {
        _logger = logger;
        _settings = configuration.GetSection("Firebase").Get<FirebaseSettings>() ?? new FirebaseSettings();
        _templateService = templateService;

        // Initialisation de Firebase Admin SDK
        if (!string.IsNullOrEmpty(_settings.ServiceAccountKeyPath) && File.Exists(_settings.ServiceAccountKeyPath))
        {
            var credential = GoogleCredential.FromFile(_settings.ServiceAccountKeyPath);
            var app = FirebaseApp.Create(new AppOptions()
            {
                Credential = credential,
                ProjectId = _settings.ProjectId
            });
            
            _messaging = FirebaseMessaging.GetMessaging(app);
        }
        else if (!string.IsNullOrEmpty(_settings.ServiceAccountKeyJson))
        {
            var credential = GoogleCredential.FromJson(_settings.ServiceAccountKeyJson);
            var app = FirebaseApp.Create(new AppOptions()
            {
                Credential = credential,
                ProjectId = _settings.ProjectId
            });
            
            _messaging = FirebaseMessaging.GetMessaging(app);
        }
        else
        {
            throw new InvalidOperationException("Configuration Firebase manquante. Veuillez fournir ServiceAccountKeyPath ou ServiceAccountKeyJson.");
        }
    }

    /// <inheritdoc />
    public ExternalNotificationType ServiceType => ExternalNotificationType.Push;

    /// <inheritdoc />
    public async Task<ExternalNotificationResult> SendNotificationAsync(
        ExternalNotificationRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi de push notification via Firebase vers {Recipients}", string.Join(", ", request.Recipients));

            // Validation des destinataires (tokens FCM)
            var validTokens = request.Recipients.Where(ValidateDestination).ToList();
            if (!validTokens.Any())
            {
                return new ExternalNotificationResult
                {
                    Success = false,
                    Type = ServiceType,
                    Status = ExternalNotificationStatus.Failed,
                    ErrorMessage = "Aucun token FCM valide",
                    FailedRecipients = request.Recipients
                };
            }

            // Préparation du contenu
            var title = request.Subject;
            var body = request.Content;

            // Utilisation d'un template si spécifié
            if (!string.IsNullOrEmpty(request.TemplateName))
            {
                var template = await _templateService.GetTemplateAsync(request.TemplateName, request.Language);
                if (template != null)
                {
                    var subjectTemplate = new NotificationTemplate
                    {
                        Id = template.Id,
                        Name = template.Name,
                        Type = template.Type,
                        Language = template.Language,
                        Subject = template.Subject,
                        Content = template.Subject,
                        Variables = template.Variables,
                        IsActive = template.IsActive,
                        CreatedAt = template.CreatedAt,
                        UpdatedAt = template.UpdatedAt,
                        Version = template.Version
                    };
                    title = _templateService.RenderTemplate(subjectTemplate, request.TemplateData);
                    body = _templateService.RenderTemplate(template, request.TemplateData);
                }
            }

            var successfulRecipients = new List<string>();
            var failedRecipients = new List<string>();
            var messageIds = new List<string>();

            if (validTokens.Count == 1)
            {
                // Envoi à un seul destinataire
                var message = CreateMessage(validTokens.First(), title, body, request);
                
                try
                {
                    var messageId = await _messaging.SendAsync(message, cancellationToken);
                    messageIds.Add(messageId);
                    successfulRecipients.Add(validTokens.First());
                    
                    _logger.LogDebug("Push notification envoyée avec succès. MessageId: {MessageId}", messageId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de l'envoi de push notification à {Token}", validTokens.First());
                    failedRecipients.Add(validTokens.First());
                }
            }
            else
            {
                // Envoi en lot (multicast)
                var multicastMessage = CreateMulticastMessage(validTokens, title, body, request);
                
                try
                {
                    var response = await _messaging.SendEachForMulticastAsync(multicastMessage, cancellationToken);
                    
                    for (int i = 0; i < response.Responses.Count; i++)
                    {
                        var sendResponse = response.Responses[i];
                        var token = validTokens[i];
                        
                        if (sendResponse.IsSuccess)
                        {
                            messageIds.Add(sendResponse.MessageId);
                            successfulRecipients.Add(token);
                        }
                        else
                        {
                            failedRecipients.Add(token);
                            _logger.LogWarning("Échec d'envoi push notification à {Token}: {Error}", 
                                token, sendResponse.Exception?.Message);
                        }
                    }
                    
                    _logger.LogInformation("Push notifications envoyées: {Success}/{Total}", 
                        response.SuccessCount, response.Responses.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de l'envoi multicast de push notifications");
                    failedRecipients.AddRange(validTokens);
                }
            }

            if (successfulRecipients.Any())
            {
                return new ExternalNotificationResult
                {
                    Success = true,
                    NotificationId = messageIds.FirstOrDefault() ?? Guid.NewGuid().ToString(),
                    Type = ServiceType,
                    Status = ExternalNotificationStatus.Sent,
                    SentAt = DateTime.UtcNow,
                    SuccessfulRecipients = successfulRecipients,
                    FailedRecipients = failedRecipients,
                    ProviderData = new Dictionary<string, object>
                    {
                        ["MessageIds"] = messageIds,
                        ["TotalMessages"] = messageIds.Count,
                        ["Platform"] = "FCM"
                    }
                };
            }
            else
            {
                return new ExternalNotificationResult
                {
                    Success = false,
                    Type = ServiceType,
                    Status = ExternalNotificationStatus.Failed,
                    ErrorMessage = "Échec d'envoi à tous les destinataires",
                    FailedRecipients = failedRecipients
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de push notification via Firebase");
            
            return new ExternalNotificationResult
            {
                Success = false,
                Type = ServiceType,
                Status = ExternalNotificationStatus.Failed,
                ErrorMessage = ex.Message,
                FailedRecipients = request.Recipients
            };
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ExternalNotificationResult>> SendBulkNotificationsAsync(
        IEnumerable<ExternalNotificationRequest> requests, 
        CancellationToken cancellationToken = default)
    {
        var results = new List<ExternalNotificationResult>();
        var requestList = requests.ToList();

        _logger.LogInformation("Envoi en lot de {Count} push notifications via Firebase", requestList.Count);

        // Firebase supporte l'envoi en lot efficacement
        foreach (var request in requestList)
        {
            var result = await SendNotificationAsync(request, cancellationToken);
            results.Add(result);

            // Petite pause pour éviter le rate limiting
            if (requestList.Count > 20)
            {
                await Task.Delay(50, cancellationToken);
            }
        }

        return results;
    }

    /// <inheritdoc />
    public async Task<ExternalNotificationStatus> GetNotificationStatusAsync(
        string notificationId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Vérification du statut de la push notification {NotificationId} via Firebase", notificationId);

            // Firebase ne fournit pas d'API directe pour vérifier le statut d'un message
            // En production, utiliser Firebase Analytics ou des webhooks personnalisés
            await Task.Delay(1, cancellationToken);

            return ExternalNotificationStatus.Delivered;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification du statut push notification {NotificationId}", notificationId);
            return ExternalNotificationStatus.Failed;
        }
    }

    /// <inheritdoc />
    public bool ValidateDestination(string destination)
    {
        if (string.IsNullOrWhiteSpace(destination))
            return false;

        try
        {
            // Validation basique du token FCM (longueur et format)
            return destination.Length > 100 && destination.All(c => char.IsLetterOrDigit(c) || c == '_' || c == '-' || c == ':');
        }
        catch
        {
            return false;
        }
    }

    #region Méthodes privées

    /// <summary>
    /// Crée un message Firebase pour un seul destinataire
    /// </summary>
    private Message CreateMessage(string token, string title, string body, ExternalNotificationRequest request)
    {
        var notification = new Notification
        {
            Title = title,
            Body = body,
            ImageUrl = request.Metadata.GetValueOrDefault("imageUrl")?.ToString()
        };

        var data = new Dictionary<string, string>();
        foreach (var metadata in request.Metadata)
        {
            data[metadata.Key] = metadata.Value?.ToString() ?? string.Empty;
        }

        // Configuration spécifique Android
        var androidConfig = new AndroidConfig
        {
            Priority = request.Priority == ExternalNotificationPriority.High || request.Priority == ExternalNotificationPriority.Critical 
                ? Priority.High 
                : Priority.Normal,
            Notification = new AndroidNotification
            {
                Icon = _settings.DefaultIcon,
                Color = _settings.DefaultColor,
                Sound = request.Priority == ExternalNotificationPriority.Critical ? "urgent" : "default",
                ChannelId = GetChannelId(request.Priority)
            }
        };

        // Configuration spécifique iOS
        var apnsConfig = new ApnsConfig
        {
            Aps = new Aps
            {
                Alert = new ApsAlert
                {
                    Title = title,
                    Body = body
                },
                Badge = 1,
                Sound = request.Priority == ExternalNotificationPriority.Critical ? "urgent.aiff" : "default",
                ContentAvailable = true
            }
        };

        return new Message
        {
            Token = token,
            Notification = notification,
            Data = data,
            Android = androidConfig,
            Apns = apnsConfig
        };
    }

    /// <summary>
    /// Crée un message multicast Firebase
    /// </summary>
    private MulticastMessage CreateMulticastMessage(List<string> tokens, string title, string body, ExternalNotificationRequest request)
    {
        var notification = new Notification
        {
            Title = title,
            Body = body,
            ImageUrl = request.Metadata.GetValueOrDefault("imageUrl")?.ToString()
        };

        var data = new Dictionary<string, string>();
        foreach (var metadata in request.Metadata)
        {
            data[metadata.Key] = metadata.Value?.ToString() ?? string.Empty;
        }

        return new MulticastMessage
        {
            Tokens = tokens,
            Notification = notification,
            Data = data,
            Android = new AndroidConfig
            {
                Priority = request.Priority == ExternalNotificationPriority.High || request.Priority == ExternalNotificationPriority.Critical 
                    ? Priority.High 
                    : Priority.Normal,
                Notification = new AndroidNotification
                {
                    Icon = _settings.DefaultIcon,
                    Color = _settings.DefaultColor,
                    ChannelId = GetChannelId(request.Priority)
                }
            },
            Apns = new ApnsConfig
            {
                Aps = new Aps
                {
                    Alert = new ApsAlert
                    {
                        Title = title,
                        Body = body
                    },
                    Badge = 1,
                    Sound = request.Priority == ExternalNotificationPriority.Critical ? "urgent.aiff" : "default"
                }
            }
        };
    }

    /// <summary>
    /// Obtient l'ID du canal de notification selon la priorité
    /// </summary>
    private string GetChannelId(ExternalNotificationPriority priority)
    {
        return priority switch
        {
            ExternalNotificationPriority.Critical => "critical_notifications",
            ExternalNotificationPriority.High => "high_priority_notifications",
            ExternalNotificationPriority.Normal => "default_notifications",
            ExternalNotificationPriority.Low => "low_priority_notifications",
            _ => "default_notifications"
        };
    }

    #endregion
}

/// <summary>
/// Configuration Firebase
/// </summary>
public class FirebaseSettings
{
    /// <summary>
    /// ID du projet Firebase
    /// </summary>
    public string ProjectId { get; set; } = string.Empty;

    /// <summary>
    /// Chemin vers le fichier de clé de service
    /// </summary>
    public string ServiceAccountKeyPath { get; set; } = string.Empty;

    /// <summary>
    /// JSON de la clé de service (alternative au fichier)
    /// </summary>
    public string ServiceAccountKeyJson { get; set; } = string.Empty;

    /// <summary>
    /// Icône par défaut pour Android
    /// </summary>
    public string DefaultIcon { get; set; } = "ic_notification";

    /// <summary>
    /// Couleur par défaut pour Android
    /// </summary>
    public string DefaultColor { get; set; } = "#FF6B35";

    /// <summary>
    /// Activer les analytics
    /// </summary>
    public bool EnableAnalytics { get; set; } = true;

    /// <summary>
    /// Limite de taux d'envoi par minute
    /// </summary>
    public int RateLimitPerMinute { get; set; } = 600;

    /// <summary>
    /// TTL par défaut des messages (en secondes)
    /// </summary>
    public int DefaultTtl { get; set; } = 3600; // 1 heure

    /// <summary>
    /// Activer la compression des messages
    /// </summary>
    public bool EnableCompression { get; set; } = true;
}
