# Docker Compose pour le développement
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

version: '3.8'

services:
  # Configuration spécifique au développement pour l'API
  # servicelink-api:
  #   build:
  #     context: ./backend
  #     dockerfile: Dockerfile.dev
  #     target: development
  #   environment:
  #     - ASPNETCORE_ENVIRONMENT=Development
  #     - ASPNETCORE_URLS=http://+:8080;https://+:8081
  #     - ASPNETCORE_Kestrel__Certificates__Default__Password=password
  #     - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
  #   volumes:
  #     - ./backend:/app
  #     - ./backend/.aspnet/https:/https:ro
  #     - /app/bin
  #     - /app/obj
  #   ports:
  #     - "8080:8080"
  #     - "8081:8081"
  #   command: dotnet watch run --project src/ServiceLink.API/ServiceLink.API.csproj

  # Base de données de développement avec données de test
  servicelink-postgres:
    image: postgres:16-alpine
    container_name: servicelink-postgres
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: dev_password_2025!
    ports:
      - "5437:5432"
    volumes:
      - servicelink_postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init/01-init-db.sql:/docker-entrypoint-initdb.d/01-init-db.sql
    networks:
      - servicelink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis de développement sans mot de passe
  # servicelink-redis:
  #   command: redis-server --appendonly yes
    # Pas de mot de passe en développement pour simplifier

  # Frontend avec hot reload
  # servicelink-frontend:
  #   profiles: [] # Toujours actif en développement
  #   environment:
  #     - NODE_ENV=development
  #     - VITE_API_URL=http://localhost:8080
  #     - VITE_WS_URL=ws://localhost:8080
  #     - CHOKIDAR_USEPOLLING=true
  #   command: sh -c "npm install && npm run dev -- --host 0.0.0.0"

  # Mailhog pour tester les emails en développement
  servicelink-mailhog:
    image: mailhog/mailhog:latest
    container_name: servicelink-mailhog
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - servicelink-network

  # pgAdmin pour gérer la base de données
  # servicelink-pgadmin:
  #   image: dpage/pgadmin4:latest
  #   container_name: servicelink-pgadmin
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: admin
  #     PGADMIN_CONFIG_SERVER_MODE: 'False'
  #   ports:
  #     - "5050:80"
  #   volumes:
  #     - pgadmin_data:/var/lib/pgadmin
  #   networks:
  #     - servicelink-network
  #   depends_on:
  #     - servicelink-postgres

  # Redis Commander pour gérer Redis
  # servicelink-redis-commander:
  #   image: rediscommander/redis-commander:latest
  #   container_name: servicelink-redis-commander
  #   environment:
  #     - REDIS_HOSTS=local:servicelink-redis:6379
  #   ports:
  #     - "8081:8081"
  #   networks:
  #     - servicelink-network
  #   depends_on:
  #     - servicelink-redis

volumes:
  servicelink_postgres_data:
    driver: local
  # pgadmin_data:
  #   driver: local

networks:
  servicelink-network:
    driver: bridge
    name: servicelink-network
