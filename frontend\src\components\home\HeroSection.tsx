import React, { useState } from 'react';
import { Search, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const popularServices = [
  'Mén<PERSON>', 'Jardinage', 'Bricolage', 'Plomberie', 'Électricité', 
  'Peinture', 'Déménagement', 'Garde d\'enfants'
];

export const HeroSection: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [location, setLocation] = useState('');

  return (
    <section className="bg-gradient-to-br from-blue-600 to-blue-700 text-white py-16 lg:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main Title */}
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            Réservez le prestataire idéal
          </h1>
          
          <p className="text-xl lg:text-2xl mb-8 text-blue-100">
            Des milliers de professionnels qualifiés près de chez vous
          </p>

          {/* Search Form */}
          <div className="bg-white rounded-lg p-4 shadow-xl max-w-2xl mx-auto mb-8">
            <div className="flex flex-col md:flex-row gap-3">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  placeholder="Quel service recherchez-vous ?"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-12 text-foreground"
                />
              </div>
              
              <div className="flex-1 relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  placeholder="Où ?"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  className="pl-10 h-12 text-foreground"
                />
              </div>
              
              <Button size="lg" className="h-12 px-8">
                <Search className="w-5 h-5 mr-2" />
                Rechercher
              </Button>
            </div>
          </div>

          {/* Popular Services */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {popularServices.map((service) => (
              <Badge 
                key={service} 
                variant="secondary" 
                className="bg-white/20 text-white hover:bg-white/30 cursor-pointer transition-colors"
              >
                {service}
              </Badge>
            ))}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold">322 000</div>
              <div className="text-blue-100">Prestataires qualifiés</div>
            </div>
            <div>
              <div className="text-3xl font-bold">1 255 000</div>
              <div className="text-blue-100">Services réalisés</div>
            </div>
            <div>
              <div className="text-3xl font-bold">3 760 000</div>
              <div className="text-blue-100">Clients satisfaits</div>
            </div>
            <div>
              <div className="text-3xl font-bold">4.9/5</div>
              <div className="text-blue-100">Note moyenne</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
