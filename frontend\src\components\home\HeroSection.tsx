import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useCategories, useServiceStats } from '@/hooks/useServices';

export const HeroSection: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [location, setLocation] = useState('');
  const navigate = useNavigate();

  // Charger les données depuis l'API
  const { data: categories, loading: categoriesLoading } = useCategories();
  const { data: stats, loading: statsLoading } = useServiceStats();

  // Gestion de la recherche
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      const params = new URLSearchParams();
      params.set('search', searchQuery.trim());
      if (location.trim()) {
        params.set('location', location.trim());
      }
      navigate(`/services?${params.toString()}`);
    }
  };

  // Gestion du clic sur une catégorie
  const handleCategoryClick = (categoryName: string) => {
    navigate(`/services?search=${encodeURIComponent(categoryName)}`);
  };

  return (
    <section className="bg-gradient-to-br from-blue-600 to-blue-700 text-white py-16 lg:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main Title */}
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            Réservez le prestataire idéal
          </h1>

          <p className="text-xl lg:text-2xl mb-8 text-blue-100">
            Des milliers de professionnels qualifiés près de chez vous
          </p>

          {/* Search Form */}
          <form onSubmit={handleSearch} className="bg-white rounded-lg p-4 shadow-xl max-w-2xl mx-auto mb-8">
            <div className="flex flex-col md:flex-row gap-3">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  placeholder="Quel service recherchez-vous ?"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-12 text-foreground"
                />
              </div>

              <div className="flex-1 relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  placeholder="Où ?"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  className="pl-10 h-12 text-foreground"
                />
              </div>

              <Button type="submit" size="lg" className="h-12 px-8">
                <Search className="w-5 h-5 mr-2" />
                Rechercher
              </Button>
            </div>
          </form>

          {/* Popular Categories */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {categoriesLoading ? (
              // Skeleton loading
              Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="h-8 w-20 bg-white/20 rounded-full animate-pulse" />
              ))
            ) : (
              categories?.slice(0, 8).map((category) => (
                <Badge
                  key={category.id}
                  variant="secondary"
                  className="bg-white/20 text-white hover:bg-white/30 cursor-pointer transition-colors"
                  onClick={() => handleCategoryClick(category.name)}
                >
                  {category.name}
                </Badge>
              ))
            )}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold">
                {statsLoading ? '...' : (stats?.totalProviders?.toLocaleString() || '322 000')}
              </div>
              <div className="text-blue-100">Prestataires qualifiés</div>
            </div>
            <div>
              <div className="text-3xl font-bold">
                {statsLoading ? '...' : (stats?.totalBookings?.toLocaleString() || '1 255 000')}
              </div>
              <div className="text-blue-100">Services réalisés</div>
            </div>
            <div>
              <div className="text-3xl font-bold">
                {statsLoading ? '...' : (stats?.totalServices?.toLocaleString() || '3 760 000')}
              </div>
              <div className="text-blue-100">Services disponibles</div>
            </div>
            <div>
              <div className="text-3xl font-bold">
                {statsLoading ? '...' : (stats?.averageRating?.toFixed(1) || '4.9')}/5
              </div>
              <div className="text-blue-100">Note moyenne</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
