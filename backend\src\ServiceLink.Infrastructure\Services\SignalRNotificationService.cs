using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using ServiceLink.Infrastructure.Hubs;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Implémentation du service de notifications temps réel avec SignalR
/// </summary>
public class SignalRNotificationService : INotificationService
{
    private readonly IHubContext<NotificationHub> _notificationHub;
    private readonly ICacheService _cacheService;
    private readonly ILogger<SignalRNotificationService> _logger;

    public SignalRNotificationService(
        IHubContext<NotificationHub> notificationHub,
        ICacheService cacheService,
        ILogger<SignalRNotificationService> logger)
    {
        _notificationHub = notificationHub;
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task SendToUserAsync(Guid userId, NotificationDto notification, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi de notification à l'utilisateur {UserId}: {Title}", userId, notification.Title);

            // Envoyer la notification à l'utilisateur spécifique
            await _notificationHub.Clients.User(userId.ToString())
                .SendAsync("ReceiveNotification", notification, cancellationToken);

            // Optionnel: Sauvegarder la notification en cache pour récupération ultérieure
            await SaveNotificationToCacheAsync(userId, notification);

            _logger.LogInformation("Notification envoyée avec succès à l'utilisateur {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification à l'utilisateur {UserId}", userId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task SendToRoleAsync(UserRole role, NotificationDto notification, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi de notification au rôle {Role}: {Title}", role, notification.Title);

            var groupName = SignalRGroups.GetRoleGroup(role);
            await _notificationHub.Clients.Group(groupName)
                .SendAsync("ReceiveNotification", notification, cancellationToken);

            _logger.LogInformation("Notification envoyée avec succès au rôle {Role}", role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification au rôle {Role}", role);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task SendToGroupAsync(string groupName, NotificationDto notification, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi de notification au groupe {GroupName}: {Title}", groupName, notification.Title);

            await _notificationHub.Clients.Group(groupName)
                .SendAsync("ReceiveNotification", notification, cancellationToken);

            _logger.LogInformation("Notification envoyée avec succès au groupe {GroupName}", groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification au groupe {GroupName}", groupName);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task AddToGroupAsync(Guid userId, string groupName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Ajout de l'utilisateur {UserId} au groupe {GroupName}", userId, groupName);

            // Dans une implémentation complète, on récupérerait les connexions actives de l'utilisateur
            // Pour l'instant, on utilise l'ID utilisateur comme identifiant de connexion
            await _notificationHub.Groups.AddToGroupAsync(userId.ToString(), groupName, cancellationToken);

            _logger.LogInformation("Utilisateur {UserId} ajouté au groupe {GroupName}", userId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout de l'utilisateur {UserId} au groupe {GroupName}", userId, groupName);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task RemoveFromGroupAsync(Guid userId, string groupName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Suppression de l'utilisateur {UserId} du groupe {GroupName}", userId, groupName);

            await _notificationHub.Groups.RemoveFromGroupAsync(userId.ToString(), groupName, cancellationToken);

            _logger.LogInformation("Utilisateur {UserId} supprimé du groupe {GroupName}", userId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'utilisateur {UserId} du groupe {GroupName}", userId, groupName);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ConnectedUserDto>> GetConnectedUsersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Récupération de la liste des utilisateurs connectés");

            // Récupérer les utilisateurs connectés depuis le cache
            var connectedUsers = new List<ConnectedUserDto>();

            // Dans une implémentation complète, on parcourrait toutes les connexions actives
            // stockées en cache et on construirait la liste des utilisateurs connectés

            var pattern = "signalr:connections:user:*";
            // Note: RemoveByPatternAsync existe mais pas GetByPatternAsync
            // Dans une vraie implémentation, on utiliserait Redis directement ou une autre méthode

            _logger.LogDebug("Trouvé {Count} utilisateurs connectés", connectedUsers.Count);

            return connectedUsers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des utilisateurs connectés");
            return Enumerable.Empty<ConnectedUserDto>();
        }
    }

    /// <inheritdoc />
    public async Task<bool> IsUserConnectedAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"signalr:connections:user:{userId}";
            return await _cacheService.ExistsAsync(cacheKey, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de connexion pour l'utilisateur {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// Sauvegarde une notification en cache pour récupération ultérieure
    /// </summary>
    private async Task SaveNotificationToCacheAsync(Guid userId, NotificationDto notification)
    {
        try
        {
            var cacheKey = $"notifications:user:{userId}:{notification.Id}";
            await _cacheService.SetAsync(cacheKey, notification, TimeSpan.FromDays(7)); // Garder 7 jours
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erreur lors de la sauvegarde de notification en cache pour l'utilisateur {UserId}", userId);
            // Ne pas faire échouer l'envoi de notification pour un problème de cache
        }
    }
}

/// <summary>
/// Implémentation du service de chat temps réel avec SignalR
/// </summary>
public class SignalRChatService : IChatService
{
    private readonly IHubContext<ChatHub> _chatHub;
    private readonly ICacheService _cacheService;
    private readonly ILogger<SignalRChatService> _logger;

    public SignalRChatService(
        IHubContext<ChatHub> chatHub,
        ICacheService cacheService,
        ILogger<SignalRChatService> logger)
    {
        _chatHub = chatHub;
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task SendMessageAsync(Guid conversationId, ChatMessageDto message, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi de message dans la conversation {ConversationId} par {SenderId}",
                conversationId, message.SenderId);

            var groupName = SignalRGroups.GetConversationGroup(conversationId);
            await _chatHub.Clients.Group(groupName)
                .SendAsync("ReceiveMessage", message, cancellationToken);

            // Sauvegarder le message en cache
            await SaveMessageToCacheAsync(message);

            _logger.LogInformation("Message envoyé avec succès dans la conversation {ConversationId}", conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de message dans la conversation {ConversationId}", conversationId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task MarkMessageAsReadAsync(Guid messageId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Marquage du message {MessageId} comme lu par l'utilisateur {UserId}", messageId, userId);

            // Dans une implémentation complète, on mettrait à jour la base de données
            // et on notifierait via SignalR

            // Pour l'instant, on met juste à jour le cache
            var cacheKey = $"message:read:{messageId}:{userId}";
            await _cacheService.SetAsync(cacheKey, new
            {
                MessageId = messageId,
                UserId = userId,
                ReadAt = DateTime.UtcNow
            }, TimeSpan.FromDays(30));

            _logger.LogDebug("Message {MessageId} marqué comme lu par {UserId}", messageId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage du message {MessageId} comme lu", messageId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task UserTypingAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var groupName = SignalRGroups.GetConversationGroup(conversationId);
            await _chatHub.Clients.Group(groupName)
                .SendAsync("UserStartedTyping", new
                {
                    UserId = userId,
                    ConversationId = conversationId,
                    StartedAt = DateTime.UtcNow
                }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la notification de frappe pour l'utilisateur {UserId}", userId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task UserStoppedTypingAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var groupName = SignalRGroups.GetConversationGroup(conversationId);
            await _chatHub.Clients.Group(groupName)
                .SendAsync("UserStoppedTyping", new
                {
                    UserId = userId,
                    ConversationId = conversationId,
                    StoppedAt = DateTime.UtcNow
                }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la notification d'arrêt de frappe pour l'utilisateur {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// Envoie une notification de création de réservation
    /// </summary>
    public async Task SendBookingCreatedNotificationAsync(Guid userId, Guid bookingId, string clientName, string serviceName, DateTime scheduledDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi de notification de création de réservation à {UserId}", userId);

            var notification = new
            {
                Type = "BookingCreated",
                BookingId = bookingId,
                ClientName = clientName,
                ServiceName = serviceName,
                ScheduledDate = scheduledDate,
                Message = $"Nouvelle réservation de {clientName} pour {serviceName}",
                Timestamp = DateTime.UtcNow
            };

            await _chatHub.Clients.User(userId.ToString()).SendAsync("BookingNotification", notification, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de la notification de création de réservation");
        }
    }

    /// <summary>
    /// Envoie une notification de confirmation de réservation
    /// </summary>
    public async Task SendBookingConfirmationNotificationAsync(Guid userId, Guid bookingId, string providerName, string serviceName, DateTime scheduledDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi de notification de confirmation de réservation à {UserId}", userId);

            var notification = new
            {
                Type = "BookingConfirmed",
                BookingId = bookingId,
                ProviderName = providerName,
                ServiceName = serviceName,
                ScheduledDate = scheduledDate,
                Message = $"Votre réservation pour {serviceName} avec {providerName} a été confirmée",
                Timestamp = DateTime.UtcNow
            };

            await _chatHub.Clients.User(userId.ToString()).SendAsync("BookingNotification", notification, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de la notification de confirmation de réservation");
        }
    }

    /// <summary>
    /// Sauvegarde un message en cache
    /// </summary>
    private async Task SaveMessageToCacheAsync(ChatMessageDto message)
    {
        try
        {
            var cacheKey = $"message:{message.ConversationId}:{message.Id}";
            await _cacheService.SetAsync(cacheKey, message, TimeSpan.FromDays(30));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erreur lors de la sauvegarde du message {MessageId} en cache", message.Id);
        }
    }
}

/// <summary>
/// Implémentation du service de réservations temps réel avec SignalR
/// </summary>
public class SignalRBookingRealtimeService : IBookingRealtimeService
{
    private readonly IHubContext<BookingHub> _bookingHub;
    private readonly INotificationService _notificationService;
    private readonly ILogger<SignalRBookingRealtimeService> _logger;

    public SignalRBookingRealtimeService(
        IHubContext<BookingHub> bookingHub,
        INotificationService notificationService,
        ILogger<SignalRBookingRealtimeService> logger)
    {
        _bookingHub = bookingHub;
        _notificationService = notificationService;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task NotifyNewBookingAsync(BookingNotificationDto booking, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Notification de nouvelle réservation {BookingId} du client {ClientId} au prestataire {ProviderId}",
                booking.BookingId, booking.ClientId, booking.ProviderId);

            var notification = new NotificationDto
            {
                Type = "NewBooking",
                Title = "Nouvelle réservation",
                Message = $"Nouvelle réservation pour {booking.ServiceName}",
                Priority = NotificationPriority.High,
                Data = booking
            };

            // Notifier le prestataire
            await _notificationService.SendToUserAsync(booking.ProviderId, notification, cancellationToken);

            // Notifier les administrateurs
            await _notificationService.SendToRoleAsync(UserRole.Admin, notification, cancellationToken);

            // Envoyer via le hub de réservations
            var groupName = SignalRGroups.GetBookingGroup(booking.BookingId);
            await _bookingHub.Clients.Group(groupName)
                .SendAsync("ReceiveBookingNotification", notification, cancellationToken);

            _logger.LogInformation("Notification de nouvelle réservation {BookingId} envoyée", booking.BookingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la notification de nouvelle réservation {BookingId}", booking.BookingId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task NotifyBookingStatusChangeAsync(Guid bookingId, string newStatus, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Notification de changement de statut pour la réservation {BookingId}: {NewStatus}",
                bookingId, newStatus);

            var notification = new NotificationDto
            {
                Type = "BookingStatusChanged",
                Title = "Statut de réservation mis à jour",
                Message = $"Le statut de votre réservation a été mis à jour: {newStatus}",
                Priority = NotificationPriority.Normal,
                Data = new
                {
                    BookingId = bookingId,
                    NewStatus = newStatus,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            var groupName = SignalRGroups.GetBookingGroup(bookingId);
            await _bookingHub.Clients.Group(groupName)
                .SendAsync("ReceiveBookingNotification", notification, cancellationToken);

            _logger.LogInformation("Notification de changement de statut envoyée pour la réservation {BookingId}", bookingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la notification de changement de statut pour la réservation {BookingId}", bookingId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task NotifyBookingCancellationAsync(Guid bookingId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Notification d'annulation pour la réservation {BookingId}: {Reason}",
                bookingId, reason);

            var notification = new NotificationDto
            {
                Type = "BookingCancelled",
                Title = "Réservation annulée",
                Message = $"La réservation a été annulée. Raison: {reason}",
                Priority = NotificationPriority.High,
                Data = new
                {
                    BookingId = bookingId,
                    Reason = reason,
                    CancelledAt = DateTime.UtcNow
                }
            };

            var groupName = SignalRGroups.GetBookingGroup(bookingId);
            await _bookingHub.Clients.Group(groupName)
                .SendAsync("ReceiveBookingNotification", notification, cancellationToken);

            _logger.LogInformation("Notification d'annulation envoyée pour la réservation {BookingId}", bookingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la notification d'annulation pour la réservation {BookingId}", bookingId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task NotifyBookingCreatedAsync(Guid bookingId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Notification de création pour la réservation {BookingId}", bookingId);

            var notification = new NotificationDto
            {
                Type = "BookingCreated",
                Title = "Nouvelle réservation",
                Message = "Une nouvelle réservation a été créée",
                Priority = NotificationPriority.Normal,
                Data = new
                {
                    BookingId = bookingId,
                    CreatedAt = DateTime.UtcNow
                }
            };

            var groupName = SignalRGroups.GetBookingGroup(bookingId);
            await _bookingHub.Clients.Group(groupName)
                .SendAsync("ReceiveBookingNotification", notification, cancellationToken);

            _logger.LogInformation("Notification de création envoyée pour la réservation {BookingId}", bookingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la notification de création pour la réservation {BookingId}", bookingId);
            throw;
        }
    }
}
