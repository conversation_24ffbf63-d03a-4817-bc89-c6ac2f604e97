using ServiceLink.Domain.Entities;

namespace ServiceLink.Domain.Interfaces;

/// <summary>
/// Interface pour le repository des avis
/// </summary>
public interface IReviewRepository : IRepository<Review>
{
    /// <summary>
    /// Obtient les avis d'un prestataire
    /// </summary>
    Task<IEnumerable<Review>> GetByProviderIdAsync(Guid providerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les avis d'un client
    /// </summary>
    Task<IEnumerable<Review>> GetByClientIdAsync(Guid clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les avis d'un service
    /// </summary>
    Task<IEnumerable<Review>> GetByServiceIdAsync(Guid serviceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient l'avis d'une réservation
    /// </summary>
    Task<Review?> GetByBookingIdAsync(Guid bookingId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie si un avis existe pour une réservation
    /// </summary>
    Task<bool> ExistsForBookingAsync(Guid bookingId, CancellationToken cancellationToken = default);
}
