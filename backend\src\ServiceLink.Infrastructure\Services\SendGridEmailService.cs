using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SendGrid;
using SendGrid.Helpers.Mail;
using ServiceLink.Application.Interfaces;
using System.Text.RegularExpressions;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Service d'envoi d'emails via SendGrid
/// </summary>
public class SendGridEmailService : IExternalNotificationService
{
    private readonly ILogger<SendGridEmailService> _logger;
    private readonly ISendGridClient _sendGridClient;
    private readonly SendGridSettings _settings;
    private readonly INotificationTemplateService _templateService;

    public SendGridEmailService(
        ILogger<SendGridEmailService> logger,
        ISendGridClient sendGridClient,
        IConfiguration configuration,
        INotificationTemplateService templateService)
    {
        _logger = logger;
        _sendGridClient = sendGridClient;
        _settings = configuration.GetSection("SendGrid").Get<SendGridSettings>() ?? new SendGridSettings();
        _templateService = templateService;
    }

    /// <inheritdoc />
    public ExternalNotificationType ServiceType => ExternalNotificationType.Email;

    /// <inheritdoc />
    public async Task<ExternalNotificationResult> SendNotificationAsync(
        ExternalNotificationRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi d'email via SendGrid vers {Recipients}", string.Join(", ", request.Recipients));

            // Validation des destinataires
            var validRecipients = request.Recipients.Where(ValidateDestination).ToList();
            if (!validRecipients.Any())
            {
                return new ExternalNotificationResult
                {
                    Success = false,
                    Type = ServiceType,
                    Status = ExternalNotificationStatus.Failed,
                    ErrorMessage = "Aucun destinataire valide",
                    FailedRecipients = request.Recipients
                };
            }

            // Préparation du contenu
            var subject = request.Subject;
            var content = request.Content;

            // Utilisation d'un template si spécifié
            if (!string.IsNullOrEmpty(request.TemplateName))
            {
                var template = await _templateService.GetTemplateAsync(request.TemplateName, request.Language);
                if (template != null)
                {
                    var subjectTemplate = new NotificationTemplate
                    {
                        Id = template.Id,
                        Name = template.Name,
                        Type = template.Type,
                        Language = template.Language,
                        Subject = template.Subject,
                        Content = template.Subject,
                        Variables = template.Variables,
                        IsActive = template.IsActive,
                        CreatedAt = template.CreatedAt,
                        UpdatedAt = template.UpdatedAt,
                        Version = template.Version
                    };
                    subject = _templateService.RenderTemplate(subjectTemplate, request.TemplateData);
                    content = _templateService.RenderTemplate(template, request.TemplateData);
                }
            }

            // Création du message SendGrid
            var from = new EmailAddress(_settings.FromEmail, _settings.FromName);
            var msg = new SendGridMessage
            {
                From = from,
                Subject = subject,
                HtmlContent = content,
                PlainTextContent = StripHtml(content)
            };

            // Ajout des destinataires
            foreach (var recipient in validRecipients)
            {
                msg.AddTo(new EmailAddress(recipient));
            }

            // Ajout des pièces jointes
            foreach (var attachment in request.Attachments)
            {
                msg.AddAttachment(attachment.FileName, attachment.Content, attachment.ContentType);
            }

            // Configuration des catégories et tags
            if (request.Tags.Any())
            {
                msg.Categories = request.Tags;
            }

            // Métadonnées personnalisées
            if (request.Metadata.Any())
            {
                foreach (var metadata in request.Metadata)
                {
                    msg.AddCustomArg(metadata.Key, metadata.Value?.ToString() ?? string.Empty);
                }
            }

            // Configuration de la priorité
            if (request.Priority == ExternalNotificationPriority.High || request.Priority == ExternalNotificationPriority.Critical)
            {
                msg.Headers = new Dictionary<string, string>
                {
                    ["X-Priority"] = "1",
                    ["X-MSMail-Priority"] = "High"
                };
            }

            // Envoi du message
            var response = await _sendGridClient.SendEmailAsync(msg, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var messageId = response.Headers.GetValues("X-Message-Id").FirstOrDefault() ?? Guid.NewGuid().ToString();
                
                _logger.LogInformation("Email envoyé avec succès via SendGrid. MessageId: {MessageId}", messageId);

                return new ExternalNotificationResult
                {
                    Success = true,
                    NotificationId = messageId,
                    Type = ServiceType,
                    Status = ExternalNotificationStatus.Sent,
                    SentAt = DateTime.UtcNow,
                    SuccessfulRecipients = validRecipients,
                    ProviderData = new Dictionary<string, object>
                    {
                        ["StatusCode"] = (int)response.StatusCode,
                        ["Headers"] = response.Headers.ToDictionary(h => h.Key, h => string.Join(", ", h.Value))
                    }
                };
            }
            else
            {
                var errorBody = await response.Body.ReadAsStringAsync();
                _logger.LogError("Échec d'envoi email SendGrid. StatusCode: {StatusCode}, Body: {Body}", 
                    response.StatusCode, errorBody);

                return new ExternalNotificationResult
                {
                    Success = false,
                    Type = ServiceType,
                    Status = ExternalNotificationStatus.Failed,
                    ErrorMessage = $"SendGrid error: {response.StatusCode}",
                    ErrorCode = response.StatusCode.ToString(),
                    FailedRecipients = validRecipients,
                    ProviderData = new Dictionary<string, object>
                    {
                        ["StatusCode"] = (int)response.StatusCode,
                        ["ErrorBody"] = errorBody
                    }
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi d'email via SendGrid");
            
            return new ExternalNotificationResult
            {
                Success = false,
                Type = ServiceType,
                Status = ExternalNotificationStatus.Failed,
                ErrorMessage = ex.Message,
                FailedRecipients = request.Recipients
            };
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ExternalNotificationResult>> SendBulkNotificationsAsync(
        IEnumerable<ExternalNotificationRequest> requests, 
        CancellationToken cancellationToken = default)
    {
        var results = new List<ExternalNotificationResult>();
        var requestList = requests.ToList();

        _logger.LogInformation("Envoi en lot de {Count} emails via SendGrid", requestList.Count);

        // SendGrid supporte l'envoi en lot, mais pour simplifier nous envoyons individuellement
        // En production, on pourrait optimiser avec l'API batch de SendGrid
        foreach (var request in requestList)
        {
            var result = await SendNotificationAsync(request, cancellationToken);
            results.Add(result);

            // Petite pause pour éviter le rate limiting
            if (requestList.Count > 10)
            {
                await Task.Delay(100, cancellationToken);
            }
        }

        return results;
    }

    /// <inheritdoc />
    public async Task<ExternalNotificationStatus> GetNotificationStatusAsync(
        string notificationId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Vérification du statut de l'email {NotificationId} via SendGrid", notificationId);

            // En production, utiliser l'API SendGrid Event Webhook ou Stats API
            // Pour l'instant, retourner un statut simulé
            await Task.Delay(1, cancellationToken);

            // Simulation basée sur l'âge du message
            return ExternalNotificationStatus.Delivered;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification du statut email {NotificationId}", notificationId);
            return ExternalNotificationStatus.Failed;
        }
    }

    /// <inheritdoc />
    public bool ValidateDestination(string destination)
    {
        if (string.IsNullOrWhiteSpace(destination))
            return false;

        try
        {
            // Validation basique de l'email avec regex
            var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
            return emailRegex.IsMatch(destination);
        }
        catch
        {
            return false;
        }
    }

    #region Méthodes privées

    /// <summary>
    /// Supprime les balises HTML d'un contenu
    /// </summary>
    /// <param name="html">Contenu HTML</param>
    /// <returns>Contenu en texte brut</returns>
    private static string StripHtml(string html)
    {
        if (string.IsNullOrEmpty(html))
            return string.Empty;

        try
        {
            // Suppression basique des balises HTML
            var stripped = Regex.Replace(html, "<.*?>", string.Empty);
            
            // Décodage des entités HTML communes
            stripped = stripped.Replace("&nbsp;", " ")
                              .Replace("&amp;", "&")
                              .Replace("&lt;", "<")
                              .Replace("&gt;", ">")
                              .Replace("&quot;", "\"")
                              .Replace("&#39;", "'");

            return stripped.Trim();
        }
        catch
        {
            return html;
        }
    }

    #endregion
}

/// <summary>
/// Configuration SendGrid
/// </summary>
public class SendGridSettings
{
    /// <summary>
    /// Clé API SendGrid
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// Email expéditeur par défaut
    /// </summary>
    public string FromEmail { get; set; } = "<EMAIL>";

    /// <summary>
    /// Nom expéditeur par défaut
    /// </summary>
    public string FromName { get; set; } = "ServiceLink";

    /// <summary>
    /// Email de réponse par défaut
    /// </summary>
    public string ReplyToEmail { get; set; } = "<EMAIL>";

    /// <summary>
    /// Activer le tracking des ouvertures
    /// </summary>
    public bool EnableOpenTracking { get; set; } = true;

    /// <summary>
    /// Activer le tracking des clics
    /// </summary>
    public bool EnableClickTracking { get; set; } = true;

    /// <summary>
    /// Activer les webhooks
    /// </summary>
    public bool EnableWebhooks { get; set; } = true;

    /// <summary>
    /// URL du webhook
    /// </summary>
    public string WebhookUrl { get; set; } = string.Empty;

    /// <summary>
    /// Template par défaut pour les emails transactionnels
    /// </summary>
    public string DefaultTemplateId { get; set; } = string.Empty;

    /// <summary>
    /// Limite de taux d'envoi par minute
    /// </summary>
    public int RateLimitPerMinute { get; set; } = 100;
}
