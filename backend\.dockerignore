# Fichiers et dossiers à ignorer lors de la construction Docker

# Dossiers de build et de sortie
**/bin/
**/obj/
**/out/
**/TestResults/

# Dossiers de packages NuGet
**/.nuget/
**/packages/

# Fichiers de configuration locale
**/.env
**/.env.local
**/.env.development
**/.env.production
**/appsettings.Development.json
**/appsettings.Local.json

# Fichiers de base de données locale
**/*.db
**/*.sqlite
**/*.sqlite3

# Logs
**/logs/
**/*.log

# Fichiers temporaires
**/tmp/
**/temp/

# Fichiers de cache
**/.cache/
**/node_modules/

# Fichiers IDE
**/.vs/
**/.vscode/
**/.idea/
**/*.swp
**/*.swo
**/*~

# Fichiers système
**/.DS_Store
**/Thumbs.db

# Fichiers Git
**/.git/
**/.gitignore
**/.gitattributes

# Documentation
**/README.md
**/CHANGELOG.md
**/docs/

# Fichiers de test
**/tests/
**/*Tests/
**/*.Tests/

# Fichiers Docker
**/Dockerfile*
**/docker-compose*
**/.dockerignore

# Certificats et clés
**/*.pfx
**/*.p12
**/*.key
**/*.pem
**/*.crt

# Fichiers de sauvegarde
**/*.bak
**/*.backup
