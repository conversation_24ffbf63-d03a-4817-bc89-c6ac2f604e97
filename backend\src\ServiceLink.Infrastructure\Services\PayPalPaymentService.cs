using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PayPalCheckoutSdk.Core;
using PayPalCheckoutSdk.Orders;
using PayPalCheckoutSdk.Payments;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using System.Text.Json;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Implémentation du service de paiement PayPal
/// </summary>
public class PayPalPaymentService : IPaymentService
{
    private readonly ILogger<PayPalPaymentService> _logger;
    private readonly PayPalSettings _settings;
    private readonly PayPalHttpClient _client;

    public PayPalPaymentService(
        IConfiguration configuration,
        ILogger<PayPalPaymentService> logger)
    {
        _logger = logger;
        _settings = configuration.GetSection("PayPal").Get<PayPalSettings>() ?? new PayPalSettings();
        
        // Configuration de l'environnement PayPal
        PayPalEnvironment environment;
        if (_settings.Environment.ToLowerInvariant() == "production")
        {
            environment = new LiveEnvironment(_settings.ClientId, _settings.ClientSecret);
        }
        else
        {
            environment = new SandboxEnvironment(_settings.ClientId, _settings.ClientSecret);
        }
        
        _client = new PayPalHttpClient(environment);

        _logger.LogInformation("Service de paiement PayPal initialisé en mode {Environment}", _settings.Environment);
    }

    /// <inheritdoc />
    public PaymentProvider Provider => PaymentProvider.PayPal;

    /// <inheritdoc />
    public async Task<PaymentIntentResponse> CreatePaymentIntentAsync(
        CreatePaymentIntentRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Création d'une commande PayPal pour {Amount} {Currency}", 
                request.Amount, request.Currency);

            var orderRequest = new OrderRequest()
            {
                CheckoutPaymentIntent = "CAPTURE",
                PurchaseUnits = new List<PurchaseUnitRequest>()
                {
                    new PurchaseUnitRequest()
                    {
                        AmountWithBreakdown = new AmountWithBreakdown()
                        {
                            CurrencyCode = request.Currency.ToUpperInvariant(),
                            Value = FormatAmount(request.Amount, request.Currency)
                        },
                        Description = request.Description,
                        CustomId = request.BookingId.ToString(),
                        InvoiceId = $"INV-{request.BookingId}-{DateTime.UtcNow:yyyyMMddHHmmss}"
                    }
                },
                ApplicationContext = new ApplicationContext()
                {
                    ReturnUrl = request.SuccessUrl ?? _settings.ReturnUrl,
                    CancelUrl = request.CancelUrl ?? _settings.CancelUrl,
                    BrandName = "ServiceLink",
                    LandingPage = "BILLING",
                    UserAction = "PAY_NOW"
                }
            };

            var createOrderRequest = new OrdersCreateRequest();
            createOrderRequest.Prefer("return=representation");
            createOrderRequest.RequestBody(orderRequest);

            var response = await _client.Execute(createOrderRequest);
            var order = response.Result<Order>();

            _logger.LogInformation("Commande PayPal créée: {OrderId}", order.Id);

            // Trouver le lien d'approbation
            var approvalLink = order.Links?.FirstOrDefault(l => l.Rel == "approve")?.Href;

            return new PaymentIntentResponse
            {
                PaymentIntentId = order.Id,
                ClientSecret = order.Id, // PayPal utilise l'ID de commande
                Status = MapPayPalStatus(order.Status),
                Amount = request.Amount,
                Currency = request.Currency,
                RedirectUrl = approvalLink,
                ProviderData = new Dictionary<string, object>
                {
                    ["paypal_order_id"] = order.Id,
                    ["paypal_status"] = order.Status,
                    ["approval_url"] = approvalLink ?? string.Empty
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la commande PayPal");
            throw new InvalidOperationException($"Erreur PayPal: {ex.Message}", ex);
        }
    }

    /// <inheritdoc />
    public async Task<PaymentConfirmationResponse> ConfirmPaymentAsync(
        string paymentIntentId, 
        string paymentMethodId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Capture de la commande PayPal {OrderId}", paymentIntentId);

            var captureRequest = new OrdersCaptureRequest(paymentIntentId);
            captureRequest.Prefer("return=representation");
            captureRequest.RequestBody(new OrderActionRequest());

            var response = await _client.Execute(captureRequest);
            var order = response.Result<Order>();

            var success = order.Status == "COMPLETED";
            var captureId = order.PurchaseUnits?.FirstOrDefault()?.Payments?.Captures?.FirstOrDefault()?.Id;

            _logger.LogInformation("Commande PayPal {OrderId} capturée: {Status}", paymentIntentId, order.Status);

            return new PaymentConfirmationResponse
            {
                Success = success,
                TransactionId = captureId ?? paymentIntentId,
                Status = MapPayPalStatus(order.Status),
                ErrorMessage = success ? null : $"Capture échouée: {order.Status}",
                ErrorCode = success ? null : order.Status
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la capture PayPal {OrderId}", paymentIntentId);
            
            return new PaymentConfirmationResponse
            {
                Success = false,
                TransactionId = paymentIntentId,
                Status = PaymentStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = "CAPTURE_ERROR"
            };
        }
    }

    /// <inheritdoc />
    public async Task<PaymentStatusResponse> GetPaymentStatusAsync(
        string paymentId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Récupération du statut de la commande PayPal {OrderId}", paymentId);

            var getOrderRequest = new OrdersGetRequest(paymentId);
            var response = await _client.Execute(getOrderRequest);
            var order = response.Result<Order>();

            return new PaymentStatusResponse
            {
                PaymentId = order.Id,
                Status = MapPayPalStatus(order.Status),
                Amount = ParseAmount(order.PurchaseUnits?.FirstOrDefault()?.AmountWithBreakdown?.Value ?? "0"),
                Currency = order.PurchaseUnits?.FirstOrDefault()?.AmountWithBreakdown?.CurrencyCode ?? "USD",
                CreatedAt = DateTime.TryParse(order.CreateTime, out var createTime) ? createTime : DateTime.UtcNow,
                UpdatedAt = DateTime.TryParse(order.UpdateTime, out var updateTime) ? updateTime : DateTime.UtcNow,
                Metadata = new Dictionary<string, string>
                {
                    ["custom_id"] = order.PurchaseUnits?.FirstOrDefault()?.CustomId ?? string.Empty,
                    ["invoice_id"] = order.PurchaseUnits?.FirstOrDefault()?.InvoiceId ?? string.Empty
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du statut PayPal {OrderId}", paymentId);
            throw new InvalidOperationException($"Erreur PayPal: {ex.Message}", ex);
        }
    }

    /// <inheritdoc />
    public async Task<RefundResponse> RefundPaymentAsync(
        Application.Interfaces.RefundRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Remboursement PayPal pour la capture {CaptureId}", request.PaymentId);

            var refundRequest = new CapturesRefundRequest(request.PaymentId);
            var refundBody = new PayPalCheckoutSdk.Payments.RefundRequest()
            {
                NoteToPayer = request.Reason
            };

            if (request.Amount.HasValue)
            {
                refundBody.Amount = new PayPalCheckoutSdk.Payments.Money()
                {
                    CurrencyCode = "EUR", // À adapter selon le contexte
                    Value = FormatAmount(request.Amount.Value, "EUR")
                };
            }

            refundRequest.RequestBody(refundBody);

            var response = await _client.Execute(refundRequest);
            var refund = response.Result<PayPalCheckoutSdk.Payments.Refund>();

            _logger.LogInformation("Remboursement PayPal créé: {RefundId}", refund.Id);

            return new RefundResponse
            {
                Success = refund.Status == "COMPLETED",
                RefundId = refund.Id,
                Amount = ParseAmount(refund.Amount?.Value ?? "0"),
                Status = MapPayPalRefundStatus(refund.Status)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du remboursement PayPal {CaptureId}", request.PaymentId);
            
            return new RefundResponse
            {
                Success = false,
                RefundId = string.Empty,
                Amount = 0,
                Status = RefundStatus.Failed,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PaymentTransactionDto>> GetUserTransactionsAsync(
        Guid userId, 
        int limit = 50, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Récupération des transactions PayPal pour l'utilisateur {UserId}", userId);

            // PayPal ne fournit pas d'API directe pour lister les transactions par utilisateur
            // Dans une implémentation complète, on stockerait les transactions en base de données
            // et on les récupérerait depuis là
            
            _logger.LogWarning("GetUserTransactionsAsync non implémenté pour PayPal - utiliser la base de données locale");
            
            return Enumerable.Empty<PaymentTransactionDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des transactions PayPal pour {UserId}", userId);
            throw;
        }
    }

    /// <inheritdoc />
    public bool ValidateWebhookSignature(string payload, string signature, string secret)
    {
        try
        {
            // PayPal utilise un système de validation différent
            // Pour une implémentation complète, utiliser PayPal Webhook SDK
            _logger.LogWarning("Validation webhook PayPal non implémentée - à implémenter avec PayPal Webhook SDK");
            return true; // Temporaire pour les tests
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erreur lors de la validation webhook PayPal");
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<WebhookProcessingResult> ProcessWebhookEventAsync(
        WebhookEventDto webhookEvent, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Traitement de l'événement webhook PayPal: {EventType}", webhookEvent.EventType);

            var result = new WebhookProcessingResult { Success = true };

            switch (webhookEvent.EventType)
            {
                case "CHECKOUT.ORDER.APPROVED":
                    result.ActionsPerformed.Add("Commande approuvée par l'utilisateur");
                    break;

                case "PAYMENT.CAPTURE.COMPLETED":
                    result.ActionsPerformed.Add("Paiement capturé avec succès");
                    break;

                case "PAYMENT.CAPTURE.DENIED":
                    result.ActionsPerformed.Add("Paiement refusé - notification envoyée");
                    break;

                default:
                    result.Message = $"Événement {webhookEvent.EventType} ignoré";
                    break;
            }

            _logger.LogInformation("Événement webhook PayPal {EventType} traité avec succès", webhookEvent.EventType);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de l'événement webhook PayPal {EventType}", 
                webhookEvent.EventType);
            
            return new WebhookProcessingResult
            {
                Success = false,
                Message = "Erreur lors du traitement",
                Errors = { ex.Message }
            };
        }
    }

    #region Méthodes privées

    private static PaymentStatus MapPayPalStatus(string paypalStatus)
    {
        return paypalStatus switch
        {
            "CREATED" => PaymentStatus.Pending,
            "SAVED" => PaymentStatus.Pending,
            "APPROVED" => PaymentStatus.RequiresConfirmation,
            "VOIDED" => PaymentStatus.Cancelled,
            "COMPLETED" => PaymentStatus.Completed,
            "PAYER_ACTION_REQUIRED" => PaymentStatus.RequiresAction,
            _ => PaymentStatus.Failed
        };
    }

    private static RefundStatus MapPayPalRefundStatus(string paypalStatus)
    {
        return paypalStatus switch
        {
            "PENDING" => RefundStatus.Pending,
            "COMPLETED" => RefundStatus.Succeeded,
            "FAILED" => RefundStatus.Failed,
            "CANCELLED" => RefundStatus.Canceled,
            _ => RefundStatus.Failed
        };
    }

    private static string FormatAmount(long amountInCents, string currency)
    {
        // Convertir les centimes en montant décimal
        var amount = amountInCents / 100.0m;
        return amount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }

    private static long ParseAmount(string amountString)
    {
        if (decimal.TryParse(amountString, System.Globalization.NumberStyles.Float, 
            System.Globalization.CultureInfo.InvariantCulture, out var amount))
        {
            return (long)(amount * 100); // Convertir en centimes
        }
        return 0;
    }

    #endregion
}

/// <summary>
/// Configuration pour PayPal
/// </summary>
public class PayPalSettings
{
    /// <summary>
    /// ID client PayPal
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// Secret client PayPal
    /// </summary>
    public string ClientSecret { get; set; } = string.Empty;

    /// <summary>
    /// Environnement (sandbox ou production)
    /// </summary>
    public string Environment { get; set; } = "sandbox";

    /// <summary>
    /// URL de retour après paiement réussi
    /// </summary>
    public string ReturnUrl { get; set; } = string.Empty;

    /// <summary>
    /// URL de retour après paiement annulé
    /// </summary>
    public string CancelUrl { get; set; } = string.Empty;
}
