import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { BarChart3, FileText, AlertTriangle, Users, TrendingUp, MessageSquare, Shield, Settings } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useAuthStore } from '@/stores/authStore';

export const DashboardManager: React.FC = () => {
  const { user } = useAuthStore();

  // Mock manager stats (à remplacer par de vraies données)
  const managerStats = {
    totalComplaints: 23,
    resolvedComplaints: 18,
    pendingComplaints: 5,
    escalatedIssues: 2,
    teamPerformance: 87,
    customerSatisfaction: 92,
    monthlyReports: 12,
    activeDisputes: 7
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 -mx-6 px-6 py-8 rounded-lg">
        <h1 className="text-3xl font-bold mb-2 text-blue-800">
          Tableau de bord Manager - {user?.firstName}
        </h1>
        <p className="text-blue-700 mb-6">
          Gérez les réclamations, litiges et supervisez les performances de l'équipe
        </p>
        
        {/* Manager Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <AlertTriangle className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-800">{managerStats.pendingComplaints}</div>
            <p className="text-sm text-orange-600">Réclamations en attente</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Shield className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-800">{managerStats.resolvedComplaints}</div>
            <p className="text-sm text-green-600">Réclamations résolues</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-800">{managerStats.teamPerformance}%</div>
            <p className="text-sm text-blue-600">Performance équipe</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-800">{managerStats.customerSatisfaction}%</div>
            <p className="text-sm text-purple-600">Satisfaction client</p>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700">Réclamations Totales</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-800">{managerStats.totalComplaints}</div>
            <p className="text-xs text-orange-600">
              Taux de résolution: {((managerStats.resolvedComplaints / managerStats.totalComplaints) * 100).toFixed(1)}%
            </p>
          </CardContent>
        </Card>

        <Card className="border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-700">Litiges Actifs</CardTitle>
            <Shield className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-800">{managerStats.activeDisputes}</div>
            <p className="text-xs text-red-600">
              {managerStats.escalatedIssues} escaladés
            </p>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Rapports Mensuels</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">{managerStats.monthlyReports}</div>
            <p className="text-xs text-blue-600">
              Ce mois-ci
            </p>
          </CardContent>
        </Card>

        <Card className="border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Performance Équipe</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800">{managerStats.teamPerformance}%</div>
            <p className="text-xs text-green-600">
              Objectif: 85%
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Actions de gestion
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/manager/complaints" className="block">
              <Button className="h-auto p-6 flex-col space-y-3 w-full bg-orange-600 hover:bg-orange-700">
                <AlertTriangle className="h-8 w-8" />
                <span className="text-lg">Réclamations</span>
                <span className="text-sm opacity-90">
                  {managerStats.pendingComplaints} en attente
                </span>
              </Button>
            </Link>
            
            <Link to="/manager/disputes" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-red-200 hover:bg-red-50">
                <Shield className="h-8 w-8 text-red-600" />
                <span className="text-lg">Litiges</span>
                <span className="text-sm text-muted-foreground">
                  {managerStats.activeDisputes} actifs
                </span>
              </Button>
            </Link>
            
            <Link to="/manager/reports" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-blue-200 hover:bg-blue-50">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                <span className="text-lg">Rapports Avancés</span>
                <span className="text-sm text-muted-foreground">Analyses détaillées</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              Performance de l'Équipe
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Résolution réclamations</span>
                <span>{((managerStats.resolvedComplaints / managerStats.totalComplaints) * 100).toFixed(1)}%</span>
              </div>
              <Progress value={(managerStats.resolvedComplaints / managerStats.totalComplaints) * 100} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Satisfaction client</span>
                <span>{managerStats.customerSatisfaction}%</span>
              </div>
              <Progress value={managerStats.customerSatisfaction} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Performance équipe</span>
                <span>{managerStats.teamPerformance}%</span>
              </div>
              <Progress value={managerStats.teamPerformance} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              Alertes Prioritaires
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm">{managerStats.escalatedIssues} litiges escaladés</span>
                </div>
                <Badge variant="destructive">Urgent</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <MessageSquare className="h-4 w-4 text-orange-600" />
                  <span className="text-sm">{managerStats.pendingComplaints} réclamations en attente</span>
                </div>
                <Badge variant="secondary">À traiter</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Performance équipe excellente</span>
                </div>
                <Badge className="bg-green-100 text-green-800">OK</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Management Tips */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="text-blue-800">📊 Conseils de gestion</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-1" />
              <div>
                <p className="font-medium text-orange-800">Priorisez les escalades</p>
                <p className="text-sm text-orange-600">Traitez les litiges escaladés en premier</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <BarChart3 className="h-5 w-5 text-blue-600 mt-1" />
              <div>
                <p className="font-medium text-blue-800">Analysez les tendances</p>
                <p className="text-sm text-blue-600">Utilisez les rapports pour identifier les problèmes récurrents</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
