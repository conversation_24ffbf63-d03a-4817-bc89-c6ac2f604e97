using ServiceLink.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace ServiceLink.Application.DTOs;

/// <summary>
/// DTO pour l'authentification
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Adresse email
    /// </summary>
    [Required(ErrorMessage = "L'email est requis")]
    [EmailAddress(ErrorMessage = "Format d'email invalide")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Mot de passe
    /// </summary>
    [Required(ErrorMessage = "Le mot de passe est requis")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Code 2FA (optionnel)
    /// </summary>
    public string? TwoFactorCode { get; set; }

    /// <summary>
    /// Se souvenir de moi
    /// </summary>
    public bool RememberMe { get; set; } = false;
}

/// <summary>
/// DTO de réponse pour l'authentification
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// Token JWT
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Token de rafraîchissement
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// Informations utilisateur
    /// </summary>
    public UserResponse User { get; set; } = new();

    /// <summary>
    /// Date d'expiration du token
    /// </summary>
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// DTO pour la création d'un utilisateur
/// </summary>
public class RegisterRequest
{
    /// <summary>
    /// Adresse email
    /// </summary>
    [Required(ErrorMessage = "L'email est requis")]
    [EmailAddress(ErrorMessage = "Format d'email invalide")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Mot de passe
    /// </summary>
    [Required(ErrorMessage = "Le mot de passe est requis")]
    [MinLength(8, ErrorMessage = "Le mot de passe doit contenir au moins 8 caractères")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Confirmation du mot de passe
    /// </summary>
    [Required(ErrorMessage = "La confirmation du mot de passe est requise")]
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// Prénom
    /// </summary>
    [Required(ErrorMessage = "Le prénom est requis")]
    [StringLength(50, ErrorMessage = "Le prénom ne peut pas dépasser 50 caractères")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Nom
    /// </summary>
    [Required(ErrorMessage = "Le nom est requis")]
    [StringLength(50, ErrorMessage = "Le nom ne peut pas dépasser 50 caractères")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Type d'utilisateur
    /// </summary>
    [Required(ErrorMessage = "Le type d'utilisateur est requis")]
    public UserRole Role { get; set; } = UserRole.Client;

    /// <summary>
    /// Langue préférée
    /// </summary>
    public string Language { get; set; } = "fr-FR";

    /// <summary>
    /// Fuseau horaire
    /// </summary>
    public string TimeZone { get; set; } = "Europe/Paris";

    /// <summary>
    /// Nom de l'entreprise (pour les prestataires)
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// Numéro SIRET (pour les prestataires)
    /// </summary>
    public string? Siret { get; set; }

    /// <summary>
    /// Description de l'activité (pour les prestataires)
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Numéro de téléphone (pour les prestataires)
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Adresse (pour les prestataires)
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Ville (pour les prestataires)
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// Code postal (pour les prestataires)
    /// </summary>
    public string? PostalCode { get; set; }

    /// <summary>
    /// Pays (pour les prestataires)
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// Acceptation des conditions d'utilisation
    /// </summary>
    [Required(ErrorMessage = "Vous devez accepter les conditions d'utilisation")]
    public bool AcceptTerms { get; set; } = false;
}


/// <summary>
/// DTO pour la demande de rafraîchissement de token
/// </summary>
public class RefreshTokenRequest
{
    /// <summary>
    /// Token de rafraîchissement
    /// </summary>
    [Required(ErrorMessage = "Le token de rafraîchissement est requis")]
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour la réponse de rafraîchissement de token
/// </summary>
public class RefreshTokenResponse
{
    /// <summary>
    /// Nouveau token JWT
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Nouveau token de rafraîchissement
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// Date d'expiration du token
    /// </summary>
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// DTO pour la demande de mot de passe oublié
/// </summary>
public class ForgotPasswordRequest
{
    /// <summary>
    /// Adresse email
    /// </summary>
    [Required(ErrorMessage = "L'email est requis")]
    [EmailAddress(ErrorMessage = "Format d'email invalide")]
    public string Email { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour la demande de réinitialisation de mot de passe
/// </summary>
public class ResetPasswordRequest
{
    /// <summary>
    /// Token de réinitialisation
    /// </summary>
    [Required(ErrorMessage = "Le token est requis")]
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Adresse email
    /// </summary>
    [Required(ErrorMessage = "L'email est requis")]
    [EmailAddress(ErrorMessage = "Format d'email invalide")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Nouveau mot de passe
    /// </summary>
    [Required(ErrorMessage = "Le mot de passe est requis")]
    [MinLength(8, ErrorMessage = "Le mot de passe doit contenir au moins 8 caractères")]
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// Confirmation du nouveau mot de passe
    /// </summary>
    [Required(ErrorMessage = "La confirmation du mot de passe est requise")]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour la demande de confirmation d'email
/// </summary>
public class ConfirmEmailRequest
{
    /// <summary>
    /// Token de confirmation
    /// </summary>
    [Required(ErrorMessage = "Le token est requis")]
    public string Token { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour la mise à jour du profil
/// </summary>
public class UpdateProfileRequest
{
    /// <summary>
    /// Prénom
    /// </summary>
    [Required(ErrorMessage = "Le prénom est requis")]
    [StringLength(50, ErrorMessage = "Le prénom ne peut pas dépasser 50 caractères")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Nom
    /// </summary>
    [Required(ErrorMessage = "Le nom est requis")]
    [StringLength(50, ErrorMessage = "Le nom ne peut pas dépasser 50 caractères")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Adresse email
    /// </summary>
    [Required(ErrorMessage = "L'email est requis")]
    [EmailAddress(ErrorMessage = "Format d'email invalide")]
    public string Email { get; set; } = string.Empty;
}
