using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;

namespace ServiceLink.Infrastructure.Repositories;

public class PasswordResetTokenRepository : IPasswordResetTokenRepository
{
    private readonly ServiceLinkDbContext _dbContext;
    private static readonly TimeSpan TokenLifetime = TimeSpan.FromHours(2);

    public PasswordResetTokenRepository(ServiceLinkDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public string GenerateToken()
    {
        // Génère un token sécurisé
        var bytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes);
    }

    public async Task SaveAsync(Guid userId, string token)
    {
        var entity = new PasswordResetToken
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            Token = token,
            ExpiresAt = DateTime.UtcNow.Add(TokenLifetime),
            CreatedAt = DateTime.UtcNow,
            IsUsed = false
        };
        _dbContext.PasswordResetTokens.Add(entity);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<bool> ValidateTokenAsync(Guid userId, string token)
    {
        var entity = await _dbContext.PasswordResetTokens
            .FirstOrDefaultAsync(x => x.UserId == userId && x.Token == token);
        return entity != null && !entity.IsUsed && entity.ExpiresAt > DateTime.UtcNow;
    }

    public async Task InvalidateTokenAsync(Guid userId, string token)
    {
        var entity = await _dbContext.PasswordResetTokens
            .FirstOrDefaultAsync(x => x.UserId == userId && x.Token == token);
        if (entity != null)
        {
            entity.IsUsed = true;
            await _dbContext.SaveChangesAsync();
        }
    }
}
