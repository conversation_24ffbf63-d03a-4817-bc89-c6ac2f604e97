{"prd_info": {"langue": "Français", "langage_programmation": [".NET Core 9+", "React.js", "TypeScript", "TailwindCSS"], "nom_projet": "servicelink_platform", "date_creation": "2025-07-06", "version": "1.0", "statut": "À réviser par l'équipe technique", "exigences_originales": "Développement d'une plateforme de mise en relation entre prestataires de services et clients, avec gestion complète des profils, réservations, paiements et évaluations"}, "objectifs_produit": [{"objectif": "Faciliter la mise en relation", "description": "Créer une plateforme intuitive permettant aux clients de trouver facilement des prestataires de services qualifiés dans leur région"}, {"objectif": "Optimiser la gestion des services", "description": "Fournir aux prestataires des outils complets pour gérer leurs services, planning et revenus"}, {"objectif": "Assurer la qualité et la confiance", "description": "Mettre en place un système d'évaluation et de certification pour garantir la qualité des services"}], "user_stories": {"client": ["En tant que Client, je veux pouvoir rechercher des prestataires par catégorie et localisation pour trouver le service dont j'ai besoin", "En tant que Client, je veux pouvoir réserver un service en ligne et effectuer le paiement de manière sécurisée", "En tant que Client, je veux pouvoir évaluer et commenter les services reçus pour aider autres utilisateurs"], "prestataire": ["En tant que Prestataire, je veux créer et gérer mon profil professionnel pour attirer des clients", "En tant que Prestataire, je veux gérer mon planning et mes réservations depuis un tableau de bord centralisé", "En tant que Prestataire, je veux recevoir des notifications en temps réel pour les nouvelles demandes de service"], "administrateur": ["En tant qu'Administrateur, je veux superviser les activités de la plateforme et gérer les utilisateurs", "En tant qu'Administrateur, je veux analyser les performances via des tableaux de bord détaillés", "En tant qu'Administrateur, je veux gérer les litiges et assurer la qualité du service"]}, "analyse_concurrentielle": [{"nom": "TaskRabbit", "avantages": ["Interface utilisateur excellente", "Large base d'utilisateurs"], "inconvenients": ["Commission élevée (15%)", "Services limités aux tâches ponctuelles"], "position_marche": [0.3, 0.8]}, {"nom": "Fiverr", "avantages": ["Marché global", "Système de packages flexible"], "inconvenients": ["Axé sur les services numériques", "Moins adapté aux services locaux"], "position_marche": [0.4, 0.9]}, {"nom": "HelloWork Services", "avantages": ["Spécialisé marché français", "Vérification des prestataires"], "inconvenients": ["Interface da<PERSON>e", "Fonctionnalités limitées"], "position_marche": [0.2, 0.3]}, {"nom": "Leboncoin Services", "avantages": ["Forte notoriété en France", "Large audience"], "inconvenients": ["Pas de système de paiement intégré", "Peu de protection"], "position_marche": [0.1, 0.2]}, {"nom": "ProntoPro", "avantages": ["Focus sur les devis", "Bon système de matching"], "inconvenients": ["Interface complexe", "Commission élevée"], "position_marche": [0.5, 0.6]}, {"nom": "<PERSON><PERSON><PERSON>", "avantages": ["Services à domicile", "Prestataires vérifiés"], "inconvenients": ["Limité aux services de beauté/bien-être"], "position_marche": [0.8, 0.7]}, {"nom": "SuperMano", "avantages": ["Services de bricolage spécialisés", "Prestataires certifiés"], "inconvenients": ["<PERSON>che limitée", "Couverture géographique restreinte"], "position_marche": [0.9, 0.5]}], "architecture_technique": {"backend": {"framework": ".NET Core 9+", "patterns": ["Clean Architecture", "CQRS (Command Query Responsibility Segregation)", "Repository Pattern", "Microservices"], "base_de_donnees": "PostgreSQL", "orm": "Entity Framework Core", "authentification": {"type": "JWT (JSON Web Tokens)", "securite_avancee": "Multi-Factor Authentication (MFA)", "controle_acces": "Role-Based Access Control (RBAC)"}, "api": "RESTful API avec Swagger/OpenAPI", "caching": "Redis", "file_storage": "Azure Blob Storage ou AWS S3", "real_time": "SignalR pour notifications", "tests": "xUnit pour tests unitaires et d'intégration", "logging": "Serilog pour audit et monitoring"}, "frontend": {"framework": "React.js 18+", "langage": "TypeScript", "bundler": "Vite", "styling": "TailwindCSS", "composants": "Shadcn UI", "state_management": "<PERSON><PERSON><PERSON> ou <PERSON><PERSON>", "routing": "React Router DOM", "forms": "React Hook Form + Zod validation", "http_client": "Axios avec intercepteurs"}, "mobile": {"statut": "Phase 3", "technologie": "Flutter"}}, "exigences_fonctionnelles": {"P0_must_have": [{"id": "AUTH001", "description": "Système d'authentification multi-rôles (JWT + 2FA)", "priorite": "P0"}, {"id": "USER002", "description": "Gestion des profils utilisateurs (7 types)", "priorite": "P0"}, {"id": "SERV003", "description": "Catalogue de services avec catégories/sous-catégories", "priorite": "P0"}, {"id": "BOOK004", "description": "Système de réservation en temps réel", "priorite": "P0"}, {"id": "PAY005", "description": "Intégration paiements (Stripe, PayPal, Banque)", "priorite": "P0"}, {"id": "NOTIF006", "description": "Système de notifications push/email/SMS", "priorite": "P0"}, {"id": "EVAL007", "description": "Système d'évaluation et commentaires", "priorite": "P0"}, {"id": "ADMIN008", "description": "Panel d'administration complet", "priorite": "P0"}], "P1_should_have": [{"id": "GEO009", "description": "Géolocalisation et cartes interactives", "priorite": "P1"}, {"id": "CHAT010", "description": "Messagerie intégrée entre utilisateurs", "priorite": "P1"}, {"id": "SCHED011", "description": "Gestion avancée du planning", "priorite": "P1"}, {"id": "REPORT012", "description": "Reporting et analytics avancés", "priorite": "P1"}, {"id": "MOBILE013", "description": "Applications mobiles natives", "priorite": "P1"}, {"id": "PROMO014", "description": "Système de codes promos et réductions", "priorite": "P1"}], "P2_nice_to_have": [{"id": "AI015", "description": "Recommandations basées sur IA", "priorite": "P2"}, {"id": "MULTI016", "description": "Support multi-langue", "priorite": "P2"}, {"id": "SOCIAL017", "description": "Intégration réseaux sociaux", "priorite": "P2"}, {"id": "VIDEO018", "description": "<PERSON><PERSON><PERSON> vidéo intég<PERSON>", "priorite": "P2"}, {"id": "IOT019", "description": "Intégration objets connectés", "priorite": "P2"}]}, "specifications_api": {"authentication": ["POST /api/auth/login", "POST /api/auth/register", "POST /api/auth/refresh", "POST /api/auth/logout", "POST /api/auth/forgot-password", "POST /api/auth/reset-password"], "users": ["GET /api/users/profile", "PUT /api/users/profile", "GET /api/users/{id}", "POST /api/users/avatar", "GET /api/users/search", "PUT /api/users/{id}/status"], "services": ["GET /api/services", "POST /api/services", "GET /api/services/{id}", "PUT /api/services/{id}", "DELETE /api/services/{id}", "GET /api/services/categories", "GET /api/services/search"], "bookings": ["GET /api/bookings", "POST /api/bookings", "GET /api/bookings/{id}", "PUT /api/bookings/{id}/status", "DELETE /api/bookings/{id}", "GET /api/bookings/provider/{id}", "GET /api/bookings/client/{id}"], "payments": ["POST /api/payments/create-intent", "POST /api/payments/confirm", "GET /api/payments/history", "POST /api/payments/refund", "GET /api/payments/transactions"], "notifications": ["GET /api/notifications", "POST /api/notifications/send", "PUT /api/notifications/{id}/read", "DELETE /api/notifications/{id}"], "reviews": ["GET /api/reviews/service/{id}", "POST /api/reviews", "PUT /api/reviews/{id}", "DELETE /api/reviews/{id}"]}, "roles_utilisateurs": [{"role": "Admin Global", "description": "Super Administrateur avec tous les droits", "permissions": ["Configuration plateforme complète", "Gestion des comptes et rôles", "Gestion types et catégories de services", "Supervision paiements et finances"]}, {"role": "Manager", "description": "Gestionnaire avec droits étendus", "permissions": ["Fonctionnalités du support", "Gestion réclamations et litiges", "Accès rapports avancés"]}, {"role": "Support", "description": "Support client", "permissions": ["Accès statistiques globales", "Gestion chat et ticketing", "Support client"]}, {"role": "Superviseur", "description": "Superviseur de qualité", "permissions": ["Vérification et validation documents", "Suspension comptes non-conformes"]}, {"role": "Client", "description": "Utilisateur final demandeur de services", "permissions": ["Recherche et réservation de services", "Gestion profil personnel", "Évaluation des prestataires"]}, {"role": "Presta<PERSON>", "description": "Fournisseur de services", "permissions": ["Gestion profil professionnel", "Gestion calendrier et disponibilités", "Suivi financier et paiements"]}], "categories_services": ["<PERSON><PERSON><PERSON>", "Bricolage", "Jardinage", "<PERSON><PERSON><PERSON><PERSON>", "Électricité", "Cours particuliers", "Aide aux seniors", "Beauté et bien-être", "<PERSON><PERSON><PERSON>", "Déménagement", "Réparation informatique", "Garde d'animaux", "Cuisine à domicile", "Coaching sportif", "Services automobiles"], "fonctionnalites_interface": {"client": {"pages_principales": ["Page d'accueil avec recherche", "Résultats de recherche avec filtres", "Profils des prestataires", "Processus de réservation", "Espace personnel", "Historique des services", "Système de paiement"], "composants_cles": ["Barre de recherche intelligente avec auto-complétion", "Système de filtres dynamiques", "Cartes de services avec images et évaluations", "Calendrier de disponibilité interactif", "Processus de paiement sécurisé"]}, "prestataire": {"pages_principales": ["Dashboard prestataire", "Gestion du profil", "Calendrier des missions", "Messagerie clients", "Suivi financier", "Gestion des avis"], "fonctionnalites": ["Création profil détaillé avec média", "Gestion disponibilités et calendrier", "Gestion demandes de service", "Messagerie intégrée avec clients", "Suivi paiements et trésorerie"]}, "administration": {"dashboards": ["Dashboard Admin Global", "Dashboard Support", "Dashboard Manager", "Dashboard Superviseur"], "fonctionnalites": ["Statistiques et analytics", "Gestion des utilisateurs", "Modération des contenus", "Gestion des litiges", "Suivi des paiements"]}}, "systeme_paiements": {"passerelles_internationales": ["Stripe", "PayPal", "MasterCard", "VisaCard"], "paiements_mobiles_afrique": ["MTN Mobile Money", "Orange Money", "Flutterwave"], "fonctionnalites": ["Paiement sécurisé intégré", "Commission automatique (10-20%)", "Paie<PERSON> différé (Buy Now, Pay Later)", "Gestion des transactions", "Système de facturation", "Historique des paiements"]}, "specifications_securite": {"authentification": "JWT avec refresh tokens", "autorisation": "RBAC granulaire", "chiffrement": "HTTPS obligatoire", "validation": "Validation côté client et serveur", "audit": "Logs complets des actions utilisateurs", "conformite": "Respect RGPD"}, "plan_developpement": {"phase_1": {"duree": "Mois 1-4", "focus": "MVP Backend", "livrables": ["Architecture de base et authentification", "API utilisateurs et services", "Base de données et migrations", "Système de réservation basique", "Tests unitaires et intégration"]}, "phase_2": {"duree": "Mois 5-8", "focus": "Frontend et Intégrations", "livrables": ["Interface utilisateur React", "Intégration API backend", "Système de paiement", "Notifications et messagerie", "Tests end-to-end"]}, "phase_3": {"duree": "<PERSON><PERSON> 9-12", "focus": "Optimisation et Déploiement", "livrables": ["Applications mobiles", "Performance et sécurité", "Analytics et reporting", "Déploiement production", "Support et maintenance"]}}, "objectifs_business": {"annee_1": {"utilisateurs_clients": "5 000 utilisateurs actifs", "prestataires": "1 000 prestataires actifs", "revenus_prevus": "30 000 €", "investissement": "80 000 €"}, "sources_revenus": ["Commission automatique sur missions (10-20%)", "Abonnements premium prestataires", "Paiement différé avec frais intégrés", "Partenariats et offres sponsorisées", "Fidélisation avec cashback"]}, "questions_ouvertes": [{"question": "Gestion multi-devises", "description": "Faut-il supporter plusieurs devises pour l'expansion internationale ?"}, {"question": "Système de franchise", "description": "Comment gérer les prestataires partenaires avec leur propre marque ?"}, {"question": "Intégration ERP", "description": "Quels systèmes de gestion externe intégrer pour les grandes entreprises ?"}, {"question": "Conformité RGPD", "description": "Quelles mesures spécifiques pour la protection des données personnelles ?"}, {"question": "Système de backup", "description": "Quelle stratégie de sauvegarde et récupération des données ?"}], "exigences_qualite": {"code": ["Documentation complète de chaque ligne de code", "Utilisation des versions de packages récentes et stables", "Vérification de la documentation officielle avant implémentation", "Tests unitaires et d'intégration obligatoires", "Code review systématique"], "fonctionnel": ["100% des fonctionnalités opérationnelles", "Tous les boutons cliquables et fonctionnels", "Expérience utilisateur fluide", "Design professionnel et responsive", "Sécurité <PERSON>e"], "performance": ["Temps de chargement optimisés", "Gestion efficace des données", "Scalabilité horizontale", "Monitoring des performances"]}, "notes_implementation": ["Développement en deux phases distinctes : Backend complet puis Frontend", "Pas de fake data ou dummy data - données réelles uniquement", "Privilégier les méthodes simples et à jour", "Application 100% opérationnelle et sécurisée avant livraison", "Tous les fichiers accessibles à tous les membres de l'équipe", "Suivi strict de l'ordre de développement spécifié"]}