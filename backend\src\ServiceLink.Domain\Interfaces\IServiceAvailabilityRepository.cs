using ServiceLink.Domain.Entities;

namespace ServiceLink.Domain.Interfaces;

/// <summary>
/// Interface pour le repository des disponibilités
/// </summary>
public interface IServiceAvailabilityRepository : IRepository<ServiceAvailability>
{
    /// <summary>
    /// Obtient les disponibilités d'un service
    /// </summary>
    Task<IEnumerable<ServiceAvailability>> GetByServiceIdAsync(Guid serviceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les disponibilités d'un prestataire
    /// </summary>
    Task<IEnumerable<ServiceAvailability>> GetByProviderIdAsync(Guid providerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les disponibilités pour une période
    /// </summary>
    Task<IEnumerable<ServiceAvailability>> GetForPeriodAsync(Guid serviceId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie la disponibilité pour une date/heure
    /// </summary>
    Task<bool> IsAvailableAsync(Guid serviceId, DateTime dateTime, int durationMinutes, CancellationToken cancellationToken = default);
}
