using MediatR;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Interfaces;

namespace ServiceLink.Application.Handlers;

public class LoginCommandHandler : IRequestHandler<LoginCommand, LoginResponse>
{
    private readonly IPasswordService _passwordService;
    private readonly IEmailService _emailService;
    private readonly IUserRepository _userRepository;
    private readonly IJwtService _jwtService;

    public LoginCommandHandler(IPasswordService passwordService, IEmailService emailService, IUserRepository userRepository, IJwtService jwtService)
    {
        _passwordService = passwordService;
        _emailService = emailService;
        _userRepository = userRepository;
        _jwtService = jwtService;
    }

    public async Task<LoginResponse> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        // 1. Récupérer l'utilisateur par email
        var user = await _userRepository.GetByEmailAsync(request.Email);
        if (user == null)
            throw new UnauthorizedAccessException("Utilisateur ou mot de passe invalide.");

        // 2. Vérifier que l'utilisateur est actif
        if (!user.IsActive)
            throw new UnauthorizedAccessException("Compte inactif ou désactivé.");

        // 3. Vérifier le mot de passe
        var passwordValid = _passwordService.VerifyPassword(request.Password, user.PasswordHash, user.PasswordSalt);
        if (!passwordValid)
            throw new UnauthorizedAccessException("Utilisateur ou mot de passe invalide.");

        // 4. Enregistrer la connexion réussie
        user.RecordSuccessfulLogin();
        await _userRepository.UpdateAsync(user);

        // 5. Générer les tokens
        var jwtToken = _jwtService.GenerateJwtToken(user);
        var refreshToken = _jwtService.GenerateRefreshToken(user);
        var expiresAt = DateTime.UtcNow.AddMinutes(120); // À adapter selon config

        // 5. Mapper UserResponse
        var userResponse = new UserResponse
        {
            Id = user.Id,
            Email = user.Email.Value,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            RoleDescription = user.Role.ToString(),
            IsEmailConfirmed = user.IsEmailConfirmed,
            IsPhoneConfirmed = user.IsPhoneConfirmed,
            IsActive = user.IsActive,
            IsTwoFactorEnabled = false, // À compléter si 2FA
            AvatarUrl = null, // À compléter si besoin
            ProfileCompletionPercentage = user.ProfileCompletionPercentage,
            Language = "fr-FR", // À compléter si stocké
            TimeZone = "Europe/Paris", // À compléter si stocké
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            LastLoginAt = user.LastLoginAt
        };

        // 6. Retourner la réponse
        return new LoginResponse
        {
            Token = jwtToken,
            RefreshToken = refreshToken,
            User = userResponse,
            ExpiresAt = expiresAt
        };
    }
}
