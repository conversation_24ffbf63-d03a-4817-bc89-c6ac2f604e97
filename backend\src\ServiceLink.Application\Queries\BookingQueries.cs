using MediatR;
using ServiceLink.Application.DTOs;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.Queries;

/// <summary>
/// Query pour obtenir une réservation par ID
/// </summary>
public class GetBookingByIdQuery : IRequest<BookingResponse?>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID de l'utilisateur qui fait la demande (pour vérifier les permissions)
    /// </summary>
    public Guid UserId { get; set; }

    public GetBookingByIdQuery(Guid bookingId, Guid userId)
    {
        BookingId = bookingId;
        UserId = userId;
    }
}

/// <summary>
/// Query pour obtenir les réservations d'un client
/// </summary>
public class GetClientBookingsQuery : IRequest<PagedResult<BookingResponse>>
{
    /// <summary>
    /// ID du client
    /// </summary>
    public Guid ClientId { get; set; }

    /// <summary>
    /// Statuts à filtrer
    /// </summary>
    public List<BookingStatus>? StatusFilter { get; set; }

    /// <summary>
    /// Date de début pour le filtrage
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Date de fin pour le filtrage
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Numéro de page
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Taille de page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Champ de tri
    /// </summary>
    public string SortBy { get; set; } = "ScheduledDate";

    /// <summary>
    /// Direction du tri
    /// </summary>
    public string SortDirection { get; set; } = "desc";

    public GetClientBookingsQuery(Guid clientId)
    {
        ClientId = clientId;
    }
}

/// <summary>
/// Query pour obtenir les réservations d'un prestataire
/// </summary>
public class GetProviderBookingsQuery : IRequest<PagedResult<BookingResponse>>
{
    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Statuts à filtrer
    /// </summary>
    public List<BookingStatus>? StatusFilter { get; set; }

    /// <summary>
    /// Date de début pour le filtrage
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Date de fin pour le filtrage
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Numéro de page
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Taille de page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Champ de tri
    /// </summary>
    public string SortBy { get; set; } = "ScheduledDate";

    /// <summary>
    /// Direction du tri
    /// </summary>
    public string SortDirection { get; set; } = "desc";

    public GetProviderBookingsQuery(Guid providerId)
    {
        ProviderId = providerId;
    }
}

/// <summary>
/// Query pour obtenir les statistiques de réservation d'un utilisateur
/// </summary>
public class GetBookingStatsQuery : IRequest<BookingStatsResponse>
{
    /// <summary>
    /// ID de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Période de début
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Période de fin
    /// </summary>
    public DateTime? EndDate { get; set; }

    public GetBookingStatsQuery(Guid userId, DateTime? startDate = null, DateTime? endDate = null)
    {
        UserId = userId;
        StartDate = startDate;
        EndDate = endDate;
    }
}

/// <summary>
/// Query pour rechercher des créneaux disponibles
/// </summary>
public class SearchAvailableSlotsQuery : IRequest<List<AvailableSlotDto>>
{
    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Date de début de recherche
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Date de fin de recherche
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Durée souhaitée en minutes
    /// </summary>
    public int DurationMinutes { get; set; }

    /// <summary>
    /// Adresse du client (pour calculer la distance)
    /// </summary>
    public ServiceAddressDto? ClientAddress { get; set; }

    public SearchAvailableSlotsQuery(Guid serviceId, DateTime startDate, DateTime endDate, int durationMinutes)
    {
        ServiceId = serviceId;
        StartDate = startDate;
        EndDate = endDate;
        DurationMinutes = durationMinutes;
    }
}

/// <summary>
/// Query pour obtenir les réservations à venir (notifications)
/// </summary>
public class GetUpcomingBookingsQuery : IRequest<List<BookingResponse>>
{
    /// <summary>
    /// ID de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Nombre d'heures à l'avance
    /// </summary>
    public int HoursAhead { get; set; } = 24;

    public GetUpcomingBookingsQuery(Guid userId, int hoursAhead = 24)
    {
        UserId = userId;
        HoursAhead = hoursAhead;
    }
}

/// <summary>
/// Query pour obtenir les réservations expirées
/// </summary>
public class GetExpiredBookingsQuery : IRequest<List<BookingResponse>>
{
    /// <summary>
    /// Limite de résultats
    /// </summary>
    public int Limit { get; set; } = 100;

    public GetExpiredBookingsQuery(int limit = 100)
    {
        Limit = limit;
    }
}

/// <summary>
/// Résultat paginé générique
/// </summary>
public class PagedResult<T>
{
    /// <summary>
    /// Éléments de la page courante
    /// </summary>
    public List<T> Items { get; set; } = new();

    /// <summary>
    /// Nombre total d'éléments
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Numéro de page courante
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Taille de page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Nombre total de pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Indique s'il y a une page suivante
    /// </summary>
    public bool HasNextPage { get; set; }

    /// <summary>
    /// Indique s'il y a une page précédente
    /// </summary>
    public bool HasPreviousPage { get; set; }
}

/// <summary>
/// Réponse pour les statistiques de réservation
/// </summary>
public class BookingStatsResponse
{
    /// <summary>
    /// Nombre total de réservations
    /// </summary>
    public int TotalBookings { get; set; }

    /// <summary>
    /// Nombre de réservations terminées
    /// </summary>
    public int CompletedBookings { get; set; }

    /// <summary>
    /// Nombre de réservations annulées
    /// </summary>
    public int CancelledBookings { get; set; }

    /// <summary>
    /// Nombre de réservations en attente
    /// </summary>
    public int PendingBookings { get; set; }

    /// <summary>
    /// Chiffre d'affaires total en centimes
    /// </summary>
    public long TotalRevenue { get; set; }

    /// <summary>
    /// Chiffre d'affaires formaté
    /// </summary>
    public string TotalRevenueFormatted { get; set; } = string.Empty;

    /// <summary>
    /// Note moyenne reçue
    /// </summary>
    public decimal AverageRating { get; set; }

    /// <summary>
    /// Nombre total d'avis
    /// </summary>
    public int TotalReviews { get; set; }

    /// <summary>
    /// Taux de completion (%)
    /// </summary>
    public decimal CompletionRate { get; set; }

    /// <summary>
    /// Taux d'annulation (%)
    /// </summary>
    public decimal CancellationRate { get; set; }

    /// <summary>
    /// Statistiques par mois
    /// </summary>
    public List<MonthlyStatsDto> MonthlyStats { get; set; } = new();
}

/// <summary>
/// DTO pour les statistiques mensuelles
/// </summary>
public class MonthlyStatsDto
{
    /// <summary>
    /// Année
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// Mois
    /// </summary>
    public int Month { get; set; }

    /// <summary>
    /// Nom du mois
    /// </summary>
    public string MonthName { get; set; } = string.Empty;

    /// <summary>
    /// Nombre de réservations
    /// </summary>
    public int BookingCount { get; set; }

    /// <summary>
    /// Chiffre d'affaires en centimes
    /// </summary>
    public long Revenue { get; set; }

    /// <summary>
    /// Chiffre d'affaires formaté
    /// </summary>
    public string RevenueFormatted { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour un créneau disponible
/// </summary>
public class AvailableSlotDto
{
    /// <summary>
    /// Date et heure du créneau
    /// </summary>
    public DateTime DateTime { get; set; }

    /// <summary>
    /// Durée disponible en minutes
    /// </summary>
    public int AvailableDurationMinutes { get; set; }

    /// <summary>
    /// Prix pour ce créneau en centimes
    /// </summary>
    public long Price { get; set; }

    /// <summary>
    /// Prix formaté
    /// </summary>
    public string PriceFormatted { get; set; } = string.Empty;

    /// <summary>
    /// Indique si c'est un créneau urgent
    /// </summary>
    public bool IsUrgent { get; set; }

    /// <summary>
    /// Distance du prestataire en km
    /// </summary>
    public double? DistanceKm { get; set; }

    /// <summary>
    /// Temps de trajet estimé en minutes
    /// </summary>
    public int? TravelTimeMinutes { get; set; }
}
