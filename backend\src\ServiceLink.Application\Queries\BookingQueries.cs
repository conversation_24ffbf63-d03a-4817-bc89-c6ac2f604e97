using MediatR;
using ServiceLink.Application.DTOs;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.Queries;

/// <summary>
/// Query pour obtenir une réservation par son ID
/// </summary>
public class GetBookingByIdQuery : IRequest<BookingResponse?>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID de l'utilisateur qui fait la demande (pour vérifier les permissions)
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur
    /// </summary>
    public UserRole UserRole { get; set; }
}

/// <summary>
/// Query pour obtenir les réservations d'un utilisateur
/// </summary>
public class GetUserBookingsQuery : IRequest<PagedResult<BookingResponse>>
{
    /// <summary>
    /// ID de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur
    /// </summary>
    public UserRole UserRole { get; set; }

    /// <summary>
    /// Statuts à inclure
    /// </summary>
    public List<BookingStatus>? Statuses { get; set; }

    /// <summary>
    /// Date de début de la période
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Date de fin de la période
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Numéro de page
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Taille de la page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Champ de tri
    /// </summary>
    public string SortBy { get; set; } = "ScheduledDate";

    /// <summary>
    /// Ordre de tri
    /// </summary>
    public string SortOrder { get; set; } = "desc";
}

/// <summary>
/// Query pour rechercher des réservations
/// </summary>
public class SearchBookingsQuery : IRequest<PagedResult<BookingResponse>>
{
    /// <summary>
    /// ID du client (optionnel)
    /// </summary>
    public Guid? ClientId { get; set; }

    /// <summary>
    /// ID du prestataire (optionnel)
    /// </summary>
    public Guid? ProviderId { get; set; }

    /// <summary>
    /// ID du service (optionnel)
    /// </summary>
    public Guid? ServiceId { get; set; }

    /// <summary>
    /// Statuts à inclure
    /// </summary>
    public List<BookingStatus>? Statuses { get; set; }

    /// <summary>
    /// Date de début de la période
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Date de fin de la période
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Montant minimum
    /// </summary>
    public long? MinAmount { get; set; }

    /// <summary>
    /// Montant maximum
    /// </summary>
    public long? MaxAmount { get; set; }

    /// <summary>
    /// Réservations urgentes uniquement
    /// </summary>
    public bool? IsUrgentOnly { get; set; }

    /// <summary>
    /// Réservations récurrentes uniquement
    /// </summary>
    public bool? IsRecurringOnly { get; set; }

    /// <summary>
    /// Terme de recherche libre
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Numéro de page
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Taille de la page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Champ de tri
    /// </summary>
    public string SortBy { get; set; } = "ScheduledDate";

    /// <summary>
    /// Ordre de tri
    /// </summary>
    public string SortOrder { get; set; } = "desc";

    /// <summary>
    /// ID de l'utilisateur qui fait la demande
    /// </summary>
    public Guid RequestingUserId { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur qui fait la demande
    /// </summary>
    public UserRole RequestingUserRole { get; set; }
}

/// <summary>
/// Query pour obtenir les créneaux disponibles
/// </summary>
public class GetAvailableSlotsQuery : IRequest<List<AvailableSlotDto>>
{
    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Date de début de la période
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Date de fin de la période
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Durée souhaitée en minutes
    /// </summary>
    public int DurationMinutes { get; set; }

    /// <summary>
    /// Fuseau horaire du client
    /// </summary>
    public string TimeZone { get; set; } = "UTC";
}

/// <summary>
/// Query pour obtenir les statistiques de réservation
/// </summary>
public class GetBookingStatsQuery : IRequest<BookingStatsDto>
{
    /// <summary>
    /// ID de l'utilisateur (optionnel, pour filtrer)
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur
    /// </summary>
    public UserRole? UserRole { get; set; }

    /// <summary>
    /// Date de début de la période
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Date de fin de la période
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Groupement des statistiques (day, week, month, year)
    /// </summary>
    public string GroupBy { get; set; } = "month";
}

/// <summary>
/// Query pour obtenir le calendrier d'un prestataire
/// </summary>
public class GetProviderCalendarQuery : IRequest<List<CalendarEventDto>>
{
    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Date de début de la période
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Date de fin de la période
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Inclure les disponibilités
    /// </summary>
    public bool IncludeAvailabilities { get; set; } = true;

    /// <summary>
    /// Inclure les réservations
    /// </summary>
    public bool IncludeBookings { get; set; } = true;

    /// <summary>
    /// Fuseau horaire
    /// </summary>
    public string TimeZone { get; set; } = "UTC";
}

/// <summary>
/// Query pour obtenir les réservations à venir
/// </summary>
public class GetUpcomingBookingsQuery : IRequest<List<BookingResponse>>
{
    /// <summary>
    /// ID de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur
    /// </summary>
    public UserRole UserRole { get; set; }

    /// <summary>
    /// Nombre d'heures à l'avance
    /// </summary>
    public int HoursAhead { get; set; } = 24;

    /// <summary>
    /// Nombre maximum de résultats
    /// </summary>
    public int MaxResults { get; set; } = 10;
}

/// <summary>
/// Query pour obtenir les réservations nécessitant une action
/// </summary>
public class GetBookingsRequiringActionQuery : IRequest<List<BookingResponse>>
{
    /// <summary>
    /// ID de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur
    /// </summary>
    public UserRole UserRole { get; set; }

    /// <summary>
    /// Types d'actions (confirm, start, complete, review)
    /// </summary>
    public List<string>? ActionTypes { get; set; }
}

/// <summary>
/// Query pour obtenir l'historique des réservations
/// </summary>
public class GetBookingHistoryQuery : IRequest<List<BookingHistoryDto>>
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID de l'utilisateur qui fait la demande
    /// </summary>
    public Guid RequestingUserId { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur qui fait la demande
    /// </summary>
    public UserRole RequestingUserRole { get; set; }
}

/// <summary>
/// Query pour valider une réservation
/// </summary>
public class ValidateBookingQuery : IRequest<BookingValidationResult>
{
    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Date et heure prévues
    /// </summary>
    public DateTime ScheduledDate { get; set; }

    /// <summary>
    /// Durée en minutes
    /// </summary>
    public int DurationMinutes { get; set; }

    /// <summary>
    /// Adresse du service
    /// </summary>
    public ServiceAddressDto ServiceAddress { get; set; } = new();

    /// <summary>
    /// ID de la réservation à exclure (pour les modifications)
    /// </summary>
    public Guid? ExcludeBookingId { get; set; }
}

/// <summary>
/// DTO pour un créneau disponible
/// </summary>
public class AvailableSlotDto
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public int DurationMinutes { get; set; }
    public long Price { get; set; }
    public string PriceFormatted { get; set; } = string.Empty;
    public bool IsUrgent { get; set; }
    public decimal? UrgentSurcharge { get; set; }
    public string TimeZone { get; set; } = "UTC";
}

/// <summary>
/// DTO pour les statistiques de réservation
/// </summary>
public class BookingStatsDto
{
    public int TotalBookings { get; set; }
    public int PendingBookings { get; set; }
    public int ConfirmedBookings { get; set; }
    public int CompletedBookings { get; set; }
    public int CancelledBookings { get; set; }
    public long TotalRevenue { get; set; }
    public string TotalRevenueFormatted { get; set; } = string.Empty;
    public decimal AverageBookingValue { get; set; }
    public string AverageBookingValueFormatted { get; set; } = string.Empty;
    public decimal CompletionRate { get; set; }
    public decimal CancellationRate { get; set; }
    public List<BookingStatsGroupDto> GroupedStats { get; set; } = new();
}

/// <summary>
/// DTO pour les statistiques groupées
/// </summary>
public class BookingStatsGroupDto
{
    public string Period { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public int BookingCount { get; set; }
    public long Revenue { get; set; }
    public string RevenueFormatted { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour un événement de calendrier
/// </summary>
public class CalendarEventDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Type { get; set; } = string.Empty; // booking, availability, break
    public string Status { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public bool IsAllDay { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// DTO pour l'historique d'une réservation
/// </summary>
public class BookingHistoryDto
{
    public Guid Id { get; set; }
    public DateTime Timestamp { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public UserRole? UserRole { get; set; }
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Résultat de validation d'une réservation
/// </summary>
public class BookingValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public long EstimatedPrice { get; set; }
    public string EstimatedPriceFormatted { get; set; } = string.Empty;
    public DateTime? SuggestedAlternativeDate { get; set; }
    public List<AvailableSlotDto> AlternativeSlots { get; set; } = new();
}

/// <summary>
/// Résultat paginé générique
/// </summary>
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}
