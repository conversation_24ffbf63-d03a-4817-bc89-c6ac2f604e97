import { create } from 'zustand';
import * as signalR from '@microsoft/signalr';

export type NotificationType = 
  | 'BookingCreated'
  | 'BookingConfirmed'
  | 'BookingRejected'
  | 'BookingCancelled'
  | 'ServiceStarted'
  | 'ServiceCompleted'
  | 'PaymentReceived'
  | 'ReviewReceived'
  | 'SystemAlert'
  | 'Info'
  | 'Warning'
  | 'Error';

export type NotificationPriority = 'Low' | 'Normal' | 'High' | 'Critical';

export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  isRead: boolean;
  createdAt: Date;
  expiresAt?: Date;
  data?: Record<string, any>;
  actionUrl?: string;
  actionText?: string;
}

export interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  action?: {
    text: string;
    onClick: () => void;
  };
}

export interface NotificationState {
  // State
  notifications: Notification[];
  unreadCount: number;
  toasts: ToastNotification[];
  isConnected: boolean;
  connection: signalR.HubConnection | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  initializeSignalR: (token: string) => Promise<void>;
  disconnectSignalR: () => Promise<void>;
  loadNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  clearAllNotifications: () => Promise<void>;
  showToast: (toast: Omit<ToastNotification, 'id'>) => void;
  hideToast: (id: string) => void;
  clearError: () => void;
}

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5280/api';
const HUB_URL = import.meta.env.VITE_SIGNALR_URL || 'http://localhost:5280/hubs';

export const useNotificationStore = create<NotificationState>((set, get) => ({
  // Initial state
  notifications: [],
  unreadCount: 0,
  toasts: [],
  isConnected: false,
  connection: null,
  isLoading: false,
  error: null,

  // Actions
  initializeSignalR: async (token: string) => {
    try {
      const connection = new signalR.HubConnectionBuilder()
        .withUrl(`${HUB_URL}/notifications`, {
          accessTokenFactory: () => token,
        })
        .withAutomaticReconnect()
        .build();

      // Set up event handlers
      connection.on('ReceiveNotification', (notification: Notification) => {
        set((state) => ({
          notifications: [notification, ...state.notifications],
          unreadCount: state.unreadCount + 1,
        }));

        // Show toast for high priority notifications
        if (notification.priority === 'High' || notification.priority === 'Critical') {
          get().showToast({
            type: notification.type === 'Error' ? 'error' : 'info',
            title: notification.title,
            message: notification.message,
            duration: notification.priority === 'Critical' ? 0 : 5000,
          });
        }
      });

      connection.on('BookingNotification', (data: any) => {
        const notification: Notification = {
          id: crypto.randomUUID(),
          userId: data.userId || '',
          type: data.type,
          title: data.title || 'Booking Update',
          message: data.message,
          priority: 'Normal',
          isRead: false,
          createdAt: new Date(data.timestamp || Date.now()),
          data: data,
        };

        set((state) => ({
          notifications: [notification, ...state.notifications],
          unreadCount: state.unreadCount + 1,
        }));

        // Show toast for booking notifications
        get().showToast({
          type: 'info',
          title: notification.title,
          message: notification.message,
          duration: 4000,
        });
      });

      connection.on('ChatMessage', (message: any) => {
        // Handle chat messages if needed
        get().showToast({
          type: 'info',
          title: 'New Message',
          message: `${message.senderName}: ${message.content}`,
          duration: 3000,
        });
      });

      connection.onreconnecting(() => {
        set({ isConnected: false });
      });

      connection.onreconnected(() => {
        set({ isConnected: true });
      });

      connection.onclose(() => {
        set({ isConnected: false, connection: null });
      });

      await connection.start();
      
      set({
        connection,
        isConnected: true,
        error: null,
      });

    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to connect to notifications',
        isConnected: false,
      });
      throw error;
    }
  },

  disconnectSignalR: async () => {
    const { connection } = get();
    if (connection) {
      try {
        await connection.stop();
      } catch (error) {
        console.error('Error disconnecting SignalR:', error);
      }
    }
    set({
      connection: null,
      isConnected: false,
    });
  },

  loadNotifications: async () => {
    set({ isLoading: true, error: null });

    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/notifications`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load notifications');
      }

      const notifications: Notification[] = await response.json();
      const unreadCount = notifications.filter(n => !n.isRead).length;
      
      set({
        notifications,
        unreadCount,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load notifications',
      });
    }
  },

  markAsRead: async (id: string) => {
    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/notifications/${id}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }

      set((state) => ({
        notifications: state.notifications.map(notification =>
          notification.id === id 
            ? { ...notification, isRead: true }
            : notification
        ),
        unreadCount: Math.max(0, state.unreadCount - 1),
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to mark notification as read',
      });
    }
  },

  markAllAsRead: async () => {
    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/notifications/read-all`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }

      set((state) => ({
        notifications: state.notifications.map(notification => ({
          ...notification,
          isRead: true,
        })),
        unreadCount: 0,
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to mark all notifications as read',
      });
    }
  },

  deleteNotification: async (id: string) => {
    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/notifications/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete notification');
      }

      set((state) => {
        const notification = state.notifications.find(n => n.id === id);
        const wasUnread = notification && !notification.isRead;
        
        return {
          notifications: state.notifications.filter(n => n.id !== id),
          unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount,
        };
      });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to delete notification',
      });
    }
  },

  clearAllNotifications: async () => {
    try {
      const authStore = (window as any).authStore?.getState?.();
      const token = authStore?.token;

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/notifications/clear-all`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to clear all notifications');
      }

      set({
        notifications: [],
        unreadCount: 0,
      });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to clear all notifications',
      });
    }
  },

  showToast: (toast: Omit<ToastNotification, 'id'>) => {
    const id = crypto.randomUUID();
    const newToast: ToastNotification = {
      ...toast,
      id,
      duration: toast.duration ?? 4000,
    };

    set((state) => ({
      toasts: [...state.toasts, newToast],
    }));

    // Auto-hide toast after duration (if duration > 0)
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        get().hideToast(id);
      }, newToast.duration);
    }
  },

  hideToast: (id: string) => {
    set((state) => ({
      toasts: state.toasts.filter(toast => toast.id !== id),
    }));
  },

  clearError: () => {
    set({ error: null });
  },
}));
