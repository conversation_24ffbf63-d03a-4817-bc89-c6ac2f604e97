using MediatR;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Interfaces;
using System.Threading;
using System.Threading.Tasks;

namespace ServiceLink.Application.Handlers;

public class ChangePasswordCommandHandler : IRequestHandler<ChangePasswordCommand, bool>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordService _passwordService;

    public ChangePasswordCommandHandler(IUserRepository userRepository, IPasswordService passwordService)
    {
        _userRepository = userRepository;
        _passwordService = passwordService;
    }

    public async Task<bool> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
    {
        // 1. Récupérer l'utilisateur
        var user = await _userRepository.GetByIdAsync(request.UserId);
        if (user == null || !user.IsActive)
            return false;

        // 2. Vérifier l'ancien mot de passe
        var passwordValid = _passwordService.VerifyPassword(request.CurrentPassword, user.PasswordHash, user.PasswordSalt);
        if (!passwordValid)
            return false;

        // 3. Générer le nouveau hash et salt
        var (newHash, newSalt) = _passwordService.HashPassword(request.NewPassword);

        // Use the SetPassword method to update the password hash and salt
        user.SetPassword(newHash, newSalt);

        // 4. Sauvegarder
        await _userRepository.UpdateAsync(user);
        return true;
    }
}
