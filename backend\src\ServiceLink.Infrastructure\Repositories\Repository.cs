using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Infrastructure.Data;
using System.Linq.Expressions;

namespace ServiceLink.Infrastructure.Repositories;

/// <summary>
/// Implémentation générique du repository
/// </summary>
/// <typeparam name="T">Type d'entité héritant de BaseEntity</typeparam>
public class Repository<T> : IRepository<T> where T : BaseEntity
{
    /// <summary>
    /// Contexte de base de données
    /// </summary>
    protected readonly ServiceLinkDbContext Context;

    /// <summary>
    /// DbSet de l'entité
    /// </summary>
    protected readonly DbSet<T> DbSet;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="context">Contexte de base de données</param>
    public Repository(ServiceLinkDbContext context)
    {
        Context = context ?? throw new ArgumentNullException(nameof(context));
        DbSet = context.Set<T>();
    }

    /// <summary>
    /// Obtient une entité par son identifiant
    /// </summary>
    /// <param name="id">Identifiant de l'entité</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'entité si trouvée, null sinon</returns>
    public virtual async Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await DbSet.FindAsync(new object[] { id }, cancellationToken);
    }

    /// <summary>
    /// Obtient toutes les entités
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste de toutes les entités</returns>
    public virtual async Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await DbSet.ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Trouve des entités selon un prédicat
    /// </summary>
    /// <param name="predicate">Condition de recherche</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des entités correspondantes</returns>
    public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(predicate).ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Trouve la première entité correspondant au prédicat
    /// </summary>
    /// <param name="predicate">Condition de recherche</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>La première entité trouvée ou null</returns>
    public virtual async Task<T?> FindFirstAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await DbSet.FirstOrDefaultAsync(predicate, cancellationToken);
    }

    /// <summary>
    /// Vérifie si une entité existe selon un prédicat
    /// </summary>
    /// <param name="predicate">Condition de recherche</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'entité existe</returns>
    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await DbSet.AnyAsync(predicate, cancellationToken);
    }

    /// <summary>
    /// Compte le nombre d'entités selon un prédicat
    /// </summary>
    /// <param name="predicate">Condition de recherche (optionnelle)</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nombre d'entités</returns>
    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        if (predicate == null)
        {
            return await DbSet.CountAsync(cancellationToken);
        }

        return await DbSet.CountAsync(predicate, cancellationToken);
    }

    /// <summary>
    /// Obtient des entités avec pagination
    /// </summary>
    /// <param name="pageNumber">Numéro de page (commence à 1)</param>
    /// <param name="pageSize">Taille de la page</param>
    /// <param name="predicate">Condition de recherche (optionnelle)</param>
    /// <param name="orderBy">Fonction de tri (optionnelle)</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat paginé</returns>
    public virtual async Task<PagedResult<T>> GetPagedAsync(
        int pageNumber,
        int pageSize,
        Expression<Func<T, bool>>? predicate = null,
        Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
        CancellationToken cancellationToken = default)
    {
        if (pageNumber < 1)
            throw new ArgumentException("Le numéro de page doit être supérieur à 0.", nameof(pageNumber));

        if (pageSize < 1)
            throw new ArgumentException("La taille de page doit être supérieure à 0.", nameof(pageSize));

        var query = DbSet.AsQueryable();

        // Application du filtre
        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        // Compte total
        var totalCount = await query.CountAsync(cancellationToken);

        // Application du tri
        if (orderBy != null)
        {
            query = orderBy(query);
        }
        else
        {
            // Tri par défaut par date de création
            query = query.OrderBy(e => e.CreatedAt);
        }

        // Pagination
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<T>(items, totalCount, pageNumber, pageSize);
    }

    /// <summary>
    /// Ajoute une nouvelle entité
    /// </summary>
    /// <param name="entity">Entité à ajouter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'entité ajoutée</returns>
    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        var entry = await DbSet.AddAsync(entity, cancellationToken);
        return entry.Entity;
    }

    /// <summary>
    /// Ajoute plusieurs entités
    /// </summary>
    /// <param name="entities">Entités à ajouter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public virtual async Task AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities));

        await DbSet.AddRangeAsync(entities, cancellationToken);
    }

    /// <summary>
    /// Met à jour une entité
    /// </summary>
    /// <param name="entity">Entité à mettre à jour</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'entité mise à jour</returns>
    public virtual async Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        var entry = DbSet.Update(entity);
        await Context.SaveChangesAsync(cancellationToken);
        return entry.Entity;
    }

    /// <summary>
    /// Supprime une entité (soft delete)
    /// </summary>
    /// <param name="entity">Entité à supprimer</param>
    /// <param name="deletedBy">Utilisateur qui supprime</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public virtual Task DeleteAsync(T entity, Guid? deletedBy = null, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        entity.SoftDelete(deletedBy);
        return Task.CompletedTask;
    }

    /// <summary>
    /// Supprime une entité par son identifiant (soft delete)
    /// </summary>
    /// <param name="id">Identifiant de l'entité</param>
    /// <param name="deletedBy">Utilisateur qui supprime</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public virtual async Task DeleteByIdAsync(Guid id, Guid? deletedBy = null, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            await DeleteAsync(entity, deletedBy, cancellationToken);
        }
    }

    /// <summary>
    /// Supprime définitivement une entité (hard delete)
    /// </summary>
    /// <param name="entity">Entité à supprimer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public virtual Task HardDeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        DbSet.Remove(entity);
        return Task.CompletedTask;
    }

    /// <summary>
    /// Restaure une entité supprimée
    /// </summary>
    /// <param name="entity">Entité à restaurer</param>
    /// <param name="restoredBy">Utilisateur qui restaure</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public virtual Task RestoreAsync(T entity, Guid? restoredBy = null, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        entity.Restore(restoredBy);
        return Task.CompletedTask;
    }
}
