import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { MessageSquare, Headphones, BarChart3, Clock, CheckCircle, AlertCircle, Users, Phone } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useAuthStore } from '@/stores/authStore';

export const DashboardSupport: React.FC = () => {
  const { user } = useAuthStore();

  // Mock support stats (à remplacer par de vraies données)
  const supportStats = {
    activeChats: 12,
    pendingTickets: 8,
    resolvedToday: 24,
    averageResponseTime: 2.3, // en minutes
    customerSatisfaction: 94,
    totalTicketsThisWeek: 156,
    escalatedTickets: 3,
    onlineAgents: 6
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-teal-50 to-cyan-50 -mx-6 px-6 py-8 rounded-lg">
        <h1 className="text-3xl font-bold mb-2 text-teal-800">
          Support Client - {user?.firstName}
        </h1>
        <p className="text-teal-700 mb-6">
          Gérez le chat en temps réel, les tickets et assurez un support client de qualité
        </p>
        
        {/* Support Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <MessageSquare className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-800">{supportStats.activeChats}</div>
            <p className="text-sm text-blue-600">Chats actifs</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-800">{supportStats.pendingTickets}</div>
            <p className="text-sm text-orange-600">Tickets en attente</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-800">{supportStats.resolvedToday}</div>
            <p className="text-sm text-green-600">Résolus aujourd'hui</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-800">{supportStats.customerSatisfaction}%</div>
            <p className="text-sm text-purple-600">Satisfaction client</p>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Temps de Réponse</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">{supportStats.averageResponseTime}min</div>
            <p className="text-xs text-blue-600">
              Objectif: &lt; 5min
            </p>
          </CardContent>
        </Card>

        <Card className="border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Tickets Résolus</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800">{supportStats.totalTicketsThisWeek}</div>
            <p className="text-xs text-green-600">
              Cette semaine
            </p>
          </CardContent>
        </Card>

        <Card className="border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700">Tickets Escaladés</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-800">{supportStats.escalatedTickets}</div>
            <p className="text-xs text-orange-600">
              Nécessitent attention
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700">Agents En Ligne</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800">{supportStats.onlineAgents}</div>
            <p className="text-xs text-purple-600">
              Disponibles maintenant
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Headphones className="h-5 w-5" />
            Actions de support
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/support/chat" className="block">
              <Button className="h-auto p-6 flex-col space-y-3 w-full bg-blue-600 hover:bg-blue-700">
                <MessageSquare className="h-8 w-8" />
                <span className="text-lg">Chat en Direct</span>
                <span className="text-sm opacity-90">
                  {supportStats.activeChats} conversations actives
                </span>
              </Button>
            </Link>
            
            <Link to="/support/tickets" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-orange-200 hover:bg-orange-50">
                <Clock className="h-8 w-8 text-orange-600" />
                <span className="text-lg">Tickets</span>
                <span className="text-sm text-muted-foreground">
                  {supportStats.pendingTickets} en attente
                </span>
              </Button>
            </Link>
            
            <Link to="/support/knowledge-base" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-green-200 hover:bg-green-50">
                <BarChart3 className="h-8 w-8 text-green-600" />
                <span className="text-lg">Base de Connaissances</span>
                <span className="text-sm text-muted-foreground">FAQ et guides</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Live Support Activity */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              Activité en Temps Réel
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm">Chat avec Marie D. - Problème paiement</span>
                </div>
                <Badge variant="outline">2min</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-sm">Ticket #1234 - Remboursement</span>
                </div>
                <Badge variant="secondary">En attente</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Ticket #1230 résolu</span>
                </div>
                <Badge className="bg-green-100 text-green-800">Terminé</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-teal-600" />
              Performance Support
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Temps de réponse</span>
                <span>{supportStats.averageResponseTime}min / 5min</span>
              </div>
              <Progress value={(5 - supportStats.averageResponseTime) / 5 * 100} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Satisfaction client</span>
                <span>{supportStats.customerSatisfaction}%</span>
              </div>
              <Progress value={supportStats.customerSatisfaction} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Tickets résolus aujourd'hui</span>
                <span>{supportStats.resolvedToday}/30</span>
              </div>
              <Progress value={(supportStats.resolvedToday / 30) * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Support Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-teal-600" />
            Statistiques Globales
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{supportStats.totalTicketsThisWeek}</div>
              <p className="text-sm text-muted-foreground">Tickets cette semaine</p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {((supportStats.totalTicketsThisWeek - supportStats.pendingTickets) / supportStats.totalTicketsThisWeek * 100).toFixed(1)}%
              </div>
              <p className="text-sm text-muted-foreground">Taux de résolution</p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">{supportStats.averageResponseTime}min</div>
              <p className="text-sm text-muted-foreground">Temps de réponse moyen</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Support Tips */}
      <Card className="bg-gradient-to-r from-teal-50 to-cyan-50">
        <CardHeader>
          <CardTitle className="text-teal-800">💬 Conseils pour un support efficace</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <Clock className="h-5 w-5 text-blue-600 mt-1" />
              <div>
                <p className="font-medium text-blue-800">Répondez rapidement</p>
                <p className="text-sm text-blue-600">Objectif: moins de 5 minutes</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <MessageSquare className="h-5 w-5 text-teal-600 mt-1" />
              <div>
                <p className="font-medium text-teal-800">Soyez empathique</p>
                <p className="text-sm text-teal-600">Écoutez et comprenez le problème</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
