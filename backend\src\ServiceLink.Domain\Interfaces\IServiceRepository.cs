using ServiceLink.Domain.Entities;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Domain.Interfaces;

/// <summary>
/// Interface pour le repository des services
/// </summary>
public interface IServiceRepository : IRepository<Service>
{
    /// <summary>
    /// Obtient les services d'un prestataire
    /// </summary>
    Task<IEnumerable<Service>> GetByProviderIdAsync(Guid providerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les services d'une catégorie
    /// </summary>
    Task<IEnumerable<Service>> GetByCategoryIdAsync(Guid categoryId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Recherche des services par critères
    /// </summary>
    Task<IEnumerable<Service>> SearchAsync(string? searchTerm, Guid? categoryId, ServiceAddress? location, double? radiusKm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie la disponibilité d'un service
    /// </summary>
    Task<bool> IsAvailableAsync(Guid serviceId, DateTime dateTime, int durationMinutes, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient l'adresse du prestataire
    /// </summary>
    Task<ServiceAddress?> GetProviderAddressAsync(Guid providerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les créneaux disponibles
    /// </summary>
    Task<IEnumerable<DateTime>> GetAvailableSlotsAsync(Guid serviceId, DateTime startDate, DateTime endDate, int durationMinutes, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les services avec pagination
    /// </summary>
    Task<(IEnumerable<Service> Items, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        string? searchTerm = null,
        Guid? categoryId = null,
        Guid? providerId = null,
        bool? isActive = null,
        ServiceAddress? location = null,
        double? radiusKm = null,
        string sortBy = "Name",
        string sortDirection = "asc",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les services populaires
    /// </summary>
    Task<IEnumerable<Service>> GetPopularServicesAsync(int limit = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les services recommandés pour un utilisateur
    /// </summary>
    Task<IEnumerable<Service>> GetRecommendedServicesAsync(Guid userId, int limit = 10, CancellationToken cancellationToken = default);
}
