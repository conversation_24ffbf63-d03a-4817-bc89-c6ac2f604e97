using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Infrastructure.Data;

namespace ServiceLink.Infrastructure.Repositories;

/// <summary>
/// Implémentation du repository pour les réservations
/// </summary>
public class BookingRepository : Repository<Booking>, IBookingRepository
{
    public BookingRepository(ServiceLinkDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient les réservations d'un client
    /// </summary>
    public async Task<IEnumerable<Booking>> GetByClientIdAsync(Guid clientId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Booking>()
            .Where(b => b.ClientId == clientId)
            .Include(b => b.Client)
            .Include(b => b.Provider)
            .Include(b => b.Service)
                .ThenInclude(s => s!.Category)
            .Include(b => b.Review)
            .OrderByDescending(b => b.ScheduledDate)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les réservations d'un prestataire
    /// </summary>
    public async Task<IEnumerable<Booking>> GetByProviderIdAsync(Guid providerId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Booking>()
            .Where(b => b.ProviderId == providerId)
            .Include(b => b.Client)
            .Include(b => b.Provider)
            .Include(b => b.Service)
                .ThenInclude(s => s!.Category)
            .Include(b => b.Review)
            .OrderByDescending(b => b.ScheduledDate)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les réservations d'un service
    /// </summary>
    public async Task<IEnumerable<Booking>> GetByServiceIdAsync(Guid serviceId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Booking>()
            .Where(b => b.ServiceId == serviceId)
            .Include(b => b.Client)
            .Include(b => b.Provider)
            .Include(b => b.Service)
            .Include(b => b.Review)
            .OrderByDescending(b => b.ScheduledDate)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les réservations par statut
    /// </summary>
    public async Task<IEnumerable<Booking>> GetByStatusAsync(BookingStatus status, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Booking>()
            .Where(b => b.Status == status)
            .Include(b => b.Client)
            .Include(b => b.Provider)
            .Include(b => b.Service)
            .OrderByDescending(b => b.ScheduledDate)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les réservations expirées
    /// </summary>
    public async Task<IEnumerable<Booking>> GetExpiredBookingsAsync(int limit = 100, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Booking>()
            .Where(b => b.Status == BookingStatus.Pending && b.ExpiresAt < DateTime.UtcNow)
            .OrderBy(b => b.ExpiresAt)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les réservations à venir pour un utilisateur
    /// </summary>
    public async Task<IEnumerable<Booking>> GetUpcomingBookingsAsync(Guid userId, int hoursAhead = 24, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow.AddHours(hoursAhead);
        
        return await Context.Set<Booking>()
            .Where(b => (b.ClientId == userId || b.ProviderId == userId) &&
                       b.ScheduledDate <= cutoffTime &&
                       b.ScheduledDate > DateTime.UtcNow &&
                       (b.Status == BookingStatus.Confirmed || b.Status == BookingStatus.InProgress))
            .Include(b => b.Client)
            .Include(b => b.Provider)
            .Include(b => b.Service)
            .OrderBy(b => b.ScheduledDate)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Vérifie s'il y a des conflits de réservation
    /// </summary>
    public async Task<bool> HasConflictAsync(Guid serviceId, DateTime startDate, DateTime endDate, Guid? excludeBookingId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Booking>()
            .Where(b => b.ServiceId == serviceId &&
                       b.Status != BookingStatus.Cancelled &&
                       b.Status != BookingStatus.Rejected &&
                       b.Status != BookingStatus.Expired &&
                       ((b.ScheduledDate < endDate && b.ScheduledEndDate > startDate)));

        if (excludeBookingId.HasValue)
        {
            query = query.Where(b => b.Id != excludeBookingId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les réservations avec pagination
    /// </summary>
    public async Task<(IEnumerable<Booking> Items, int TotalCount)> GetPagedAsync(
        int page, 
        int pageSize, 
        Guid? clientId = null, 
        Guid? providerId = null, 
        List<BookingStatus>? statusFilter = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        string sortBy = "ScheduledDate",
        string sortDirection = "desc",
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Booking>()
            .Include(b => b.Client)
            .Include(b => b.Provider)
            .Include(b => b.Service)
                .ThenInclude(s => s!.Category)
            .Include(b => b.Review)
            .AsQueryable();

        // Filtres
        if (clientId.HasValue)
            query = query.Where(b => b.ClientId == clientId.Value);

        if (providerId.HasValue)
            query = query.Where(b => b.ProviderId == providerId.Value);

        if (statusFilter != null && statusFilter.Any())
            query = query.Where(b => statusFilter.Contains(b.Status));

        if (startDate.HasValue)
            query = query.Where(b => b.ScheduledDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(b => b.ScheduledDate <= endDate.Value);

        // Tri
        query = sortBy.ToLowerInvariant() switch
        {
            "scheduleddate" => sortDirection.ToLowerInvariant() == "asc" 
                ? query.OrderBy(b => b.ScheduledDate)
                : query.OrderByDescending(b => b.ScheduledDate),
            "createdat" => sortDirection.ToLowerInvariant() == "asc"
                ? query.OrderBy(b => b.CreatedAt)
                : query.OrderByDescending(b => b.CreatedAt),
            "totalamount" => sortDirection.ToLowerInvariant() == "asc"
                ? query.OrderBy(b => b.TotalAmount)
                : query.OrderByDescending(b => b.TotalAmount),
            "status" => sortDirection.ToLowerInvariant() == "asc"
                ? query.OrderBy(b => b.Status)
                : query.OrderByDescending(b => b.Status),
            _ => query.OrderByDescending(b => b.ScheduledDate)
        };

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    /// <summary>
    /// Obtient les statistiques de réservation pour un utilisateur
    /// </summary>
    public async Task<BookingStats> GetStatsAsync(Guid userId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Booking>()
            .Where(b => b.ClientId == userId || b.ProviderId == userId);

        if (startDate.HasValue)
            query = query.Where(b => b.ScheduledDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(b => b.ScheduledDate <= endDate.Value);

        var bookings = await query.ToListAsync(cancellationToken);

        var totalBookings = bookings.Count;
        var completedBookings = bookings.Count(b => b.Status == BookingStatus.Completed);
        var cancelledBookings = bookings.Count(b => b.Status == BookingStatus.Cancelled);
        var pendingBookings = bookings.Count(b => b.Status == BookingStatus.Pending);

        var totalRevenue = bookings
            .Where(b => b.Status == BookingStatus.Completed)
            .Sum(b => b.TotalAmount);

        var reviews = await Context.Set<Review>()
            .Where(r => bookings.Select(b => b.Id).Contains(r.BookingId))
            .ToListAsync(cancellationToken);

        var averageRating = reviews.Any() ? (decimal)reviews.Average(r => r.Rating) : 0;
        var totalReviews = reviews.Count;

        var completionRate = totalBookings > 0 ? (decimal)completedBookings / totalBookings * 100 : 0;
        var cancellationRate = totalBookings > 0 ? (decimal)cancelledBookings / totalBookings * 100 : 0;

        return new BookingStats
        {
            TotalBookings = totalBookings,
            CompletedBookings = completedBookings,
            CancelledBookings = cancelledBookings,
            PendingBookings = pendingBookings,
            TotalRevenue = totalRevenue,
            AverageRating = averageRating,
            TotalReviews = totalReviews,
            CompletionRate = completionRate,
            CancellationRate = cancellationRate
        };
    }

    /// <summary>
    /// Obtient une réservation avec toutes ses relations
    /// </summary>
    public override async Task<Booking?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Booking>()
            .Include(b => b.Client)
            .Include(b => b.Provider)
            .Include(b => b.Service)
                .ThenInclude(s => s!.Category)
            .Include(b => b.Review)
            .FirstOrDefaultAsync(b => b.Id == id, cancellationToken);
    }
}
