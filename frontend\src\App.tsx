import React, { useEffect } from 'react';
import { Routes, Route, Navigate, BrowserRouter } from 'react-router-dom';
import { MainLayout } from './components/layout/MainLayout';
import { NotificationPanel } from './components/notifications/NotificationPanel';
import { ToastContainer } from './components/notifications/ToastContainer';
import { Home } from './pages/Home';
import { Dashboard } from './pages/Dashboard';
import { DashboardClient } from './pages/DashboardClient';
import { DashboardProvider } from './pages/DashboardProvider';
import { DashboardAdmin } from './pages/DashboardAdmin';
import { DashboardManager } from './pages/DashboardManager';
import { DashboardSupport } from './pages/DashboardSupport';
import { DashboardSupervisor } from './pages/DashboardSupervisor';
import { Services } from './pages/Services';
import { ServiceDetail } from './pages/ServiceDetail';
import { Bookings } from './pages/Bookings';
import { LoginForm } from './components/auth/LoginForm';
import { RegisterPage } from './pages/RegisterPage';
import { ResetPasswordPage } from './pages/ResetPasswordPage';
import { ProfilePage } from './pages/ProfilePage';
import { AdminPage } from './pages/AdminPage';
import { ApiTest } from './components/debug/ApiTest';
import { RoleBasedRedirect } from './components/auth/RoleBasedRedirect';
import { useUIStore, useNotificationStore } from './stores';
import { useAuthStore } from '@/stores/authStore';

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode; roles?: string[] }> = ({
  children,
  roles
}) => {
  const { isAuthenticated, user } = useAuthStore();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (roles && user && !roles.includes(user.role)) {
    // Tous les rôles vont vers le dashboard, mais avec un contenu différent
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

// Public Route Component (redirect if authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAuthStore();

  if (isAuthenticated && user) {
    // Redirection selon le rôle
    switch (user.role) {
      case 'Client':
        return <Navigate to="/dashboard/client" replace />;
      case 'Provider':
      case 'ServiceProvider':
      case 'Prestataire':
        return <Navigate to="/dashboard/provider" replace />;
      case 'Admin':
      case 'Admin Global':
        return <Navigate to="/dashboard/admin" replace />;
      case 'Manager':
        return <Navigate to="/dashboard/manager" replace />;
      case 'Support':
        return <Navigate to="/dashboard/support" replace />;
      case 'Supervisor':
      case 'Superviseur':
        return <Navigate to="/dashboard/supervisor" replace />;
      default:
        return <Navigate to="/dashboard/general" replace />;
    }
  }

  return <>{children}</>;
};

function App() {
  const { isAuthenticated, user, token, initializeAuth } = useAuthStore();
  const { isNotificationPanelOpen, setNotificationPanelOpen } = useUIStore();
  const { initializeSignalR, disconnectSignalR } = useNotificationStore();

  // Initialize auth from localStorage on app start
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  // Initialize SignalR when user is authenticated
  useEffect(() => {
    if (isAuthenticated && token && user) {
      initializeSignalR(token);
    } else {
      disconnectSignalR();
    }

    return () => {
      disconnectSignalR();
    };
  }, [isAuthenticated, token, user, initializeSignalR, disconnectSignalR]);

  return (
    <BrowserRouter>
      <div className="min-h-screen bg-background">
        <Routes>
          {/* Public Routes */}
          <Route
            path="/"
            element={
              isAuthenticated ? <RoleBasedRedirect /> : <Home />
            }
          />

          {/* Debug Route (temporaire) */}
          <Route path="/debug/api" element={<ApiTest />} />

          <Route
            path="/login"
            element={
              <PublicRoute>
                <div className="min-h-screen flex items-center justify-center py-12 px-4">
                  <LoginForm />
                </div>
              </PublicRoute>
            }
          />

          <Route
            path="/register"
            element={
              <PublicRoute>
                <RegisterPage />
              </PublicRoute>
            }
          />

          <Route
            path="/reset-password"
            element={
              <PublicRoute>
                <ResetPasswordPage />
              </PublicRoute>
            }
          />

          {/* Protected Routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<RoleBasedRedirect />} />
            <Route path="general" element={<Dashboard />} />
            <Route path="client" element={<DashboardClient />} />
            <Route path="provider" element={<DashboardProvider />} />
            <Route path="admin" element={<DashboardAdmin />} />
            <Route path="manager" element={<DashboardManager />} />
            <Route path="support" element={<DashboardSupport />} />
            <Route path="supervisor" element={<DashboardSupervisor />} />
          </Route>

          <Route
            path="/services"
            element={
              <ProtectedRoute>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Services />} />
            <Route path=":id" element={<ServiceDetail />} />
          </Route>

          <Route
            path="/bookings"
            element={
              <ProtectedRoute>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Bookings />} />
          </Route>

          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<ProfilePage />} />
          </Route>

          {/* Provider Routes */}
          <Route
            path="/provider/*"
            element={
              <ProtectedRoute roles={['ServiceProvider', 'Admin']}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            {/* Provider routes will be added later */}
          </Route>

          {/* Admin Routes */}
          <Route
            path="/admin/*"
            element={
              <ProtectedRoute roles={['Admin']}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<AdminPage />} />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>

        {/* Global Components */}
        <NotificationPanel
          isOpen={isNotificationPanelOpen}
          onClose={() => setNotificationPanelOpen(false)}
        />
        <ToastContainer />
      </div>
    </BrowserRouter>
  );
}

export default App;
