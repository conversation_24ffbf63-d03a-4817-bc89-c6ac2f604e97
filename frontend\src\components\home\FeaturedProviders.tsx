import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Star, MapPin, Clock, CheckCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useFeaturedServices, useServiceStats } from '@/hooks/useServices';

export const FeaturedProviders: React.FC = () => {
  const navigate = useNavigate();
  const { data: featuredServices, loading: servicesLoading } = useFeaturedServices(4);
  const { data: stats, loading: statsLoading } = useServiceStats();
  // Gestion de la navigation
  const handleViewAllProviders = () => {
    navigate('/services');
  };

  const handleContactProvider = (serviceId: string) => {
    navigate(`/services/${serviceId}`);
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            {statsLoading ? '...' : (stats?.totalProviders?.toLocaleString() || '322 000')} prestataires de service à domicile
          </h2>
          <p className="text-xl text-muted-foreground mb-2">
            évalués et qualifiés
          </p>

          {/* Operation Banner */}
          <div className="bg-green-100 border border-green-200 rounded-lg p-4 max-w-2xl mx-auto mt-8 mb-8">
            <div className="flex items-center justify-center space-x-3">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xl">🎯</span>
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-green-800">Opération de l'été</h3>
                <p className="text-green-700 text-sm">
                  Trouvez vos prestataires de confiance et ne payez plus que 8% de commission !
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Providers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {servicesLoading ? (
            // Skeleton loading
            Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-4"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            featuredServices?.map((service) => (
              <Card key={service.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-4">
                  {/* Avatar and Verification */}
                  <div className="relative mb-4">
                    <img
                      src={service.provider?.avatar || '/api/placeholder/80/80'}
                      alt={`${service.provider?.firstName} ${service.provider?.lastName}`}
                      className="w-20 h-20 rounded-full mx-auto object-cover"
                    />
                    {service.provider?.isVerified && (
                      <CheckCircle className="absolute -bottom-1 -right-1 w-6 h-6 text-blue-500 bg-white rounded-full" />
                    )}
                  </div>

                  {/* Provider Info */}
                  <div className="text-center mb-4">
                    <h3 className="font-semibold text-lg">
                      {service.provider?.firstName} {service.provider?.lastName}
                    </h3>
                    <p className="text-muted-foreground">{service.category?.name || service.name}</p>

                    <div className="flex items-center justify-center space-x-1 mt-2">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        {service.location.city}
                      </span>
                    </div>
                  </div>

                  {/* Rating and Reviews */}
                  <div className="flex items-center justify-center space-x-2 mb-3">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{service.rating.toFixed(1)}</span>
                    </div>
                    <span className="text-muted-foreground text-sm">
                      ({service.reviewCount} avis)
                    </span>
                  </div>

                  {/* Tags/Specialties */}
                  <div className="flex flex-wrap gap-1 mb-4 justify-center">
                    {service.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  {/* Price and Response Time */}
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Tarif</span>
                      <span className="font-medium">
                        {service.basePrice}€{service.pricingUnit === 'Hourly' ? '/h' : ''}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Réponse</span>
                      <span className="font-medium">
                        {service.provider?.responseTime || '< 2h'}
                      </span>
                    </div>
                  </div>

                  {/* Contact Button */}
                  <Button
                    className="w-full mt-4"
                    size="sm"
                    onClick={() => handleContactProvider(service.id)}
                  >
                    Contacter
                  </Button>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Button
            variant="outline"
            size="lg"
            onClick={handleViewAllProviders}
          >
            Voir tous les prestataires
          </Button>
        </div>
      </div>
    </section>
  );
};
