import React from 'react';
import { <PERSON>, MapPin, Clock, CheckCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';

interface Provider {
  id: string;
  name: string;
  service: string;
  location: string;
  rating: number;
  reviewCount: number;
  price: string;
  responseTime: string;
  verified: boolean;
  avatar: string;
  specialties: string[];
}

const featuredProviders: Provider[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    service: 'Ménage',
    location: 'Paris 15e',
    rating: 4.9,
    reviewCount: 127,
    price: '15 €/h',
    responseTime: '< 2h',
    verified: true,
    avatar: '/api/placeholder/80/80',
    specialties: ['Ménage régulier', 'Repassage', 'Vitres']
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    service: 'Bricolage',
    location: 'Lyon 3e',
    rating: 4.8,
    reviewCount: 89,
    price: '25 €/h',
    responseTime: '< 1h',
    verified: true,
    avatar: '/api/placeholder/80/80',
    specialties: ['Montage meuble', 'Petites réparations', 'Électricité']
  },
  {
    id: '3',
    name: 'Sophie',
    service: 'Garde d\'enfants',
    location: 'Marseille 8e',
    rating: 5.0,
    reviewCount: 156,
    price: '12 €/h',
    responseTime: '< 30min',
    verified: true,
    avatar: '/api/placeholder/80/80',
    specialties: ['Garde régulière', 'Aide aux devoirs', 'Activités créatives']
  },
  {
    id: '4',
    name: 'Thomas',
    service: 'Jardinage',
    location: 'Toulouse',
    rating: 4.7,
    reviewCount: 203,
    price: '20 €/h',
    responseTime: '< 3h',
    verified: true,
    avatar: '/api/placeholder/80/80',
    specialties: ['Tonte pelouse', 'Taille haies', 'Plantation']
  }
];

export const FeaturedProviders: React.FC = () => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            322 000 prestataires de service à domicile
          </h2>
          <p className="text-xl text-muted-foreground mb-2">
            évalués et qualifiés
          </p>
          
          {/* Operation Banner */}
          <div className="bg-green-100 border border-green-200 rounded-lg p-4 max-w-2xl mx-auto mt-8 mb-8">
            <div className="flex items-center justify-center space-x-3">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xl">🎯</span>
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-green-800">Opération de l'été</h3>
                <p className="text-green-700 text-sm">
                  Trouvez vos prestataires de confiance et ne payez plus que 8% de commission !
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Providers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {featuredProviders.map((provider) => (
            <Card key={provider.id} className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-4">
                {/* Avatar and Verification */}
                <div className="relative mb-4">
                  <img
                    src={provider.avatar}
                    alt={provider.name}
                    className="w-20 h-20 rounded-full mx-auto object-cover"
                  />
                  {provider.verified && (
                    <CheckCircle className="absolute -bottom-1 -right-1 w-6 h-6 text-blue-500 bg-white rounded-full" />
                  )}
                </div>

                {/* Provider Info */}
                <div className="text-center mb-4">
                  <h3 className="font-semibold text-lg">{provider.name}</h3>
                  <p className="text-muted-foreground">{provider.service}</p>
                  
                  <div className="flex items-center justify-center space-x-1 mt-2">
                    <MapPin className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">{provider.location}</span>
                  </div>
                </div>

                {/* Rating and Reviews */}
                <div className="flex items-center justify-center space-x-2 mb-3">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{provider.rating}</span>
                  </div>
                  <span className="text-muted-foreground text-sm">
                    ({provider.reviewCount} avis)
                  </span>
                </div>

                {/* Specialties */}
                <div className="flex flex-wrap gap-1 mb-4 justify-center">
                  {provider.specialties.slice(0, 2).map((specialty) => (
                    <Badge key={specialty} variant="secondary" className="text-xs">
                      {specialty}
                    </Badge>
                  ))}
                </div>

                {/* Price and Response Time */}
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Tarif</span>
                    <span className="font-medium">{provider.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Réponse</span>
                    <span className="font-medium">{provider.responseTime}</span>
                  </div>
                </div>

                {/* Contact Button */}
                <Button className="w-full mt-4" size="sm">
                  Contacter
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Button variant="outline" size="lg">
            Voir tous les prestataires
          </Button>
        </div>
      </div>
    </section>
  );
};
