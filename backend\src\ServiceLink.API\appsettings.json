{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=servicelink;Username=servicelink_user;Password=*********************;Port=5437"}, "JWT": {"SecretKey": "ServiceLink-Super-Secret-Key-For-Development-Only-Change-In-Production-2024", "Issuer": "ServiceLink", "Audience": "ServiceLink-Users", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5}, "Email": {"SmtpServer": "localhost", "SmtpPort": 587, "EnableSsl": true, "Username": "", "Password": "", "FromEmail": "<EMAIL>", "FromName": "ServiceLink", "BaseUrl": "https://localhost:5001", "AllowedDomains": [], "BlockedDomains": ["tempmail.org", "10minutemail.com", "guerrillamail.com"], "DevelopmentMode": true}, "CORS": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:3000", "https://localhost:5173"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true, "MaxAge": 86400}, "RateLimit": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 100}, {"Endpoint": "*", "Period": "1h", "Limit": 1000}], "ClientRules": [], "IpRules": []}, "Database": {"PoolSize": 128, "CommandTimeout": 30, "EnableRetryOnFailure": true, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30"}, "Development": {"EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "SeedData": true}, "Security": {"RequireHttps": false, "RequireEmailConfirmation": true, "RequirePhoneConfirmation": false, "PasswordResetTokenExpirationHours": 1, "EmailConfirmationTokenExpirationHours": 24, "MaxFailedLoginAttempts": 5, "LockoutDurationMinutes": 30, "TwoFactorRequired": false}, "Swagger": {"Title": "ServiceLink API", "Description": "API complète pour la gestion des utilisateurs et services", "Version": "v1", "ContactName": "ServiceLink Team", "ContactEmail": "<EMAIL>", "LicenseName": "MIT", "EnableXmlComments": true, "EnableJwtAuthentication": true}}