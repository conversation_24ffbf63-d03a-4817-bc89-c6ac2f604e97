{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=servicelink;Username=servicelink_user;Password=*********************;Port=5437"}, "JWT": {"SecretKey": "ServiceLink-Super-Secret-Key-For-Development-Only-Change-In-Production-2024", "Issuer": "ServiceLink", "Audience": "ServiceLink-Users", "ExpirationMinutes": 1440, "RefreshTokenExpirationDays": 30, "ClockSkewMinutes": 5}, "Email": {"SmtpServer": "localhost", "SmtpPort": 587, "EnableSsl": true, "Username": "", "Password": "", "FromEmail": "<EMAIL>", "FromName": "ServiceLink", "BaseUrl": "https://localhost:5001", "AllowedDomains": [], "BlockedDomains": ["tempmail.org", "10minutemail.com", "guerrillamail.com"], "DevelopmentMode": true}, "CORS": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:3000", "https://localhost:5173"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true, "MaxAge": 86400}, "RateLimit": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 100}, {"Endpoint": "*", "Period": "1h", "Limit": 1000}], "ClientRules": [], "IpRules": []}, "Database": {"PoolSize": 128, "CommandTimeout": 30, "EnableRetryOnFailure": true, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30"}, "Development": {"EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "SeedData": true}, "Security": {"RequireHttps": false, "RequireEmailConfirmation": true, "RequirePhoneConfirmation": false, "PasswordResetTokenExpirationHours": 1, "EmailConfirmationTokenExpirationHours": 24, "MaxFailedLoginAttempts": 5, "LockoutDurationMinutes": 30, "TwoFactorRequired": false}, "Swagger": {"Title": "ServiceLink API", "Description": "API complète pour la gestion des utilisateurs et services", "Version": "v1", "ContactName": "ServiceLink Team", "ContactEmail": "<EMAIL>", "LicenseName": "MIT", "EnableXmlComments": true, "EnableJwtAuthentication": true}, "Redis": {"ConnectionString": "localhost:6379", "InstanceName": "ServiceLink", "Password": "", "UseSsl": false, "SslHost": "", "AbortOnConnectFail": false, "ConnectTimeoutMs": 5000, "SyncTimeoutMs": 5000, "AsyncTimeoutMs": 5000, "ConnectRetry": 3, "KeepAliveSeconds": 60, "Database": 0, "EnableDevelopmentMode": true}, "SignalR": {"EnableDetailedErrors": true, "KeepAliveIntervalSeconds": 15, "ClientTimeoutIntervalSeconds": 30, "HandshakeTimeoutSeconds": 15, "MaximumReceiveMessageSize": 32768, "StreamBufferCapacity": 10, "MaximumParallelInvocationsPerClient": 1, "UseRedisBackplane": false, "RedisConnectionString": "localhost:6379", "RedisChannelPrefix": "ServiceLink.SignalR", "EnableMessageCompression": true, "EnableDetailedLogging": true}, "Stripe": {"PublishableKey": "pk_test_...", "SecretKey": "sk_test_...", "WebhookSecret": "whsec_...", "ReturnUrl": "http://localhost:3000/payment/return", "TestMode": true}, "PayPal": {"ClientId": "your_paypal_client_id", "ClientSecret": "your_paypal_client_secret", "Environment": "sandbox", "ReturnUrl": "http://localhost:3000/payment/paypal/return", "CancelUrl": "http://localhost:3000/payment/paypal/cancel"}, "Flutterwave": {"PublicKey": "FLWPUBK_TEST-...", "SecretKey": "FLWSECK_TEST-...", "EncryptionKey": "FLWSECK_TEST...", "Environment": "sandbox", "WebhookHash": "your_webhook_hash"}, "Payment": {"DefaultProvider": "Stripe", "EnabledProviders": ["Stripe", "PayPal", "Flutterwave"], "DefaultCurrency": "EUR", "SupportedCurrencies": ["EUR", "USD", "XOF", "NGN", "GHS"], "WebhookTimeout": 30, "MaxRefundDays": 30}, "Commission": {"DefaultCommissionRate": 5.0, "StripeCommissionRate": 4.5, "PayPalCommissionRate": 5.0, "FlutterwaveCommissionRate": 3.5, "MinimumCommission": 50, "MaximumCommission": 50000, "EnableTierDiscounts": true, "PayoutFrequencyDays": 7, "MinimumPayoutAmount": 2000}, "ExternalNotifications": {"EnabledServices": ["Email", "SMS", "<PERSON><PERSON>"], "DefaultEmailService": "SendGrid", "DefaultSmsService": "<PERSON><PERSON><PERSON>", "DefaultPushService": "Firebase", "MaxRetryAttempts": 3, "RetryDelaySeconds": 30, "EnableTemplates": true, "EnableBulkSending": true, "MaxBulkSize": 100, "EnableFallback": true, "FallbackConfiguration": {"Email": ["SMS"], "SMS": ["<PERSON><PERSON>"], "Push": ["Email"]}}, "SendGrid": {"ApiKey": "YOUR_SENDGRID_API_KEY", "FromEmail": "<EMAIL>", "FromName": "ServiceLink", "ReplyToEmail": "<EMAIL>", "EnableOpenTracking": true, "EnableClickTracking": true, "EnableWebhooks": true, "WebhookUrl": "https://your-domain.com/api/webhooks/notifications/sendgrid", "DefaultTemplateId": "", "RateLimitPerMinute": 100}, "Twilio": {"AccountSid": "YOUR_TWILIO_ACCOUNT_SID", "AuthToken": "YOUR_TWILIO_AUTH_TOKEN", "FromPhoneNumber": "+***********", "MaxSmsLength": 160, "DelayBetweenSms": 100, "DelayBetweenBulk": 1000, "EnableStatusWebhooks": true, "StatusWebhookUrl": "https://your-domain.com/api/webhooks/notifications/twilio", "RateLimitPerMinute": 60, "EnableNumberValidation": true, "DefaultCountryCode": "+33"}, "Firebase": {"ProjectId": "your-firebase-project-id", "ServiceAccountKeyPath": "path/to/firebase-service-account.json", "ServiceAccountKeyJson": "", "DefaultIcon": "ic_notification", "DefaultColor": "#FF6B35", "EnableAnalytics": true, "RateLimitPerMinute": 600, "DefaultTtl": 3600, "EnableCompression": true}}