// Script pour créer les comptes administratifs manquants
// Manager, Support, Superviseur

const API_BASE = 'https://localhost:7276/api/auth/register';

const accounts = [
  {
    firstName: '<PERSON>',
    lastName: 'Manager',
    email: '<EMAIL>',
    password: 'ManagerPass123!',
    confirmPassword: 'ManagerPass123!',
    role: 12, // UserRole.Manager = 12
    acceptTerms: true,
    language: 'fr-FR',
    timeZone: 'Europe/Paris',
  },
  {
    firstName: 'Marie',
    lastName: 'Support',
    email: '<EMAIL>',
    password: 'SupportPass123!',
    confirmPassword: 'SupportPass123!',
    role: 11, // UserRole.Support = 11
    acceptTerms: true,
    language: 'fr-FR',
    timeZone: 'Europe/Paris',
  },
  {
    firstName: 'Paul',
    lastName: 'Superviseur',
    email: '<EMAIL>',
    password: 'SupervisorPass123!',
    confirmPassword: 'SupervisorPass123!',
    role: 13, // UserRole.Supervisor = 13
    acceptTerms: true,
    language: 'fr-FR',
    timeZone: 'Europe/Paris',
  }
];

async function createAccount(accountData) {
  try {
    console.log(`Création du compte ${accountData.email}...`);
    
    const response = await fetch(API_BASE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(accountData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Erreur ${response.status}: ${errorData.message || 'Inscription échouée'}`);
    }

    const result = await response.json();
    console.log(`✅ Compte ${accountData.email} créé avec succès!`);
    console.log(`   ID: ${result.id}`);
    console.log(`   Rôle: ${accountData.role}`);
    return result;
    
  } catch (error) {
    console.error(`❌ Erreur lors de la création de ${accountData.email}:`, error.message);
    return null;
  }
}

async function createAllAccounts() {
  console.log('🚀 Création des comptes administratifs...\n');
  
  for (const account of accounts) {
    await createAccount(account);
    console.log(''); // Ligne vide pour la lisibilité
  }
  
  console.log('✨ Processus terminé!');
}

// Exécution si le script est appelé directement
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  createAllAccounts();
} else {
  // Browser environment
  window.createAllAccounts = createAllAccounts;
}
