import type { 
  Booking, 
  CreateBookingRequest,
  UpdateBookingRequest,
  BookingFilters,
  BookingStats
} from '../types/booking'
import type { ServiceAvailability, ServiceBookingSlot } from '../types/service'
import type { PaginatedResponse, PaginationParams } from '../types/api'
import { get, post, put, del } from '../lib/api'

export const bookingService = {
  // Créer une nouvelle réservation
  async createBooking(data: CreateBookingRequest): Promise<Booking> {
    return await post<Booking>('/bookings', data)
  },

  // Obtenir les réservations de l'utilisateur
  async getUserBookings(
    filters: BookingFilters = {},
    pagination: PaginationParams = {}
  ): Promise<PaginatedResponse<Booking>> {
    const params = new URLSearchParams()
    
    // Ajouter les filtres
    if (filters.status) params.append('status', filters.status)
    if (filters.serviceId) params.append('serviceId', filters.serviceId)
    if (filters.providerId) params.append('providerId', filters.providerId)
    if (filters.startDate) params.append('startDate', filters.startDate)
    if (filters.endDate) params.append('endDate', filters.endDate)
    if (filters.minAmount) params.append('minAmount', filters.minAmount.toString())
    if (filters.maxAmount) params.append('maxAmount', filters.maxAmount.toString())
    
    // Ajouter la pagination
    if (pagination.pageNumber) params.append('pageNumber', pagination.pageNumber.toString())
    if (pagination.pageSize) params.append('pageSize', pagination.pageSize.toString())
    if (pagination.sortBy) params.append('sortBy', pagination.sortBy)
    if (pagination.sortDirection) params.append('sortDirection', pagination.sortDirection)

    return await get<PaginatedResponse<Booking>>(`/bookings?${params.toString()}`)
  },

  // Obtenir les réservations du prestataire
  async getProviderBookings(
    filters: BookingFilters = {},
    pagination: PaginationParams = {}
  ): Promise<PaginatedResponse<Booking>> {
    const params = new URLSearchParams()
    
    // Ajouter les filtres
    if (filters.status) params.append('status', filters.status)
    if (filters.serviceId) params.append('serviceId', filters.serviceId)
    if (filters.startDate) params.append('startDate', filters.startDate)
    if (filters.endDate) params.append('endDate', filters.endDate)
    
    // Ajouter la pagination
    if (pagination.pageNumber) params.append('pageNumber', pagination.pageNumber.toString())
    if (pagination.pageSize) params.append('pageSize', pagination.pageSize.toString())
    if (pagination.sortBy) params.append('sortBy', pagination.sortBy)
    if (pagination.sortDirection) params.append('sortDirection', pagination.sortDirection)

    return await get<PaginatedResponse<Booking>>(`/bookings/provider?${params.toString()}`)
  },

  // Obtenir une réservation par ID
  async getBookingById(id: string): Promise<Booking> {
    return await get<Booking>(`/bookings/${id}`)
  },

  // Mettre à jour une réservation
  async updateBooking(id: string, data: UpdateBookingRequest): Promise<Booking> {
    return await put<Booking>(`/bookings/${id}`, data)
  },

  // Confirmer une réservation (prestataire)
  async confirmBooking(id: string): Promise<Booking> {
    return await post<Booking>(`/bookings/${id}/confirm`)
  },

  // Refuser une réservation (prestataire)
  async rejectBooking(id: string, reason?: string): Promise<Booking> {
    return await post<Booking>(`/bookings/${id}/reject`, { reason })
  },

  // Annuler une réservation
  async cancelBooking(id: string, reason?: string): Promise<Booking> {
    return await post<Booking>(`/bookings/${id}/cancel`, { reason })
  },

  // Marquer une réservation comme terminée
  async completeBooking(id: string): Promise<Booking> {
    return await post<Booking>(`/bookings/${id}/complete`)
  },

  // Obtenir les créneaux disponibles pour un service
  async getAvailableSlots(
    serviceId: string,
    startDate: string,
    endDate: string
  ): Promise<ServiceBookingSlot[]> {
    return await get<ServiceBookingSlot[]>(
      `/bookings/available-slots/${serviceId}?startDate=${startDate}&endDate=${endDate}`
    )
  },

  // Vérifier la disponibilité d'un créneau
  async checkAvailability(
    serviceId: string,
    date: string,
    startTime: string,
    endTime: string
  ): Promise<{ isAvailable: boolean; conflicts?: string[] }> {
    return await post<{ isAvailable: boolean; conflicts?: string[] }>(
      `/bookings/check-availability`,
      { serviceId, date, startTime, endTime }
    )
  },

  // Calculer le prix d'une réservation
  async calculatePrice(
    serviceId: string,
    date: string,
    startTime: string,
    endTime: string,
    isUrgent: boolean = false
  ): Promise<{
    basePrice: number
    urgentFee: number
    serviceFee: number
    totalPrice: number
    currency: string
  }> {
    return await post<{
      basePrice: number
      urgentFee: number
      serviceFee: number
      totalPrice: number
      currency: string
    }>(`/bookings/calculate-price`, {
      serviceId,
      date,
      startTime,
      endTime,
      isUrgent
    })
  },

  // Obtenir les statistiques de réservation
  async getBookingStats(): Promise<BookingStats> {
    return await get<BookingStats>('/bookings/stats')
  },

  // Obtenir les réservations à venir
  async getUpcomingBookings(limit: number = 5): Promise<Booking[]> {
    return await get<Booking[]>(`/bookings/upcoming?limit=${limit}`)
  },

  // Obtenir l'historique des réservations
  async getBookingHistory(
    pagination: PaginationParams = {}
  ): Promise<PaginatedResponse<Booking>> {
    const params = new URLSearchParams()
    
    if (pagination.pageNumber) params.append('pageNumber', pagination.pageNumber.toString())
    if (pagination.pageSize) params.append('pageSize', pagination.pageSize.toString())
    if (pagination.sortBy) params.append('sortBy', pagination.sortBy)
    if (pagination.sortDirection) params.append('sortDirection', pagination.sortDirection)

    return await get<PaginatedResponse<Booking>>(`/bookings/history?${params.toString()}`)
  },

  // Demander un remboursement
  async requestRefund(id: string, reason: string): Promise<void> {
    await post(`/bookings/${id}/refund`, { reason })
  },

  // Signaler un problème avec une réservation
  async reportIssue(id: string, issue: string, description?: string): Promise<void> {
    await post(`/bookings/${id}/report`, { issue, description })
  }
}
