using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Factory pour créer et gérer les services de notification externes
/// </summary>
public class ExternalNotificationServiceFactory : IExternalNotificationServiceFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ExternalNotificationServiceFactory> _logger;
    private readonly ExternalNotificationSettings _settings;
    private readonly Dictionary<ExternalNotificationType, IExternalNotificationService> _services;

    public ExternalNotificationServiceFactory(
        IServiceProvider serviceProvider,
        ILogger<ExternalNotificationServiceFactory> logger,
        IConfiguration configuration)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _settings = configuration.GetSection("ExternalNotifications").Get<ExternalNotificationSettings>() ?? new ExternalNotificationSettings();
        _services = new Dictionary<ExternalNotificationType, IExternalNotificationService>();

        InitializeServices();
    }

    /// <inheritdoc />
    public IExternalNotificationService CreateNotificationService(ExternalNotificationType type)
    {
        try
        {
            if (_services.TryGetValue(type, out var service))
            {
                return service;
            }

            _logger.LogWarning("Service de notification {Type} non disponible", type);
            throw new NotSupportedException($"Service de notification {type} non supporté ou non configuré");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du service de notification {Type}", type);
            throw;
        }
    }

    /// <inheritdoc />
    public IEnumerable<IExternalNotificationService> GetAvailableServices()
    {
        return _services.Values;
    }

    /// <inheritdoc />
    public async Task<ExternalNotificationResult> SendNotificationAsync(
        ExternalNotificationRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi de notification {Type} via factory", request.Type);

            var service = CreateNotificationService(request.Type);
            var result = await service.SendNotificationAsync(request, cancellationToken);

            // Log du résultat
            if (result.Success)
            {
                _logger.LogInformation("Notification {Type} envoyée avec succès. ID: {NotificationId}", 
                    request.Type, result.NotificationId);
            }
            else
            {
                _logger.LogWarning("Échec d'envoi notification {Type}: {Error}", 
                    request.Type, result.ErrorMessage);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification {Type}", request.Type);
            
            return new ExternalNotificationResult
            {
                Success = false,
                Type = request.Type,
                Status = ExternalNotificationStatus.Failed,
                ErrorMessage = ex.Message,
                FailedRecipients = request.Recipients
            };
        }
    }

    /// <summary>
    /// Envoie une notification avec fallback automatique
    /// </summary>
    /// <param name="request">Demande de notification</param>
    /// <param name="fallbackTypes">Types de fallback en cas d'échec</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de l'envoi</returns>
    public async Task<ExternalNotificationResult> SendNotificationWithFallbackAsync(
        ExternalNotificationRequest request,
        IEnumerable<ExternalNotificationType> fallbackTypes,
        CancellationToken cancellationToken = default)
    {
        var typesToTry = new List<ExternalNotificationType> { request.Type };
        typesToTry.AddRange(fallbackTypes);

        ExternalNotificationResult? lastResult = null;

        foreach (var type in typesToTry)
        {
            try
            {
                var fallbackRequest = new ExternalNotificationRequest
                {
                    Type = type,
                    Recipients = request.Recipients,
                    Subject = request.Subject,
                    Content = request.Content,
                    TemplateName = request.TemplateName,
                    TemplateData = request.TemplateData,
                    Priority = request.Priority,
                    ScheduledAt = request.ScheduledAt,
                    Metadata = request.Metadata,
                    Language = request.Language,
                    Attachments = request.Attachments,
                    Tags = request.Tags
                };
                var result = await SendNotificationAsync(fallbackRequest, cancellationToken);

                if (result.Success)
                {
                    if (type != request.Type)
                    {
                        _logger.LogInformation("Notification envoyée avec succès via fallback {FallbackType} après échec de {OriginalType}", 
                            type, request.Type);
                    }
                    return result;
                }

                lastResult = result;
                _logger.LogWarning("Échec d'envoi via {Type}, tentative suivante...", type);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la tentative d'envoi via {Type}", type);
            }
        }

        _logger.LogError("Échec d'envoi de notification après toutes les tentatives");
        return lastResult ?? new ExternalNotificationResult
        {
            Success = false,
            Type = request.Type,
            Status = ExternalNotificationStatus.Failed,
            ErrorMessage = "Échec après toutes les tentatives de fallback",
            FailedRecipients = request.Recipients
        };
    }

    /// <summary>
    /// Envoie une notification multicanal (email + SMS + push)
    /// </summary>
    /// <param name="emailRequest">Demande email</param>
    /// <param name="smsRequest">Demande SMS</param>
    /// <param name="pushRequest">Demande push</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultats des envois</returns>
    public async Task<MultiChannelNotificationResult> SendMultiChannelNotificationAsync(
        ExternalNotificationRequest? emailRequest = null,
        ExternalNotificationRequest? smsRequest = null,
        ExternalNotificationRequest? pushRequest = null,
        CancellationToken cancellationToken = default)
    {
        var results = new List<ExternalNotificationResult>();
        var tasks = new List<Task<ExternalNotificationResult>>();

        if (emailRequest != null && _settings.EnabledServices.Contains("Email"))
        {
            tasks.Add(SendNotificationAsync(emailRequest, cancellationToken));
        }

        if (smsRequest != null && _settings.EnabledServices.Contains("SMS"))
        {
            tasks.Add(SendNotificationAsync(smsRequest, cancellationToken));
        }

        if (pushRequest != null && _settings.EnabledServices.Contains("Push"))
        {
            tasks.Add(SendNotificationAsync(pushRequest, cancellationToken));
        }

        if (tasks.Any())
        {
            results.AddRange(await Task.WhenAll(tasks));
        }

        return new MultiChannelNotificationResult
        {
            Results = results,
            OverallSuccess = results.Any(r => r.Success),
            TotalChannels = tasks.Count,
            SuccessfulChannels = results.Count(r => r.Success)
        };
    }

    #region Méthodes privées

    /// <summary>
    /// Initialise les services de notification disponibles
    /// </summary>
    private void InitializeServices()
    {
        try
        {
            // Initialisation du service Email (SendGrid)
            if (_settings.EnabledServices.Contains("Email"))
            {
                try
                {
                    var emailService = _serviceProvider.GetService<SendGridEmailService>();
                    if (emailService != null)
                    {
                        _services[ExternalNotificationType.Email] = emailService;
                        _logger.LogInformation("Service Email (SendGrid) initialisé avec succès");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de l'initialisation du service Email");
                }
            }

            // Initialisation du service SMS (Twilio)
            if (_settings.EnabledServices.Contains("SMS"))
            {
                try
                {
                    var smsService = _serviceProvider.GetService<TwilioSmsService>();
                    if (smsService != null)
                    {
                        _services[ExternalNotificationType.SMS] = smsService;
                        _logger.LogInformation("Service SMS (Twilio) initialisé avec succès");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de l'initialisation du service SMS");
                }
            }

            // Initialisation du service Push (Firebase)
            if (_settings.EnabledServices.Contains("Push"))
            {
                try
                {
                    var pushService = _serviceProvider.GetService<FirebasePushService>();
                    if (pushService != null)
                    {
                        _services[ExternalNotificationType.Push] = pushService;
                        _logger.LogInformation("Service Push (Firebase) initialisé avec succès");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de l'initialisation du service Push");
                }
            }

            _logger.LogInformation("Factory de notifications initialisée avec {Count} services", _services.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initialisation de la factory de notifications");
        }
    }

    #endregion
}

/// <summary>
/// Résultat d'envoi multicanal
/// </summary>
public class MultiChannelNotificationResult
{
    /// <summary>
    /// Résultats par canal
    /// </summary>
    public List<ExternalNotificationResult> Results { get; set; } = new();

    /// <summary>
    /// Succès global (au moins un canal réussi)
    /// </summary>
    public bool OverallSuccess { get; set; }

    /// <summary>
    /// Nombre total de canaux tentés
    /// </summary>
    public int TotalChannels { get; set; }

    /// <summary>
    /// Nombre de canaux réussis
    /// </summary>
    public int SuccessfulChannels { get; set; }

    /// <summary>
    /// Taux de succès
    /// </summary>
    public decimal SuccessRate => TotalChannels > 0 ? (decimal)SuccessfulChannels / TotalChannels * 100 : 0;
}

/// <summary>
/// Configuration des notifications externes
/// </summary>
public class ExternalNotificationSettings
{
    /// <summary>
    /// Services activés
    /// </summary>
    public List<string> EnabledServices { get; set; } = new() { "Email", "SMS", "Push" };

    /// <summary>
    /// Service par défaut pour les emails
    /// </summary>
    public string DefaultEmailService { get; set; } = "SendGrid";

    /// <summary>
    /// Service par défaut pour les SMS
    /// </summary>
    public string DefaultSmsService { get; set; } = "Twilio";

    /// <summary>
    /// Service par défaut pour les push notifications
    /// </summary>
    public string DefaultPushService { get; set; } = "Firebase";

    /// <summary>
    /// Nombre maximum de tentatives d'envoi
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Délai entre les tentatives en secondes
    /// </summary>
    public int RetryDelaySeconds { get; set; } = 30;

    /// <summary>
    /// Activer les templates
    /// </summary>
    public bool EnableTemplates { get; set; } = true;

    /// <summary>
    /// Activer l'envoi en lot
    /// </summary>
    public bool EnableBulkSending { get; set; } = true;

    /// <summary>
    /// Taille maximale des lots
    /// </summary>
    public int MaxBulkSize { get; set; } = 100;

    /// <summary>
    /// Activer le fallback automatique
    /// </summary>
    public bool EnableFallback { get; set; } = true;

    /// <summary>
    /// Configuration des fallbacks par type
    /// </summary>
    public Dictionary<string, List<string>> FallbackConfiguration { get; set; } = new()
    {
        ["Email"] = new() { "SMS" },
        ["SMS"] = new() { "Push" },
        ["Push"] = new() { "Email" }
    };
}
