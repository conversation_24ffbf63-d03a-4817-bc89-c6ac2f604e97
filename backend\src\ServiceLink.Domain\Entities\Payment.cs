using ServiceLink.Domain.Enums;

namespace ServiceLink.Domain.Entities;

/// <summary>
/// Entité représentant un paiement
/// </summary>
public class Payment : BaseEntity
{
    /// <summary>
    /// ID de la réservation associée
    /// </summary>
    public Guid BookingId { get; private set; }

    /// <summary>
    /// Montant du paiement en centimes
    /// </summary>
    public long Amount { get; private set; }

    /// <summary>
    /// Devise du paiement
    /// </summary>
    public string Currency { get; private set; } = "EUR";

    /// <summary>
    /// Méthode de paiement utilisée
    /// </summary>
    public PaymentMethod Method { get; private set; }

    /// <summary>
    /// Statut du paiement
    /// </summary>
    public PaymentStatus Status { get; private set; }

    /// <summary>
    /// Provider de paiement utilisé
    /// </summary>
    public string PaymentProvider { get; private set; } = string.Empty;

    /// <summary>
    /// ID de transaction externe
    /// </summary>
    public string? ExternalTransactionId { get; private set; }

    /// <summary>
    /// Référence de la passerelle de paiement
    /// </summary>
    public string? GatewayReference { get; private set; }

    /// <summary>
    /// Date de traitement du paiement
    /// </summary>
    public DateTime? ProcessedAt { get; private set; }

    /// <summary>
    /// Date d'autorisation du paiement
    /// </summary>
    public DateTime? AuthorizedAt { get; private set; }

    /// <summary>
    /// Date de capture du paiement
    /// </summary>
    public DateTime? CapturedAt { get; private set; }

    /// <summary>
    /// Date d'échec du paiement
    /// </summary>
    public DateTime? FailedAt { get; private set; }

    /// <summary>
    /// Raison de l'échec
    /// </summary>
    public string? FailureReason { get; private set; }

    /// <summary>
    /// Code d'erreur de la passerelle
    /// </summary>
    public string? ErrorCode { get; private set; }

    /// <summary>
    /// Montant des frais en centimes
    /// </summary>
    public long FeesAmount { get; private set; }

    /// <summary>
    /// Montant net reçu en centimes
    /// </summary>
    public long NetAmount { get; private set; }

    /// <summary>
    /// Métadonnées du paiement (JSON)
    /// </summary>
    public string? Metadata { get; private set; }

    /// <summary>
    /// Adresse IP du client
    /// </summary>
    public string? ClientIpAddress { get; private set; }

    /// <summary>
    /// User agent du client
    /// </summary>
    public string? ClientUserAgent { get; private set; }

    /// <summary>
    /// Navigation vers la réservation
    /// </summary>
    public virtual Booking? Booking { get; set; }

    /// <summary>
    /// Remboursements associés
    /// </summary>
    public virtual ICollection<Refund> Refunds { get; set; } = new List<Refund>();

    /// <summary>
    /// Constructeur privé pour EF Core
    /// </summary>
    private Payment() { }

    /// <summary>
    /// Constructeur pour créer un nouveau paiement
    /// </summary>
    public Payment(
        Guid bookingId,
        long amount,
        string currency,
        PaymentMethod method,
        string paymentProvider,
        string? clientIpAddress = null,
        string? clientUserAgent = null)
    {
        BookingId = bookingId;
        Amount = amount;
        Currency = currency;
        Method = method;
        PaymentProvider = paymentProvider;
        Status = PaymentStatus.Pending;
        ClientIpAddress = clientIpAddress;
        ClientUserAgent = clientUserAgent;
        NetAmount = amount; // Sera mis à jour après calcul des frais
    }

    /// <summary>
    /// Marque le paiement comme autorisé
    /// </summary>
    public void MarkAsAuthorized(string externalTransactionId, string? gatewayReference = null)
    {
        if (Status != PaymentStatus.Pending)
            throw new InvalidOperationException($"Cannot authorize payment with status {Status}");

        Status = PaymentStatus.Authorized;
        ExternalTransactionId = externalTransactionId;
        GatewayReference = gatewayReference;
        AuthorizedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Marque le paiement comme capturé
    /// </summary>
    public void MarkAsCaptured(long? feesAmount = null)
    {
        if (Status != PaymentStatus.Authorized && Status != PaymentStatus.Pending)
            throw new InvalidOperationException($"Cannot capture payment with status {Status}");

        Status = PaymentStatus.Captured;
        CapturedAt = DateTime.UtcNow;
        ProcessedAt = DateTime.UtcNow;

        if (feesAmount.HasValue)
        {
            FeesAmount = feesAmount.Value;
            NetAmount = Amount - FeesAmount;
        }
    }

    /// <summary>
    /// Marque le paiement comme réussi
    /// </summary>
    public void MarkAsSucceeded(long? feesAmount = null)
    {
        if (Status == PaymentStatus.Succeeded)
            return;

        Status = PaymentStatus.Succeeded;
        ProcessedAt = DateTime.UtcNow;

        if (feesAmount.HasValue)
        {
            FeesAmount = feesAmount.Value;
            NetAmount = Amount - FeesAmount;
        }
    }

    /// <summary>
    /// Marque le paiement comme échoué
    /// </summary>
    public void MarkAsFailed(string reason, string? errorCode = null)
    {
        Status = PaymentStatus.Failed;
        FailedAt = DateTime.UtcNow;
        FailureReason = reason;
        ErrorCode = errorCode;
    }

    /// <summary>
    /// Marque le paiement comme annulé
    /// </summary>
    public void MarkAsCancelled(string reason)
    {
        if (Status == PaymentStatus.Succeeded || Status == PaymentStatus.Captured)
            throw new InvalidOperationException($"Cannot cancel payment with status {Status}");

        Status = PaymentStatus.Cancelled;
        FailureReason = reason;
    }

    /// <summary>
    /// Met à jour les métadonnées
    /// </summary>
    public void UpdateMetadata(string metadata)
    {
        Metadata = metadata;
    }

    /// <summary>
    /// Vérifie si le paiement peut être remboursé
    /// </summary>
    public bool CanBeRefunded()
    {
        return Status == PaymentStatus.Succeeded || Status == PaymentStatus.Captured;
    }

    /// <summary>
    /// Obtient le montant total remboursé
    /// </summary>
    public long GetTotalRefundedAmount()
    {
        return Refunds.Where(r => r.Status == RefundStatus.Succeeded).Sum(r => r.Amount);
    }

    /// <summary>
    /// Obtient le montant disponible pour remboursement
    /// </summary>
    public long GetRefundableAmount()
    {
        return Amount - GetTotalRefundedAmount();
    }

    /// <summary>
    /// Vérifie si le paiement est complètement remboursé
    /// </summary>
    public bool IsFullyRefunded()
    {
        return GetTotalRefundedAmount() >= Amount;
    }
}

/// <summary>
/// Entité représentant un remboursement
/// </summary>
public class Refund : BaseEntity
{
    /// <summary>
    /// ID du paiement original
    /// </summary>
    public Guid PaymentId { get; private set; }

    /// <summary>
    /// Montant du remboursement en centimes
    /// </summary>
    public long Amount { get; private set; }

    /// <summary>
    /// Devise du remboursement
    /// </summary>
    public string Currency { get; private set; } = "EUR";

    /// <summary>
    /// Statut du remboursement
    /// </summary>
    public RefundStatus Status { get; private set; }

    /// <summary>
    /// Raison du remboursement
    /// </summary>
    public string Reason { get; private set; } = string.Empty;

    /// <summary>
    /// ID de transaction externe du remboursement
    /// </summary>
    public string? ExternalRefundId { get; private set; }

    /// <summary>
    /// Date de traitement du remboursement
    /// </summary>
    public DateTime? ProcessedAt { get; private set; }

    /// <summary>
    /// Date d'échec du remboursement
    /// </summary>
    public DateTime? FailedAt { get; private set; }

    /// <summary>
    /// Raison de l'échec
    /// </summary>
    public string? FailureReason { get; private set; }

    /// <summary>
    /// Navigation vers le paiement
    /// </summary>
    public virtual Payment? Payment { get; set; }

    /// <summary>
    /// Constructeur privé pour EF Core
    /// </summary>
    private Refund() { }

    /// <summary>
    /// Constructeur pour créer un nouveau remboursement
    /// </summary>
    public Refund(Guid paymentId, long amount, string currency, string reason)
    {
        PaymentId = paymentId;
        Amount = amount;
        Currency = currency;
        Reason = reason;
        Status = RefundStatus.Pending;
    }

    /// <summary>
    /// Marque le remboursement comme réussi
    /// </summary>
    public void MarkAsSucceeded(string externalRefundId)
    {
        Status = RefundStatus.Succeeded;
        ExternalRefundId = externalRefundId;
        ProcessedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Marque le remboursement comme échoué
    /// </summary>
    public void MarkAsFailed(string reason)
    {
        Status = RefundStatus.Failed;
        FailedAt = DateTime.UtcNow;
        FailureReason = reason;
    }
}
