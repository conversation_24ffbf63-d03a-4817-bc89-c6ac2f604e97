using System.Threading;
using System.Threading.Tasks;
using Moq;
using Xunit;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Handlers;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;

public class LoginCommandHandlerTests
{
    [Fact]
    public async Task Handle_ValidCredentials_ReturnsLoginResponse()
    {
        // Arrange
        var passwordService = new Mock<IPasswordService>();
        var emailService = new Mock<IEmailService>();
        var userRepository = new Mock<IUserRepository>();
        var jwtService = new Mock<IJwtService>();

        var user = new User(
            ServiceLink.Domain.ValueObjects.Email.Create("<EMAIL>"),
            "Test",
            "User",
            ServiceLink.Domain.Enums.UserRole.Client,
            null,
            null
        );
        user.SetPassword("hash", "salt");
        user.ConfirmEmail();
        user.Activate();

        userRepository.Setup(r => r.GetByEmailAsync(It.IsAny<string>(), It.IsAny<CancellationToken>())).ReturnsAsync(user);
        passwordService.Setup(p => p.VerifyPassword(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>())).Returns(true);
        jwtService.Setup(j => j.GenerateJwtToken(user)).Returns("jwt-token");
        jwtService.Setup(j => j.GenerateRefreshToken(user)).Returns("refresh-token");

        var handler = new LoginCommandHandler(passwordService.Object, emailService.Object, userRepository.Object, jwtService.Object);
        var command = new LoginCommand { Email = "<EMAIL>", Password = "password" };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("jwt-token", result.Token);
        Assert.Equal("refresh-token", result.RefreshToken);
        Assert.Equal(user.Email.Value, result.User.Email);
    }

    [Fact]
    public async Task Handle_InvalidPassword_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        var passwordService = new Mock<IPasswordService>();
        var emailService = new Mock<IEmailService>();
        var userRepository = new Mock<IUserRepository>();
        var jwtService = new Mock<IJwtService>();

        var user = new User(
            ServiceLink.Domain.ValueObjects.Email.Create("<EMAIL>"),
            "Test",
            "User",
            ServiceLink.Domain.Enums.UserRole.Client,
            null,
            null
        );
        user.SetPassword("hash", "salt");
        user.ConfirmEmail();
        user.Activate();

        userRepository.Setup(r => r.GetByEmailAsync(It.IsAny<string>(), It.IsAny<CancellationToken>())).ReturnsAsync(user);
        passwordService.Setup(p => p.VerifyPassword(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>())).Returns(false);

        var handler = new LoginCommandHandler(passwordService.Object, emailService.Object, userRepository.Object, jwtService.Object);
        var command = new LoginCommand { Email = "<EMAIL>", Password = "wrongpassword" };

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(() => handler.Handle(command, CancellationToken.None));
    }
}
