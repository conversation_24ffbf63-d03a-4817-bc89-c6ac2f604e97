namespace ServiceLink.Domain.Interfaces;

/// <summary>
/// Interface pour l'Unit of Work pattern
/// Gère les transactions et coordonne les repositories
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// Repository des utilisateurs
    /// </summary>
    IUserRepository Users { get; }

    /// <summary>
    /// Sauvegarde tous les changements dans une transaction
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nombre d'entités affectées</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Démarre une nouvelle transaction
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Transaction</returns>
    Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Valide la transaction en cours
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Annule la transaction en cours
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Exécute une opération dans une transaction
    /// </summary>
    /// <param name="operation">Opération à exécuter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de l'opération</returns>
    Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Exécute une opération dans une transaction (sans retour)
    /// </summary>
    /// <param name="operation">Opération à exécuter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task ExecuteInTransactionAsync(Func<Task> operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Détache une entité du contexte
    /// </summary>
    /// <param name="entity">Entité à détacher</param>
    void Detach(object entity);

    /// <summary>
    /// Recharge une entité depuis la base de données
    /// </summary>
    /// <param name="entity">Entité à recharger</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task ReloadAsync(object entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie s'il y a des changements en attente
    /// </summary>
    /// <returns>True s'il y a des changements</returns>
    bool HasChanges();

    /// <summary>
    /// Annule tous les changements en attente
    /// </summary>
    void DiscardChanges();
}

/// <summary>
/// Interface pour les transactions de base de données
/// </summary>
public interface IDbTransaction : IDisposable
{
    /// <summary>
    /// Valide la transaction
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task CommitAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Annule la transaction
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task RollbackAsync(CancellationToken cancellationToken = default);
}
