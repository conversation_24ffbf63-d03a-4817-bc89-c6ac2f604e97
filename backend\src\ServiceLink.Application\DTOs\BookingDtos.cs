using ServiceLink.Domain.Enums;
using ServiceLink.Domain.ValueObjects;
using System.ComponentModel.DataAnnotations;

namespace ServiceLink.Application.DTOs;

/// <summary>
/// DTO pour créer une nouvelle réservation
/// </summary>
public class CreateBookingRequest
{
    /// <summary>
    /// ID du prestataire
    /// </summary>
    [Required]
    public Guid ProviderId { get; set; }

    /// <summary>
    /// ID du service
    /// </summary>
    [Required]
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Date et heure prévues pour le service
    /// </summary>
    [Required]
    public DateTime ScheduledDate { get; set; }

    /// <summary>
    /// Durée estimée en minutes
    /// </summary>
    [Range(15, 1440)] // 15 minutes à 24 heures
    public int EstimatedDurationMinutes { get; set; }

    /// <summary>
    /// Adresse où le service doit être effectué
    /// </summary>
    [Required]
    public ServiceAddressDto ServiceAddress { get; set; } = new();

    /// <summary>
    /// Notes du client
    /// </summary>
    [MaxLength(1000)]
    public string ClientNotes { get; set; } = string.Empty;

    /// <summary>
    /// Indique si c'est une réservation urgente
    /// </summary>
    public bool IsUrgent { get; set; }

    /// <summary>
    /// Indique si c'est une réservation récurrente
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Configuration de récurrence (JSON)
    /// </summary>
    public string? RecurrenceConfig { get; set; }

    /// <summary>
    /// Métadonnées additionnelles
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// DTO pour l'adresse de service
/// </summary>
public class ServiceAddressDto
{
    /// <summary>
    /// Ligne d'adresse 1
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string AddressLine1 { get; set; } = string.Empty;

    /// <summary>
    /// Ligne d'adresse 2 (optionnelle)
    /// </summary>
    [MaxLength(200)]
    public string? AddressLine2 { get; set; }

    /// <summary>
    /// Ville
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string City { get; set; } = string.Empty;

    /// <summary>
    /// Code postal
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string PostalCode { get; set; } = string.Empty;

    /// <summary>
    /// Région/État
    /// </summary>
    [MaxLength(100)]
    public string? Region { get; set; }

    /// <summary>
    /// Pays
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Country { get; set; } = string.Empty;

    /// <summary>
    /// Latitude
    /// </summary>
    public double? Latitude { get; set; }

    /// <summary>
    /// Longitude
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// Instructions d'accès spéciales
    /// </summary>
    [MaxLength(500)]
    public string? AccessInstructions { get; set; }

    /// <summary>
    /// Étage ou numéro d'appartement
    /// </summary>
    [MaxLength(50)]
    public string? Floor { get; set; }

    /// <summary>
    /// Code d'accès ou digicode
    /// </summary>
    [MaxLength(50)]
    public string? AccessCode { get; set; }

    /// <summary>
    /// Convertit vers ServiceAddress
    /// </summary>
    public ServiceAddress ToServiceAddress()
    {
        return new ServiceAddress(
            AddressLine1,
            City,
            PostalCode,
            Country,
            AddressLine2,
            Region,
            Latitude,
            Longitude,
            AccessInstructions,
            Floor,
            AccessCode);
    }
}

/// <summary>
/// DTO de réponse pour une réservation
/// </summary>
public class BookingResponse
{
    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// ID du client
    /// </summary>
    public Guid ClientId { get; set; }

    /// <summary>
    /// Informations du client
    /// </summary>
    public UserSummaryDto? Client { get; set; }

    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// Informations du prestataire
    /// </summary>
    public UserSummaryDto? Provider { get; set; }

    /// <summary>
    /// ID du service
    /// </summary>
    public Guid ServiceId { get; set; }

    /// <summary>
    /// Informations du service
    /// </summary>
    public ServiceSummaryDto? Service { get; set; }

    /// <summary>
    /// Date et heure prévues
    /// </summary>
    public DateTime ScheduledDate { get; set; }

    /// <summary>
    /// Date et heure de fin prévues
    /// </summary>
    public DateTime? ScheduledEndDate { get; set; }

    /// <summary>
    /// Statut de la réservation
    /// </summary>
    public BookingStatus Status { get; set; }

    /// <summary>
    /// Description du statut
    /// </summary>
    public string StatusDescription { get; set; } = string.Empty;

    /// <summary>
    /// Montant total en centimes
    /// </summary>
    public long TotalAmount { get; set; }

    /// <summary>
    /// Montant total formaté
    /// </summary>
    public string TotalAmountFormatted { get; set; } = string.Empty;

    /// <summary>
    /// Montant de la commission en centimes
    /// </summary>
    public long CommissionAmount { get; set; }

    /// <summary>
    /// Devise
    /// </summary>
    public string Currency { get; set; } = "EUR";

    /// <summary>
    /// Notes du client
    /// </summary>
    public string ClientNotes { get; set; } = string.Empty;

    /// <summary>
    /// Notes du prestataire
    /// </summary>
    public string ProviderNotes { get; set; } = string.Empty;

    /// <summary>
    /// Adresse du service
    /// </summary>
    public ServiceAddressDto ServiceAddress { get; set; } = new();

    /// <summary>
    /// Durée estimée en minutes
    /// </summary>
    public int EstimatedDurationMinutes { get; set; }

    /// <summary>
    /// Durée réelle en minutes
    /// </summary>
    public int? ActualDurationMinutes { get; set; }

    /// <summary>
    /// Date de confirmation
    /// </summary>
    public DateTime? ConfirmedAt { get; set; }

    /// <summary>
    /// Date de début réel
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// Date de fin réelle
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Date d'annulation
    /// </summary>
    public DateTime? CancelledAt { get; set; }

    /// <summary>
    /// Raison de l'annulation
    /// </summary>
    public string? CancellationReason { get; set; }

    /// <summary>
    /// Date d'expiration
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Temps restant avant expiration
    /// </summary>
    public TimeSpan? TimeUntilExpiration { get; set; }

    /// <summary>
    /// Indique si c'est une réservation urgente
    /// </summary>
    public bool IsUrgent { get; set; }

    /// <summary>
    /// Indique si c'est une réservation récurrente
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Indique si la réservation peut être annulée
    /// </summary>
    public bool CanBeCancelled { get; set; }

    /// <summary>
    /// Indique si la réservation peut être modifiée
    /// </summary>
    public bool CanBeModified { get; set; }

    /// <summary>
    /// Date de création
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date de mise à jour
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Avis associé
    /// </summary>
    public ReviewSummaryDto? Review { get; set; }
}

/// <summary>
/// DTO pour mettre à jour une réservation
/// </summary>
public class UpdateBookingRequest
{
    /// <summary>
    /// Nouvelle date et heure prévues
    /// </summary>
    public DateTime? ScheduledDate { get; set; }

    /// <summary>
    /// Nouvelle durée estimée en minutes
    /// </summary>
    [Range(15, 1440)]
    public int? EstimatedDurationMinutes { get; set; }

    /// <summary>
    /// Nouvelles notes du client
    /// </summary>
    [MaxLength(1000)]
    public string? ClientNotes { get; set; }

    /// <summary>
    /// Nouvelle adresse de service
    /// </summary>
    public ServiceAddressDto? ServiceAddress { get; set; }

    /// <summary>
    /// Raison de la modification
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string ModificationReason { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour annuler une réservation
/// </summary>
public class CancelBookingRequest
{
    /// <summary>
    /// Raison de l'annulation
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// DTO résumé pour un utilisateur
/// </summary>
public class UserSummaryDto
{
    public Guid Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string? AvatarUrl { get; set; }
    public decimal? AverageRating { get; set; }
    public int? TotalReviews { get; set; }
}

/// <summary>
/// DTO résumé pour un service
/// </summary>
public class ServiceSummaryDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ShortDescription { get; set; } = string.Empty;
    public long BasePrice { get; set; }
    public string BasePriceFormatted { get; set; } = string.Empty;
    public PricingUnit PricingUnit { get; set; }
    public string Currency { get; set; } = "EUR";
    public string CategoryName { get; set; } = string.Empty;
    public decimal AverageRating { get; set; }
    public int TotalReviews { get; set; }
}

/// <summary>
/// DTO résumé pour un avis
/// </summary>
public class ReviewSummaryDto
{
    public Guid Id { get; set; }
    public int Rating { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public bool WouldRecommend { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? ProviderResponse { get; set; }
    public DateTime? ProviderResponseAt { get; set; }
}
