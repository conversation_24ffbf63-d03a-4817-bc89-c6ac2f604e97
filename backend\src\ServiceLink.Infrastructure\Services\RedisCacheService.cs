using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using StackExchange.Redis;
using System.Text.Json;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Implémentation du service de cache Redis
/// Utilise StackExchange.Redis pour les opérations avancées et IDistributedCache pour les opérations de base
/// </summary>
public class RedisCacheService : ICacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly IDatabase _database;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisCacheService(
        IDistributedCache distributedCache,
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<RedisCacheService> logger)
    {
        _distributedCache = distributedCache;
        _connectionMultiplexer = connectionMultiplexer;
        _database = connectionMultiplexer.GetDatabase();
        _logger = logger;
        
        // Configuration JSON pour la sérialisation
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };
    }

    /// <inheritdoc />
    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            _logger.LogDebug("Récupération de la clé de cache: {Key}", key);
            
            var cachedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
            
            if (string.IsNullOrEmpty(cachedValue))
            {
                _logger.LogDebug("Cache miss pour la clé: {Key}", key);
                return null;
            }

            _logger.LogDebug("Cache hit pour la clé: {Key}", key);
            return JsonSerializer.Deserialize<T>(cachedValue, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du cache pour la clé: {Key}", key);
            return null;
        }
    }

    /// <inheritdoc />
    public async Task SetAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            _logger.LogDebug("Mise en cache de la clé: {Key} avec expiration: {Expiration}", key, expiration);
            
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration
            };

            await _distributedCache.SetStringAsync(key, serializedValue, options, cancellationToken);
            
            _logger.LogDebug("Valeur mise en cache avec succès pour la clé: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise en cache pour la clé: {Key}", key);
        }
    }

    /// <inheritdoc />
    public async Task SetAsync<T>(string key, T value, CancellationToken cancellationToken = default) where T : class
    {
        await SetAsync(key, value, CacheTTL.Default, cancellationToken);
    }

    /// <inheritdoc />
    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Suppression de la clé de cache: {Key}", key);
            await _distributedCache.RemoveAsync(key, cancellationToken);
            _logger.LogDebug("Clé supprimée avec succès: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la clé: {Key}", key);
        }
    }

    /// <inheritdoc />
    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Suppression des clés par pattern: {Pattern}", pattern);
            
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern);
            
            var keyArray = keys.ToArray();
            if (keyArray.Length > 0)
            {
                await _database.KeyDeleteAsync(keyArray);
                _logger.LogDebug("Supprimé {Count} clés correspondant au pattern: {Pattern}", keyArray.Length, pattern);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression par pattern: {Pattern}", pattern);
        }
    }

    /// <inheritdoc />
    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.KeyExistsAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification d'existence de la clé: {Key}", key);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan expiration, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            // Tentative de récupération depuis le cache
            var cachedValue = await GetAsync<T>(key, cancellationToken);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            _logger.LogDebug("Cache miss pour {Key}, exécution de la factory", key);
            
            // Récupération depuis la source de données
            var value = await factory();
            if (value != null)
            {
                // Mise en cache de la nouvelle valeur
                await SetAsync(key, value, expiration, cancellationToken);
            }

            return value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur dans GetOrSetAsync pour la clé: {Key}", key);
            
            // En cas d'erreur de cache, on essaie quand même d'exécuter la factory
            try
            {
                return await factory();
            }
            catch (Exception factoryEx)
            {
                _logger.LogError(factoryEx, "Erreur dans la factory pour la clé: {Key}", key);
                return null;
            }
        }
    }

    /// <inheritdoc />
    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, CancellationToken cancellationToken = default) where T : class
    {
        return await GetOrSetAsync(key, factory, CacheTTL.Default, cancellationToken);
    }

    /// <inheritdoc />
    public async Task RefreshAsync<T>(string key, Func<Task<T?>> factory, TimeSpan expiration, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            _logger.LogDebug("Rafraîchissement du cache pour la clé: {Key}", key);
            
            var value = await factory();
            if (value != null)
            {
                await SetAsync(key, value, expiration, cancellationToken);
                _logger.LogDebug("Cache rafraîchi avec succès pour la clé: {Key}", key);
            }
            else
            {
                // Si la factory retourne null, on supprime la clé du cache
                await RemoveAsync(key, cancellationToken);
                _logger.LogDebug("Clé supprimée du cache car factory a retourné null: {Key}", key);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du rafraîchissement du cache pour la clé: {Key}", key);
        }
    }

    /// <inheritdoc />
    public async Task<CacheStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var info = await server.InfoAsync();
            
            var stats = new CacheStatistics();
            
            // Extraction des statistiques depuis Redis INFO
            foreach (var section in info)
            {
                foreach (var item in section)
                {
                    switch (item.Key.ToLower())
                    {
                        case "db0":
                            // Format: keys=X,expires=Y,avg_ttl=Z
                            var dbInfo = item.Value.Split(',');
                            if (dbInfo.Length > 0 && dbInfo[0].StartsWith("keys="))
                            {
                                if (long.TryParse(dbInfo[0].Substring(5), out var keys))
                                {
                                    stats.TotalKeys = keys;
                                }
                            }
                            break;
                        case "used_memory":
                            if (long.TryParse(item.Value, out var memory))
                            {
                                stats.UsedMemory = memory;
                            }
                            break;
                        case "connected_clients":
                            if (int.TryParse(item.Value, out var clients))
                            {
                                stats.ConnectedClients = clients;
                            }
                            break;
                        case "total_commands_processed":
                            if (long.TryParse(item.Value, out var commands))
                            {
                                stats.TotalCommandsProcessed = commands;
                            }
                            break;
                        case "uptime_in_seconds":
                            if (long.TryParse(item.Value, out var uptime))
                            {
                                stats.UptimeInSeconds = uptime;
                            }
                            break;
                    }
                }
            }
            
            // Calcul approximatif du hit ratio (nécessiterait un tracking plus sophistiqué)
            stats.HitRatio = 0.0; // À implémenter avec des métriques personnalisées si nécessaire
            
            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques Redis");
            return new CacheStatistics();
        }
    }
}
