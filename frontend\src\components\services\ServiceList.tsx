import React, { useEffect } from 'react';
import { ServiceCard } from './ServiceCard';
import { ServiceFilters } from './ServiceFilters';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useServiceStore, useUIStore } from '@/stores';
import type { Service } from '@/stores/serviceStore';

interface ServiceListProps {
  onBookService?: (service: Service) => void;
}

export const ServiceList: React.FC<ServiceListProps> = ({ onBookService }) => {
  const { 
    searchResults, 
    isLoading, 
    error, 
    searchServices, 
    searchFilters,
    clearError 
  } = useServiceStore();
  
  const { showToast } = useUIStore();

  useEffect(() => {
    // Load initial services
    searchServices({}, 1, 20);
  }, [searchServices]);

  useEffect(() => {
    if (error) {
      showToast({
        type: 'error',
        title: 'Error',
        message: error,
      });
      clearError();
    }
  }, [error, showToast, clearError]);

  const handleLoadMore = () => {
    if (searchResults && searchResults.page < searchResults.totalPages) {
      searchServices(searchFilters, searchResults.page + 1, searchResults.pageSize);
    }
  };

  const handleBookService = (service: Service) => {
    onBookService?.(service);
  };

  if (isLoading && !searchResults) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading services...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <ServiceFilters />

      {/* Results Header */}
      {searchResults && (
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-foreground">
              Services Available
            </h2>
            <p className="text-muted-foreground">
              {searchResults.totalCount} service{searchResults.totalCount !== 1 ? 's' : ''} found
            </p>
          </div>
        </div>
      )}

      {/* Services Grid */}
      {searchResults && searchResults.items.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {searchResults.items.map((service) => (
            <ServiceCard
              key={service.id}
              service={service}
              onBookNow={handleBookService}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground text-lg">
            No services found matching your criteria.
          </p>
          <p className="text-muted-foreground mt-2">
            Try adjusting your filters or search terms.
          </p>
        </div>
      )}

      {/* Load More Button */}
      {searchResults && searchResults.page < searchResults.totalPages && (
        <div className="flex justify-center pt-6">
          <Button
            onClick={handleLoadMore}
            disabled={isLoading}
            variant="outline"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </>
            ) : (
              'Load More Services'
            )}
          </Button>
        </div>
      )}
    </div>
  );
};
