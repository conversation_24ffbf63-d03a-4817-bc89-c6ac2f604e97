import React from 'react'
import { Upload, File, X, Check, AlertCircle } from 'lucide-react'
import { Button } from '../ui/Button'

interface DocumentFile {
  file: File
  type: string
  preview?: string
}

interface DocumentUploadProps {
  onUpload: (files: DocumentFile[]) => Promise<void>
  isLoading?: boolean
}

export const DocumentUpload: React.FC<DocumentUploadProps> = ({
  onUpload,
  isLoading = false,
}) => {
  const [documents, setDocuments] = React.useState<DocumentFile[]>([])
  const [dragActive, setDragActive] = React.useState(false)
  const fileInputRef = React.useRef<HTMLInputElement>(null)

  const documentTypes = [
    { value: 'identity', label: 'Pièce d\'identité (CNI, Passeport)' },
    { value: 'kbis', label: 'Extrait Kbis (moins de 3 mois)' },
    { value: 'insurance', label: 'Attestation d\'assurance professionnelle' },
    { value: 'diploma', label: 'Diplômes/Certifications (optionnel)' },
  ]

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    handleFiles(files)
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    handleFiles(files)
  }

  const handleFiles = (files: File[]) => {
    const validFiles = files.filter(file => {
      const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
      const isValidSize = file.size <= 10 * 1024 * 1024 // 10MB
      return isValidType && isValidSize
    })

    validFiles.forEach(file => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const newDocument: DocumentFile = {
          file,
          type: '', // L'utilisateur devra sélectionner le type
          preview: e.target?.result as string,
        }
        setDocuments(prev => [...prev, newDocument])
      }
      reader.readAsDataURL(file)
    })
  }

  const removeDocument = (index: number) => {
    setDocuments(prev => prev.filter((_, i) => i !== index))
  }

  const updateDocumentType = (index: number, type: string) => {
    setDocuments(prev => prev.map((doc, i) => 
      i === index ? { ...doc, type } : doc
    ))
  }

  const handleSubmit = async () => {
    const validDocuments = documents.filter(doc => doc.type)
    if (validDocuments.length === 0) {
      alert('Veuillez ajouter au moins un document avec un type sélectionné')
      return
    }

    await onUpload(validDocuments)
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Documents de vérification
          </h2>
          <p className="text-gray-600">
            Téléchargez vos documents pour vérifier votre identité et votre entreprise
          </p>
        </div>

        {/* Zone de drop */}
        <div
          className={`
            relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
            ${dragActive 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
            }
          `}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-lg font-medium text-gray-900 mb-2">
            Glissez-déposez vos fichiers ici
          </p>
          <p className="text-gray-500 mb-4">
            ou cliquez pour sélectionner des fichiers
          </p>
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
          >
            Sélectionner des fichiers
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".jpg,.jpeg,.png,.pdf"
            onChange={handleFileInput}
            className="hidden"
          />
          <p className="text-xs text-gray-500 mt-4">
            Formats acceptés : JPG, PNG, PDF (max 10MB par fichier)
          </p>
        </div>

        {/* Documents requis */}
        <div className="mt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Documents requis
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {documentTypes.map((docType) => (
              <div key={docType.value} className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0 mr-3">
                  {documents.some(doc => doc.type === docType.value) ? (
                    <Check className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {docType.label}
                  </p>
                  {docType.value !== 'diploma' && (
                    <p className="text-xs text-red-600">Requis</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Liste des documents uploadés */}
        {documents.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Documents téléchargés ({documents.length})
            </h3>
            <div className="space-y-4">
              {documents.map((document, index) => (
                <div key={index} className="flex items-center p-4 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 mr-4">
                    {document.file.type.startsWith('image/') ? (
                      <img
                        src={document.preview}
                        alt="Preview"
                        className="h-12 w-12 object-cover rounded"
                      />
                    ) : (
                      <File className="h-12 w-12 text-gray-400" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {document.file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(document.file.size)}
                    </p>
                    
                    <select
                      value={document.type}
                      onChange={(e) => updateDocumentType(index, e.target.value)}
                      className="mt-2 text-xs border-gray-300 rounded focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="">Sélectionner le type de document</option>
                      {documentTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <button
                    onClick={() => removeDocument(index)}
                    className="flex-shrink-0 ml-4 p-1 text-gray-400 hover:text-red-500 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="mt-8 flex flex-col sm:flex-row gap-4">
          <Button
            onClick={handleSubmit}
            disabled={documents.length === 0 || isLoading}
            isLoading={isLoading}
            className="flex-1"
          >
            {isLoading ? 'Envoi en cours...' : 'Envoyer les documents'}
          </Button>
          
          <Button
            variant="outline"
            onClick={() => window.location.href = '/login'}
            className="flex-1 sm:flex-none"
          >
            Faire plus tard
          </Button>
        </div>

        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">
            Processus de vérification
          </h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Vos documents seront examinés par notre équipe sous 48h</li>
            <li>• Vous recevrez un email de confirmation une fois validés</li>
            <li>• Votre compte sera activé et vous pourrez proposer vos services</li>
            <li>• En cas de problème, nous vous contacterons pour plus d'informations</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
