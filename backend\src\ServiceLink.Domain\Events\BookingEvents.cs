namespace ServiceLink.Domain.Events;

/// <summary>
/// Événement déclenché lors de la création d'une réservation
/// </summary>
public class BookingCreatedEvent : BaseDomainEvent
{
    public Guid BookingId { get; }
    public Guid ClientId { get; }
    public Guid ProviderId { get; }
    public Guid ServiceId { get; }
    public DateTime ScheduledDate { get; }
    public long TotalAmount { get; }

    public BookingCreatedEvent(
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        Guid serviceId,
        DateTime scheduledDate,
        long totalAmount,
        Guid? userId = null) : base(userId)
    {
        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        ServiceId = serviceId;
        ScheduledDate = scheduledDate;
        TotalAmount = totalAmount;
        
        AddMetadata("bookingId", bookingId);
        AddMetadata("clientId", clientId);
        AddMetadata("providerId", providerId);
        AddMetadata("serviceId", serviceId);
        AddMetadata("totalAmount", totalAmount);
    }
}

/// <summary>
/// Événement déclenché lors de la confirmation d'une réservation
/// </summary>
public class BookingConfirmedEvent : BaseDomainEvent
{
    public Guid BookingId { get; }
    public Guid ClientId { get; }
    public Guid ProviderId { get; }
    public DateTime ConfirmedAt { get; }

    public BookingConfirmedEvent(
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        DateTime confirmedAt,
        Guid? userId = null) : base(userId)
    {
        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        ConfirmedAt = confirmedAt;
        
        AddMetadata("bookingId", bookingId);
        AddMetadata("confirmedAt", confirmedAt);
    }
}

/// <summary>
/// Événement déclenché lors du rejet d'une réservation
/// </summary>
public class BookingRejectedEvent : BaseDomainEvent
{
    public Guid BookingId { get; }
    public Guid ClientId { get; }
    public Guid ProviderId { get; }
    public string Reason { get; }

    public BookingRejectedEvent(
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        string reason,
        Guid? userId = null) : base(userId)
    {
        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        Reason = reason;
        
        AddMetadata("bookingId", bookingId);
        AddMetadata("reason", reason);
    }
}

/// <summary>
/// Événement déclenché lors du démarrage d'un service
/// </summary>
public class BookingStartedEvent : BaseDomainEvent
{
    public Guid BookingId { get; }
    public Guid ClientId { get; }
    public Guid ProviderId { get; }
    public DateTime StartedAt { get; }

    public BookingStartedEvent(
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        DateTime startedAt,
        Guid? userId = null) : base(userId)
    {
        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        StartedAt = startedAt;
        
        AddMetadata("bookingId", bookingId);
        AddMetadata("startedAt", startedAt);
    }
}

/// <summary>
/// Événement déclenché lors de la completion d'un service
/// </summary>
public class BookingCompletedEvent : BaseDomainEvent
{
    public Guid BookingId { get; }
    public Guid ClientId { get; }
    public Guid ProviderId { get; }
    public DateTime CompletedAt { get; }
    public int? ActualDurationMinutes { get; }

    public BookingCompletedEvent(
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        DateTime completedAt,
        int? actualDurationMinutes,
        Guid? userId = null) : base(userId)
    {
        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        CompletedAt = completedAt;
        ActualDurationMinutes = actualDurationMinutes;
        
        AddMetadata("bookingId", bookingId);
        AddMetadata("completedAt", completedAt);
        if (actualDurationMinutes.HasValue)
            AddMetadata("actualDurationMinutes", actualDurationMinutes.Value);
    }
}

/// <summary>
/// Événement déclenché lors de l'annulation d'une réservation
/// </summary>
public class BookingCancelledEvent : BaseDomainEvent
{
    public Guid BookingId { get; }
    public Guid ClientId { get; }
    public Guid ProviderId { get; }
    public string Reason { get; }
    public Guid CancelledBy { get; }

    public BookingCancelledEvent(
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        string reason,
        Guid cancelledBy,
        Guid? userId = null) : base(userId)
    {
        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        Reason = reason;
        CancelledBy = cancelledBy;
        
        AddMetadata("bookingId", bookingId);
        AddMetadata("reason", reason);
        AddMetadata("cancelledBy", cancelledBy);
    }
}

/// <summary>
/// Événement déclenché lors de l'expiration d'une réservation
/// </summary>
public class BookingExpiredEvent : BaseDomainEvent
{
    public Guid BookingId { get; }
    public Guid ClientId { get; }
    public Guid ProviderId { get; }

    public BookingExpiredEvent(
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        Guid? userId = null) : base(userId)
    {
        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        
        AddMetadata("bookingId", bookingId);
    }
}

/// <summary>
/// Événement déclenché lors de la modification d'une réservation
/// </summary>
public class BookingModifiedEvent : BaseDomainEvent
{
    public Guid BookingId { get; }
    public Guid ClientId { get; }
    public Guid ProviderId { get; }
    public DateTime OldScheduledDate { get; }
    public DateTime NewScheduledDate { get; }
    public long OldAmount { get; }
    public long NewAmount { get; }
    public string ModificationReason { get; }

    public BookingModifiedEvent(
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        DateTime oldScheduledDate,
        DateTime newScheduledDate,
        long oldAmount,
        long newAmount,
        string modificationReason,
        Guid? userId = null) : base(userId)
    {
        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        OldScheduledDate = oldScheduledDate;
        NewScheduledDate = newScheduledDate;
        OldAmount = oldAmount;
        NewAmount = newAmount;
        ModificationReason = modificationReason;
        
        AddMetadata("bookingId", bookingId);
        AddMetadata("modificationReason", modificationReason);
        AddMetadata("oldScheduledDate", oldScheduledDate);
        AddMetadata("newScheduledDate", newScheduledDate);
    }
}

/// <summary>
/// Événement déclenché lors de la création d'un avis
/// </summary>
public class ReviewCreatedEvent : BaseDomainEvent
{
    public Guid ReviewId { get; }
    public Guid BookingId { get; }
    public Guid ClientId { get; }
    public Guid ProviderId { get; }
    public Guid ServiceId { get; }
    public int Rating { get; }

    public ReviewCreatedEvent(
        Guid reviewId,
        Guid bookingId,
        Guid clientId,
        Guid providerId,
        Guid serviceId,
        int rating,
        Guid? userId = null) : base(userId)
    {
        ReviewId = reviewId;
        BookingId = bookingId;
        ClientId = clientId;
        ProviderId = providerId;
        ServiceId = serviceId;
        Rating = rating;
        
        AddMetadata("reviewId", reviewId);
        AddMetadata("bookingId", bookingId);
        AddMetadata("rating", rating);
    }
}

/// <summary>
/// Événement déclenché lors de la réponse d'un prestataire à un avis
/// </summary>
public class ReviewResponseAddedEvent : BaseDomainEvent
{
    public Guid ReviewId { get; }
    public Guid ProviderId { get; }
    public string Response { get; }

    public ReviewResponseAddedEvent(
        Guid reviewId,
        Guid providerId,
        string response,
        Guid? userId = null) : base(userId)
    {
        ReviewId = reviewId;
        ProviderId = providerId;
        Response = response;
        
        AddMetadata("reviewId", reviewId);
        AddMetadata("providerId", providerId);
    }
}
