using FluentAssertions;

namespace ServiceLink.Application.Tests;

/// <summary>
/// Tests de base pour vérifier que l'infrastructure de test fonctionne
/// </summary>
public class BasicTests
{
    [Fact]
    public void BasicTest_ShouldPass()
    {
        // Arrange
        var expected = true;

        // Act
        var actual = true;

        // Assert
        actual.Should().Be(expected);
    }

    [Theory]
    [InlineData(1, 1, 2)]
    [InlineData(2, 3, 5)]
    [InlineData(-1, 1, 0)]
    public void Addition_ShouldReturnCorrectResult(int a, int b, int expected)
    {
        // Act
        var result = a + b;

        // Assert
        result.Should().Be(expected);
    }
}
