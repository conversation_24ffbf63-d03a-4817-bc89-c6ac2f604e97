using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Interfaces;
using System.Security.Claims;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour gérer les commissions
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CommissionController : ControllerBase
{
    private readonly ICommissionService _commissionService;
    private readonly ILogger<CommissionController> _logger;

    public CommissionController(
        ICommissionService commissionService,
        ILogger<CommissionController> logger)
    {
        _commissionService = commissionService;
        _logger = logger;
    }

    /// <summary>
    /// Calcule la commission pour un paiement
    /// </summary>
    /// <param name="request">Détails du calcul</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du calcul de commission</returns>
    [HttpPost("calculate")]
    public async Task<IActionResult> CalculateCommission(
        [FromBody] CommissionCalculationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calcul de commission demandé pour montant {Amount} {Currency}", 
                request.BaseAmount, request.Currency);

            var result = await _commissionService.CalculateCommissionAsync(request, cancellationToken);

            return Ok(new
            {
                success = true,
                data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de commission");
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Obtient les taux de commission pour un provider
    /// </summary>
    /// <param name="provider">Provider de paiement</param>
    /// <param name="serviceCategory">Catégorie de service</param>
    /// <returns>Configuration des taux</returns>
    [HttpGet("rates/{provider}")]
    public IActionResult GetCommissionRates(
        [FromRoute] string provider,
        [FromQuery] string serviceCategory = "default")
    {
        try
        {
            if (!Enum.TryParse<Domain.Enums.PaymentProvider>(provider, true, out var paymentProvider))
            {
                return BadRequest(new { success = false, message = "Provider invalide" });
            }

            var rates = _commissionService.GetCommissionRates(paymentProvider, serviceCategory);

            return Ok(new
            {
                success = true,
                provider = provider,
                serviceCategory = serviceCategory,
                data = rates
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des taux pour {Provider}", provider);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Récupère l'historique des commissions de l'utilisateur connecté
    /// </summary>
    /// <param name="startDate">Date de début (optionnelle)</param>
    /// <param name="endDate">Date de fin (optionnelle)</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Historique des commissions</returns>
    [HttpGet("history")]
    public async Task<IActionResult> GetCommissionHistory(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var start = startDate ?? DateTime.UtcNow.AddMonths(-3); // 3 mois par défaut
            var end = endDate ?? DateTime.UtcNow;

            _logger.LogInformation("Récupération de l'historique des commissions pour {UserId} du {StartDate} au {EndDate}", 
                userId, start, end);

            var history = await _commissionService.GetCommissionHistoryAsync(userId, start, end, cancellationToken);

            return Ok(new
            {
                success = true,
                data = history,
                period = new { startDate = start, endDate = end },
                total = history.Count()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'historique des commissions");
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Demande un paiement de commission
    /// </summary>
    /// <param name="request">Détails de la demande de paiement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de la demande</returns>
    [HttpPost("payout")]
    public async Task<IActionResult> RequestCommissionPayout(
        [FromBody] CommissionPayoutRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            request.ServiceProviderId = userId; // S'assurer que l'utilisateur ne peut demander que ses propres commissions

            _logger.LogInformation("Demande de paiement de commission pour {UserId}, montant {Amount}", 
                userId, request.Amount);

            var result = await _commissionService.ProcessCommissionPayoutAsync(request, cancellationToken);

            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    data = result
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = result.ErrorMessage,
                    data = result
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la demande de paiement de commission");
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Calcule la commission de la plateforme pour un montant donné
    /// </summary>
    /// <param name="amount">Montant en centimes</param>
    /// <param name="serviceCategory">Catégorie de service</param>
    /// <param name="userTier">Niveau de l'utilisateur</param>
    /// <returns>Commission calculée</returns>
    [HttpGet("platform-commission")]
    public IActionResult CalculatePlatformCommission(
        [FromQuery] long amount,
        [FromQuery] string serviceCategory = "default",
        [FromQuery] UserTier userTier = UserTier.Standard)
    {
        try
        {
            var commission = _commissionService.CalculatePlatformCommission(amount, serviceCategory, userTier);

            return Ok(new
            {
                success = true,
                data = new
                {
                    baseAmount = amount,
                    platformCommission = commission,
                    netAmount = amount - commission,
                    commissionRate = amount > 0 ? (decimal)commission / amount * 100 : 0,
                    serviceCategory = serviceCategory,
                    userTier = userTier.ToString()
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de la commission plateforme");
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Calcule les frais de traitement pour un provider et une méthode donnés
    /// </summary>
    /// <param name="amount">Montant en centimes</param>
    /// <param name="provider">Provider de paiement</param>
    /// <param name="paymentMethod">Méthode de paiement</param>
    /// <returns>Frais de traitement calculés</returns>
    [HttpGet("processing-fees")]
    public IActionResult CalculateProcessingFees(
        [FromQuery] long amount,
        [FromQuery] string provider,
        [FromQuery] string paymentMethod)
    {
        try
        {
            if (!Enum.TryParse<Domain.Enums.PaymentProvider>(provider, true, out var paymentProvider))
            {
                return BadRequest(new { success = false, message = "Provider invalide" });
            }

            if (!Enum.TryParse<Domain.Enums.PaymentMethodType>(paymentMethod, true, out var methodType))
            {
                return BadRequest(new { success = false, message = "Méthode de paiement invalide" });
            }

            var fees = _commissionService.CalculateProcessingFees(amount, paymentProvider, methodType);

            return Ok(new
            {
                success = true,
                data = new
                {
                    baseAmount = amount,
                    processingFees = fees,
                    totalAmount = amount + fees,
                    feeRate = amount > 0 ? (decimal)fees / amount * 100 : 0,
                    provider = provider,
                    paymentMethod = paymentMethod
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul des frais de traitement");
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Obtient un résumé des commissions pour l'utilisateur connecté
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résumé des commissions</returns>
    [HttpGet("summary")]
    public async Task<IActionResult> GetCommissionSummary(CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddMonths(-1); // Dernier mois

            var history = await _commissionService.GetCommissionHistoryAsync(userId, startDate, endDate, cancellationToken);
            var historyList = history.ToList();

            var summary = new
            {
                period = new { startDate, endDate },
                totalTransactions = historyList.Count,
                totalEarnings = historyList.Sum(h => h.NetAmount),
                totalCommissions = historyList.Sum(h => h.PlatformCommission),
                totalFees = historyList.Sum(h => h.ProcessingFees),
                pendingAmount = historyList.Where(h => h.Status == CommissionStatus.Pending).Sum(h => h.NetAmount),
                paidAmount = historyList.Where(h => h.Status == CommissionStatus.Paid).Sum(h => h.NetAmount),
                averageCommissionRate = historyList.Any() 
                    ? historyList.Average(h => h.BaseAmount > 0 ? (decimal)h.PlatformCommission / h.BaseAmount * 100 : 0)
                    : 0,
                byProvider = historyList.GroupBy(h => h.PaymentProvider)
                    .Select(g => new
                    {
                        provider = g.Key.ToString(),
                        count = g.Count(),
                        totalAmount = g.Sum(h => h.BaseAmount),
                        totalEarnings = g.Sum(h => h.NetAmount)
                    })
                    .ToList()
            };

            return Ok(new
            {
                success = true,
                data = summary
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du résumé des commissions");
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    #region Méthodes privées

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Utilisateur non authentifié");
        }
        return userId;
    }

    #endregion
}
