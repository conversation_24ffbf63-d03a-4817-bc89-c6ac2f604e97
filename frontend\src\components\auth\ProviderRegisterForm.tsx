import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { User, Mail, Lock, Eye, EyeOff, ArrowLeft, Building, Phone, MapPin } from 'lucide-react'
import { But<PERSON> } from '../ui/button'
import { FormInput } from '../ui/form-input'


// Schéma de validation pour l'inscription prestataire
const providerRegisterSchema = z.object({
  firstName: z
    .string()
    .min(1, 'Le prénom est requis')
    .min(2, 'Le prénom doit contenir au moins 2 caractères')
    .max(50, 'Le prénom ne peut pas dépasser 50 caractères'),
  lastName: z
    .string()
    .min(1, 'Le nom est requis')
    .min(2, 'Le nom doit contenir au moins 2 caractères')
    .max(50, 'Le nom ne peut pas dépasser 50 caractères'),
  email: z
    .string()
    .min(1, 'L\'email est requis')
    .email('Format d\'email invalide'),
  password: z
    .string()
    .min(8, 'Le mot de passe doit contenir au moins 8 caractères')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre'
    ),
  confirmPassword: z
    .string()
    .min(1, 'La confirmation du mot de passe est requise'),
  companyName: z
    .string()
    .min(1, 'Le nom de l\'entreprise est requis')
    .max(100, 'Le nom de l\'entreprise ne peut pas dépasser 100 caractères'),
  siret: z
    .string()
    .optional()
    .refine((val) => !val || /^\d{14}$/.test(val), 'Le SIRET doit contenir exactement 14 chiffres'),
  description: z
    .string()
    .min(1, 'La description est requise')
    .min(50, 'La description doit contenir au moins 50 caractères')
    .max(500, 'La description ne peut pas dépasser 500 caractères'),
  phone: z
    .string()
    .min(1, 'Le numéro de téléphone est requis')
    .regex(/^(?:\+33|0)[1-9](?:[0-9]{8})$/, 'Format de téléphone invalide'),
  address: z
    .string()
    .min(1, 'L\'adresse est requise')
    .max(200, 'L\'adresse ne peut pas dépasser 200 caractères'),
  city: z
    .string()
    .min(1, 'La ville est requise')
    .max(100, 'La ville ne peut pas dépasser 100 caractères'),
  postalCode: z
    .string()
    .min(1, 'Le code postal est requis')
    .regex(/^\d{5}$/, 'Le code postal doit contenir 5 chiffres'),
  country: z
    .string()
    .min(1, 'Le pays est requis'),
  acceptTerms: z
    .boolean()
    .refine((val) => val === true, 'Vous devez accepter les conditions d\'utilisation'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword'],
})

type ProviderRegisterFormData = z.infer<typeof providerRegisterSchema>

interface ProviderRegisterFormProps {
  onSuccess?: () => void
  onBack?: () => void
}

export const ProviderRegisterForm: React.FC<ProviderRegisterFormProps> = ({
  onSuccess,
  onBack,
}) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const [showPassword, setShowPassword] = React.useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<ProviderRegisterFormData>({
    resolver: zodResolver(providerRegisterSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      companyName: '',
      siret: '',
      description: '',
      phone: '',
      address: '',
      city: '',
      postalCode: '',
      country: 'France',
      acceptTerms: false,
    },
  })

  const onSubmit = async (data: ProviderRegisterFormData) => {
    try {
      setIsLoading(true)

      // Appel direct à l'API backend avec les bons champs
      const response = await fetch('https://localhost:7276/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          password: data.password,
          confirmPassword: data.confirmPassword,
          role: 2, // UserRole.Provider = 2
          acceptTerms: data.acceptTerms,
          language: 'fr-FR',
          timeZone: 'Europe/Paris',
          // Champs spécifiques prestataire
          companyName: data.companyName,
          siret: data.siret,
          description: data.description,
          phoneNumber: data.phone,
          address: data.address,
          city: data.city,
          postalCode: data.postalCode,
          country: data.country,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Inscription échouée')
      }

      await response.json()
      onSuccess?.()
    } catch (error: any) {
      // Gérer les erreurs spécifiques
      if (error.statusCode === 409) {
        setError('email', {
          message: 'Cette adresse email est déjà utilisée',
        })
      } else if (error.errors) {
        // Erreurs de validation du serveur
        Object.entries(error.errors).forEach(([field, messages]) => {
          if (Array.isArray(messages) && messages.length > 0) {
            setError(field as keyof ProviderRegisterFormData, {
              message: messages[0],
            })
          }
        })
      } else {
        setError('root', {
          message: error.message || 'Une erreur est survenue lors de l\'inscription',
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="flex items-center mb-6">
          {onBack && (
            <button
              onClick={onBack}
              className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          )}
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Inscription Prestataire</h2>
            <p className="text-gray-600 mt-1">
              Créez votre compte pour proposer vos services
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Informations personnelles */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Informations personnelles
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                label="Prénom"
                placeholder="Votre prénom"
                leftIcon={<User className="h-4 w-4" />}
                error={errors.firstName?.message}
                {...register('firstName')}
              />

              <FormInput
                label="Nom"
                placeholder="Votre nom"
                leftIcon={<User className="h-4 w-4" />}
                error={errors.lastName?.message}
                {...register('lastName')}
              />
            </div>

            <div className="mt-4">
              <FormInput
                label="Email"
                type="email"
                placeholder="<EMAIL>"
                leftIcon={<Mail className="h-4 w-4" />}
                error={errors.email?.message}
                {...register('email')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <FormInput
                label="Mot de passe"
                type={showPassword ? 'text' : 'password'}
                placeholder="Votre mot de passe"
                leftIcon={<Lock className="h-4 w-4" />}
                rightIcon={
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                }
                error={errors.password?.message}
                {...register('password')}
              />

              <FormInput
                label="Confirmer le mot de passe"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirmez votre mot de passe"
                leftIcon={<Lock className="h-4 w-4" />}
                rightIcon={
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                }
                error={errors.confirmPassword?.message}
                {...register('confirmPassword')}
              />
            </div>
          </div>

          {/* Informations entreprise */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Informations entreprise
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                label="Nom de l'entreprise"
                placeholder="Nom de votre entreprise"
                leftIcon={<Building className="h-4 w-4" />}
                error={errors.companyName?.message}
                {...register('companyName')}
              />

              <FormInput
                label="Numéro SIRET"
                placeholder="18945678901234"
                leftIcon={<Building className="h-4 w-4" />}
                error={errors.siret?.message}
                helperText="14 chiffres sans espaces"
                {...register('siret')}
              />
            </div>

            <div className="mt-4">
              <FormInput
                label="Téléphone"
                placeholder="01 23 45 67 89"
                leftIcon={<Phone className="h-4 w-4" />}
                error={errors.phone?.message}
                {...register('phone')}
              />
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description de votre activité
              </label>
              <textarea
                placeholder="Décrivez votre activité et vos services..."
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                rows={4}
                {...register('description')}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
              <p className="mt-1 text-sm text-gray-500">
                Minimum 50 caractères, maximum 500 caractères
              </p>
            </div>
          </div>

          {/* Adresse */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Adresse
            </h3>
            <div className="space-y-4">
              <FormInput
                label="Adresse"
                placeholder="123 rue de la République"
                leftIcon={<MapPin className="h-4 w-4" />}
                error={errors.address?.message}
                {...register('address')}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormInput
                  label="Ville"
                  placeholder="Paris"
                  error={errors.city?.message}
                  {...register('city')}
                />

                <FormInput
                  label="Code postal"
                  placeholder="75001"
                  error={errors.postalCode?.message}
                  {...register('postalCode')}
                />

                <FormInput
                  label="Pays"
                  placeholder="France"
                  error={errors.country?.message}
                  {...register('country')}
                />
              </div>
            </div>
          </div>

          <div className="flex items-start">
            <input
              type="checkbox"
              id="acceptTerms"
              className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              {...register('acceptTerms')}
            />
            <label htmlFor="acceptTerms" className="ml-2 text-sm text-gray-600">
              J'accepte les{' '}
              <a href="/terms" className="text-blue-600 hover:text-blue-500">
                conditions d'utilisation
              </a>{' '}
              et la{' '}
              <a href="/privacy" className="text-blue-600 hover:text-blue-500">
                politique de confidentialité
              </a>
            </label>
          </div>
          {errors.acceptTerms && (
            <p className="text-sm text-red-600">{errors.acceptTerms.message}</p>
          )}

          {errors.root && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{errors.root.message}</p>
            </div>
          )}

          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-yellow-800 mb-2">
              Étape suivante : Vérification
            </h4>
            <p className="text-sm text-yellow-700">
              Après votre inscription, vous devrez fournir des documents d'identification 
              pour vérifier votre entreprise. Votre compte sera activé après validation par nos équipes.
            </p>
          </div>

          <Button
            type="submit"
            className="w-full"
            isLoading={isLoading}
            disabled={isLoading}
          >
            {isLoading ? 'Création du compte...' : 'Créer mon compte prestataire'}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Vous avez déjà un compte ?{' '}
            <button
              type="button"
              onClick={() => window.location.href = '/login'}
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Se connecter
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
