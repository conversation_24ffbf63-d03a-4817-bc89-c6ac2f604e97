// Configuration des endpoints API
export const API_CONFIG = {
  // URL de base de l'API
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:5280/api',
  
  // Timeout par défaut
  TIMEOUT: 10000,
  
  // Endpoints d'authentification
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    CHANGE_PASSWORD: '/auth/change-password',
    CONFIRM_EMAIL: '/auth/confirm-email',
    RESEND_CONFIRMATION: '/auth/resend-confirmation',
    PROFILE: '/auth/profile',
  },
  
  // Endpoints des services
  SERVICES: {
    BASE: '/services',
    CATEGORIES: '/services/categories',
    POPULAR: '/services/popular',
    FEATURED: '/services/featured',
    NEARBY: '/services/nearby',
    SUGGESTIONS: '/services/suggestions',
    STATS: '/services/stats',
    FAVORITES: '/services/favorites',
    BY_PROVIDER: (providerId: string) => `/services/provider/${providerId}`,
    BY_ID: (id: string) => `/services/${id}`,
    FAVORITE: (id: string) => `/services/${id}/favorite`,
    REPORT: (id: string) => `/services/${id}/report`,
  },
  
  // Endpoints des réservations
  BOOKINGS: {
    BASE: '/bookings',
    PROVIDER: '/bookings/provider',
    UPCOMING: '/bookings/upcoming',
    HISTORY: '/bookings/history',
    STATS: '/bookings/stats',
    AVAILABLE_SLOTS: (serviceId: string) => `/bookings/available-slots/${serviceId}`,
    CHECK_AVAILABILITY: '/bookings/check-availability',
    CALCULATE_PRICE: '/bookings/calculate-price',
    BY_ID: (id: string) => `/bookings/${id}`,
    CONFIRM: (id: string) => `/bookings/${id}/confirm`,
    REJECT: (id: string) => `/bookings/${id}/reject`,
    CANCEL: (id: string) => `/bookings/${id}/cancel`,
    COMPLETE: (id: string) => `/bookings/${id}/complete`,
    REFUND: (id: string) => `/bookings/${id}/refund`,
    REPORT: (id: string) => `/bookings/${id}/report`,
  },
  
  // Endpoints des utilisateurs
  USERS: {
    BASE: '/users',
    PROFILE: '/users/profile',
    PROVIDERS: '/users/providers',
    STATS: '/users/stats',
    BY_ID: (id: string) => `/users/${id}`,
    BECOME_PROVIDER: '/users/become-provider',
    PROVIDER_APPLICATION: '/users/provider-application',
  },
  
  // Endpoints des paiements
  PAYMENTS: {
    BASE: '/payments',
    STRIPE: '/payments/stripe',
    PAYPAL: '/payments/paypal',
    FLUTTERWAVE: '/payments/flutterwave',
    WEBHOOKS: '/payments/webhooks',
    METHODS: '/payments/methods',
    HISTORY: '/payments/history',
    REFUND: (id: string) => `/payments/${id}/refund`,
  },
  
  // Endpoints des notifications
  NOTIFICATIONS: {
    BASE: '/notifications',
    MARK_READ: '/notifications/mark-read',
    MARK_ALL_READ: '/notifications/mark-all-read',
    SETTINGS: '/notifications/settings',
    BY_ID: (id: string) => `/notifications/${id}`,
  },
  
  // Endpoints des reviews
  REVIEWS: {
    BASE: '/reviews',
    BY_SERVICE: (serviceId: string) => `/reviews/service/${serviceId}`,
    BY_PROVIDER: (providerId: string) => `/reviews/provider/${providerId}`,
    BY_USER: (userId: string) => `/reviews/user/${userId}`,
    BY_ID: (id: string) => `/reviews/${id}`,
    RESPOND: (id: string) => `/reviews/${id}/respond`,
    REPORT: (id: string) => `/reviews/${id}/report`,
  },
  
  // Endpoints d'administration
  ADMIN: {
    BASE: '/admin',
    USERS: '/admin/users',
    SERVICES: '/admin/services',
    BOOKINGS: '/admin/bookings',
    PAYMENTS: '/admin/payments',
    REPORTS: '/admin/reports',
    ANALYTICS: '/admin/analytics',
    SETTINGS: '/admin/settings',
    MODERATE_SERVICE: (id: string) => `/admin/services/${id}/moderate`,
    MODERATE_USER: (id: string) => `/admin/users/${id}/moderate`,
    MODERATE_REVIEW: (id: string) => `/admin/reviews/${id}/moderate`,
  },
  
  // Endpoints de géolocalisation
  LOCATION: {
    GEOCODE: '/location/geocode',
    REVERSE_GEOCODE: '/location/reverse-geocode',
    DISTANCE: '/location/distance',
    SUGGESTIONS: '/location/suggestions',
  },
  
  // Endpoints de santé et monitoring
  HEALTH: {
    CHECK: '/health',
    READY: '/health/ready',
    LIVE: '/health/live',
  },
}

// Configuration des headers par défaut
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}

// Configuration des codes de statut
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const

// Configuration des timeouts spécifiques
export const TIMEOUTS = {
  DEFAULT: 10000,
  UPLOAD: 30000,
  DOWNLOAD: 60000,
  LONG_RUNNING: 120000,
} as const

// Configuration de la pagination par défaut
export const PAGINATION_DEFAULTS = {
  PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_SORT: 'createdAt',
  DEFAULT_DIRECTION: 'desc' as const,
} as const

// Configuration du cache
export const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  CATEGORIES_TTL: 60 * 60 * 1000, // 1 heure
  PROFILE_TTL: 15 * 60 * 1000, // 15 minutes
  SERVICES_TTL: 5 * 60 * 1000, // 5 minutes
  STATS_TTL: 60 * 60 * 1000, // 1 heure
} as const

// Configuration des retry
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  BACKOFF_FACTOR: 2,
} as const
