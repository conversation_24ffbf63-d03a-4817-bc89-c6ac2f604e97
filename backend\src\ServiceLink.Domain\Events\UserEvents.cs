using ServiceLink.Domain.Enums;

namespace ServiceLink.Domain.Events;

/// <summary>
/// Événement déclenché lors de la création d'un utilisateur
/// </summary>
public sealed class UserCreatedEvent : BaseDomainEvent
{
    public UserCreatedEvent(Guid userId, string email, UserRole role, Guid? createdBy = null) 
        : base(createdBy)
    {
        UserId = userId;
        Email = email;
        Role = role;
        
        AddMetadata("email", email);
        AddMetadata("role", role.ToString());
    }

    /// <summary>
    /// Identifiant de l'utilisateur créé
    /// </summary>
    public new Guid UserId { get; }

    /// <summary>
    /// Email de l'utilisateur créé
    /// </summary>
    public string Email { get; }

    /// <summary>
    /// Rôle de l'utilisateur créé
    /// </summary>
    public UserRole Role { get; }
}

/// <summary>
/// Événement déclenché lors de la mise à jour d'un utilisateur
/// </summary>
public sealed class UserUpdatedEvent : BaseDomainEvent
{
    public UserUpdatedEvent(Guid userId, Guid? updatedBy = null) 
        : base(updatedBy)
    {
        UserId = userId;
    }

    /// <summary>
    /// Identifiant de l'utilisateur mis à jour
    /// </summary>
    public new Guid UserId { get; }
}

/// <summary>
/// Événement déclenché lors du changement de mot de passe
/// </summary>
public sealed class UserPasswordChangedEvent : BaseDomainEvent
{
    public UserPasswordChangedEvent(Guid userId, Guid? changedBy = null) 
        : base(changedBy)
    {
        UserId = userId;
    }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public new Guid UserId { get; }
}

/// <summary>
/// Événement déclenché lors de la confirmation d'email
/// </summary>
public sealed class UserEmailConfirmedEvent : BaseDomainEvent
{
    public UserEmailConfirmedEvent(Guid userId, string email, Guid? confirmedBy = null) 
        : base(confirmedBy)
    {
        UserId = userId;
        Email = email;
        
        AddMetadata("email", email);
    }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public new Guid UserId { get; }

    /// <summary>
    /// Email confirmé
    /// </summary>
    public string Email { get; }
}

/// <summary>
/// Événement déclenché lors de la confirmation de téléphone
/// </summary>
public sealed class UserPhoneConfirmedEvent : BaseDomainEvent
{
    public UserPhoneConfirmedEvent(Guid userId, string phoneNumber, Guid? confirmedBy = null) 
        : base(confirmedBy)
    {
        UserId = userId;
        PhoneNumber = phoneNumber;
        
        AddMetadata("phoneNumber", phoneNumber);
    }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public new Guid UserId { get; }

    /// <summary>
    /// Numéro de téléphone confirmé
    /// </summary>
    public string PhoneNumber { get; }
}

/// <summary>
/// Événement déclenché lors d'une connexion réussie
/// </summary>
public sealed class UserLoggedInEvent : BaseDomainEvent
{
    public UserLoggedInEvent(Guid userId) 
        : base(userId)
    {
        UserId = userId;
        LoginTime = DateTime.UtcNow;
        
        AddMetadata("loginTime", LoginTime);
    }

    /// <summary>
    /// Identifiant de l'utilisateur connecté
    /// </summary>
    public new Guid UserId { get; }

    /// <summary>
    /// Heure de connexion
    /// </summary>
    public DateTime LoginTime { get; }
}

/// <summary>
/// Événement déclenché lors du verrouillage d'un compte
/// </summary>
public sealed class UserAccountLockedEvent : BaseDomainEvent
{
    public UserAccountLockedEvent(Guid userId, DateTime lockedUntil) 
        : base(userId)
    {
        UserId = userId;
        LockedUntil = lockedUntil;
        
        AddMetadata("lockedUntil", lockedUntil);
    }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public new Guid UserId { get; }

    /// <summary>
    /// Date de fin de verrouillage
    /// </summary>
    public DateTime LockedUntil { get; }
}

/// <summary>
/// Événement déclenché lors du déverrouillage d'un compte
/// </summary>
public sealed class UserAccountUnlockedEvent : BaseDomainEvent
{
    public UserAccountUnlockedEvent(Guid userId, Guid? unlockedBy = null) 
        : base(unlockedBy)
    {
        UserId = userId;
    }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public new Guid UserId { get; }
}

/// <summary>
/// Événement déclenché lors de l'activation de l'authentification à deux facteurs
/// </summary>
public sealed class UserTwoFactorEnabledEvent : BaseDomainEvent
{
    public UserTwoFactorEnabledEvent(Guid userId, Guid? enabledBy = null) 
        : base(enabledBy)
    {
        UserId = userId;
    }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public new Guid UserId { get; }
}

/// <summary>
/// Événement déclenché lors de la désactivation de l'authentification à deux facteurs
/// </summary>
public sealed class UserTwoFactorDisabledEvent : BaseDomainEvent
{
    public UserTwoFactorDisabledEvent(Guid userId, Guid? disabledBy = null) 
        : base(disabledBy)
    {
        UserId = userId;
    }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public new Guid UserId { get; }
}

/// <summary>
/// Événement déclenché lors de la désactivation d'un utilisateur
/// </summary>
public sealed class UserDeactivatedEvent : BaseDomainEvent
{
    public UserDeactivatedEvent(Guid userId, Guid? deactivatedBy = null) 
        : base(deactivatedBy)
    {
        UserId = userId;
    }

    /// <summary>
    /// Identifiant de l'utilisateur désactivé
    /// </summary>
    public new Guid UserId { get; }
}

/// <summary>
/// Événement déclenché lors de la réactivation d'un utilisateur
/// </summary>
public sealed class UserActivatedEvent : BaseDomainEvent
{
    public UserActivatedEvent(Guid userId, Guid? activatedBy = null) 
        : base(activatedBy)
    {
        UserId = userId;
    }

    /// <summary>
    /// Identifiant de l'utilisateur réactivé
    /// </summary>
    public new Guid UserId { get; }
}

/// <summary>
/// Événement déclenché lors du changement de rôle d'un utilisateur
/// </summary>
public sealed class UserRoleChangedEvent : BaseDomainEvent
{
    public UserRoleChangedEvent(Guid userId, UserRole oldRole, UserRole newRole, Guid? changedBy = null) 
        : base(changedBy)
    {
        UserId = userId;
        OldRole = oldRole;
        NewRole = newRole;
        
        AddMetadata("oldRole", oldRole.ToString());
        AddMetadata("newRole", newRole.ToString());
    }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public new Guid UserId { get; }

    /// <summary>
    /// Ancien rôle
    /// </summary>
    public UserRole OldRole { get; }

    /// <summary>
    /// Nouveau rôle
    /// </summary>
    public UserRole NewRole { get; }
}
