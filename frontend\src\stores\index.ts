import { useAuthStore } from './authStore';
import { useNotificationStore } from './notificationStore';
import { useUIStore } from './uiStore';

// Export all stores
export type { User, AuthState, RegisterData, LoginResponse } from './authStore';

export { useServiceStore } from './serviceStore';
export type { 
  Service, 
  ServiceCategory, 
  SearchFilters, 
  PaginatedResult, 
  ServiceState 
} from './serviceStore';

export { useBookingStore } from './bookingStore';
export type { 
  Booking, 
  BookingStatus, 
  CreateBookingRequest, 
  BookingFilters, 
  BookingStats, 
  BookingState 
} from './bookingStore';

export { useNotificationStore } from './notificationStore';
export type { 
  Notification, 
  NotificationType, 
  NotificationPriority, 
  ToastNotification, 
  NotificationState 
} from './notificationStore';

export { useUIStore } from './uiStore';
export type { 
  Theme, 
  Language, 
  Modal, 
  Sidebar, 
  Breadcrumb, 
  UIState 
} from './uiStore';

// Store initialization helper
export const initializeStores = () => {
  // This function can be called in main.tsx to initialize stores
  // Any store-specific initialization logic can go here
  
  // Example: Initialize auth store with persisted data
  const authStore = useAuthStore.getState();
  if (authStore.token && authStore.user) {
    // Optionally validate token or refresh if needed
    authStore.refreshAuth().catch(() => {
      // If refresh fails, logout user
      authStore.logout();
    });
  }
};

// Global error handler for stores
export const handleStoreError = (error: Error, storeName: string) => {
  console.error(`Error in ${storeName}:`, error);
  
  // You can add global error handling logic here
  // For example, show a global error toast
  const notificationStore = useNotificationStore.getState();
  notificationStore.showToast({
    type: 'error',
    title: 'Error',
    message: error.message || 'An unexpected error occurred',
    duration: 5000,
  });
};

// Store selectors for common use cases
export const createAuthSelector = () => {
  const authStore = useAuthStore();
  return {
    user: authStore.user,
    isAuthenticated: authStore.isAuthenticated,
    isLoading: authStore.isLoading,
    error: authStore.error,
  };
};

export const createUISelector = () => {
  const uiStore = useUIStore();
  return {
    theme: uiStore.theme,
    language: uiStore.language,
    isMobile: uiStore.isMobile,
    isLoading: uiStore.isLoading,
    sidebar: uiStore.sidebar,
  };
};

export const createNotificationSelector = () => {
  const notificationStore = useNotificationStore();
  return {
    notifications: notificationStore.notifications,
    unreadCount: notificationStore.unreadCount,
    isConnected: notificationStore.isConnected,
  };
};
