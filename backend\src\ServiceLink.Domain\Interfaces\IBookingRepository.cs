using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Domain.Interfaces;

/// <summary>
/// Interface pour le repository des réservations
/// </summary>
public interface IBookingRepository : IRepository<Booking>
{
    /// <summary>
    /// Obtient les réservations d'un client
    /// </summary>
    Task<IEnumerable<Booking>> GetByClientIdAsync(Guid clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les réservations d'un prestataire
    /// </summary>
    Task<IEnumerable<Booking>> GetByProviderIdAsync(Guid providerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les réservations d'un service
    /// </summary>
    Task<IEnumerable<Booking>> GetByServiceIdAsync(Guid serviceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les réservations par statut
    /// </summary>
    Task<IEnumerable<Booking>> GetByStatusAsync(BookingStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les réservations expirées
    /// </summary>
    Task<IEnumerable<Booking>> GetExpiredBookingsAsync(int limit = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les réservations à venir pour un utilisateur
    /// </summary>
    Task<IEnumerable<Booking>> GetUpcomingBookingsAsync(Guid userId, int hoursAhead = 24, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie s'il y a des conflits de réservation
    /// </summary>
    Task<bool> HasConflictAsync(Guid serviceId, DateTime startDate, DateTime endDate, Guid? excludeBookingId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les réservations avec pagination
    /// </summary>
    Task<(IEnumerable<Booking> Items, int TotalCount)> GetPagedAsync(
        int page, 
        int pageSize, 
        Guid? clientId = null, 
        Guid? providerId = null, 
        List<BookingStatus>? statusFilter = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        string sortBy = "ScheduledDate",
        string sortDirection = "desc",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les statistiques de réservation pour un utilisateur
    /// </summary>
    Task<BookingStats> GetStatsAsync(Guid userId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistiques de réservation
/// </summary>
public class BookingStats
{
    public int TotalBookings { get; set; }
    public int CompletedBookings { get; set; }
    public int CancelledBookings { get; set; }
    public int PendingBookings { get; set; }
    public long TotalRevenue { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalReviews { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal CancellationRate { get; set; }
}
