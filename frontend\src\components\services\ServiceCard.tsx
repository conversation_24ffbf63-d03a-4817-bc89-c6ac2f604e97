import React from 'react';
import { Link } from 'react-router-dom';
import { Star, MapPin, Clock, DollarSign } from 'lucide-react';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import type { Service } from '@/stores/serviceStore';

interface ServiceCardProps {
  service: Service;
  onBookNow?: (service: Service) => void;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({ service, onBookNow }) => {
  const formatPrice = (price: number, unit: string) => {
    const formatted = new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(price);
    
    switch (unit) {
      case 'Hourly':
        return `${formatted}/h`;
      case 'Fixed':
        return formatted;
      case 'PerUnit':
        return `${formatted}/unité`;
      default:
        return formatted;
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h${remainingMinutes}min` : `${hours}h`;
  };

  return (
    <Card className="service-card group hover:shadow-lg transition-all duration-300">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors">
              {service.name}
            </h3>
            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
              {service.description}
            </p>
          </div>
          {service.category && (
            <Badge variant="secondary" className="ml-2">
              {service.category.name}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="pb-3">
        {/* Provider Info */}
        {service.provider && (
          <div className="flex items-center space-x-3 mb-4">
            <Avatar className="h-8 w-8">
              <AvatarImage src={service.provider.avatar} alt={service.provider.firstName} />
              <AvatarFallback>
                {service.provider.firstName.charAt(0)}{service.provider.lastName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-foreground">
                {service.provider.firstName} {service.provider.lastName}
              </p>
              <div className="flex items-center space-x-1">
                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                <span className="text-xs text-muted-foreground">
                  {service.provider.rating.toFixed(1)} ({service.provider.reviewCount} avis)
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Service Details */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <DollarSign className="h-4 w-4" />
            <span>{formatPrice(service.basePrice, service.pricingUnit)}</span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>
              {formatDuration(service.minDurationMinutes)}
              {service.maxDurationMinutes !== service.minDurationMinutes && 
                ` - ${formatDuration(service.maxDurationMinutes)}`
              }
            </span>
          </div>

          {service.address && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <MapPin className="h-4 w-4" />
              <span className="truncate">
                {service.address.city}, {service.address.postalCode}
              </span>
            </div>
          )}
        </div>

        {/* Service Stats */}
        <div className="flex items-center justify-between mt-4 pt-3 border-t border-border">
          <div className="flex items-center space-x-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">
              {service.averageRating.toFixed(1)}
            </span>
            <span className="text-xs text-muted-foreground">
              ({service.totalBookings} réservations)
            </span>
          </div>
          
          {service.isActive ? (
            <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
              Disponible
            </Badge>
          ) : (
            <Badge variant="secondary">
              Indisponible
            </Badge>
          )}
        </div>
      </CardContent>

      <CardFooter className="pt-3">
        <div className="flex space-x-2 w-full">
          <Button variant="outline" asChild className="flex-1">
            <Link to={`/services/${service.id}`}>
              Voir détails
            </Link>
          </Button>
          
          {service.isActive && (
            <Button 
              onClick={() => onBookNow?.(service)}
              className="flex-1"
            >
              Réserver
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};
