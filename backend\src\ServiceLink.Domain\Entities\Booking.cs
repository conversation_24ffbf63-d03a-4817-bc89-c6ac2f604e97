using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Events;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Domain.Entities;

/// <summary>
/// Entité représentant une réservation de service
/// </summary>
public class Booking : BaseEntity
{
    /// <summary>
    /// ID du client qui fait la réservation
    /// </summary>
    public Guid ClientId { get; private set; }

    /// <summary>
    /// ID du prestataire de service
    /// </summary>
    public Guid ProviderId { get; private set; }

    /// <summary>
    /// ID du service réservé
    /// </summary>
    public Guid ServiceId { get; private set; }

    /// <summary>
    /// Date et heure prévues pour le service
    /// </summary>
    public DateTime ScheduledDate { get; private set; }

    /// <summary>
    /// Date et heure de fin prévues
    /// </summary>
    public DateTime? ScheduledEndDate { get; private set; }

    /// <summary>
    /// Statut actuel de la réservation
    /// </summary>
    public BookingStatus Status { get; private set; }

    /// <summary>
    /// Montant total de la réservation en centimes
    /// </summary>
    public long TotalAmount { get; private set; }

    /// <summary>
    /// Montant de la commission en centimes
    /// </summary>
    public long CommissionAmount { get; private set; }

    /// <summary>
    /// Notes du client
    /// </summary>
    public string ClientNotes { get; private set; } = string.Empty;

    /// <summary>
    /// Notes du prestataire
    /// </summary>
    public string ProviderNotes { get; private set; } = string.Empty;

    /// <summary>
    /// Adresse où le service doit être effectué
    /// </summary>
    public ServiceAddress ServiceAddress { get; private set; } = new();

    /// <summary>
    /// Durée estimée du service en minutes
    /// </summary>
    public int EstimatedDurationMinutes { get; private set; }

    /// <summary>
    /// Durée réelle du service en minutes (une fois terminé)
    /// </summary>
    public int? ActualDurationMinutes { get; private set; }

    /// <summary>
    /// Date de confirmation par le prestataire
    /// </summary>
    public DateTime? ConfirmedAt { get; private set; }

    /// <summary>
    /// Date de début réel du service
    /// </summary>
    public DateTime? StartedAt { get; private set; }

    /// <summary>
    /// Date de fin réelle du service
    /// </summary>
    public DateTime? CompletedAt { get; private set; }

    /// <summary>
    /// Date d'annulation
    /// </summary>
    public DateTime? CancelledAt { get; private set; }

    /// <summary>
    /// Raison de l'annulation
    /// </summary>
    public string? CancellationReason { get; private set; }

    /// <summary>
    /// Qui a annulé la réservation
    /// </summary>
    public Guid? CancelledBy { get; private set; }

    /// <summary>
    /// Date d'expiration de la réservation
    /// </summary>
    public DateTime ExpiresAt { get; private set; }

    /// <summary>
    /// Indique si c'est une réservation urgente
    /// </summary>
    public bool IsUrgent { get; private set; }

    /// <summary>
    /// Indique si c'est une réservation récurrente
    /// </summary>
    public bool IsRecurring { get; private set; }

    /// <summary>
    /// ID de la réservation parent pour les récurrences
    /// </summary>
    public Guid? ParentBookingId { get; private set; }

    /// <summary>
    /// Configuration de récurrence (JSON)
    /// </summary>
    public string? RecurrenceConfig { get; private set; }

    /// <summary>
    /// Métadonnées additionnelles (JSON)
    /// </summary>
    public string? Metadata { get; private set; }

    /// <summary>
    /// Devise de la transaction
    /// </summary>
    public string Currency { get; private set; } = "EUR";

    /// <summary>
    /// Navigation vers le client
    /// </summary>
    public virtual User? Client { get; set; }

    /// <summary>
    /// Navigation vers le prestataire
    /// </summary>
    public virtual User? Provider { get; set; }

    /// <summary>
    /// Navigation vers le service
    /// </summary>
    public virtual Service? Service { get; set; }

    /// <summary>
    /// Paiements associés à cette réservation
    /// </summary>
    public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

    /// <summary>
    /// Avis associé à cette réservation
    /// </summary>
    public virtual Review? Review { get; set; }

    /// <summary>
    /// Constructeur privé pour EF Core
    /// </summary>
    private Booking() { }

    /// <summary>
    /// Constructeur pour créer une nouvelle réservation
    /// </summary>
    public Booking(
        Guid clientId,
        Guid providerId,
        Guid serviceId,
        DateTime scheduledDate,
        long totalAmount,
        ServiceAddress serviceAddress,
        int estimatedDurationMinutes,
        string clientNotes = "",
        bool isUrgent = false,
        string currency = "EUR")
    {
        ClientId = clientId;
        ProviderId = providerId;
        ServiceId = serviceId;
        ScheduledDate = scheduledDate;
        ScheduledEndDate = scheduledDate.AddMinutes(estimatedDurationMinutes);
        TotalAmount = totalAmount;
        ServiceAddress = serviceAddress;
        EstimatedDurationMinutes = estimatedDurationMinutes;
        ClientNotes = clientNotes;
        IsUrgent = isUrgent;
        Currency = currency;
        Status = BookingStatus.Pending;
        ExpiresAt = isUrgent ? DateTime.UtcNow.AddHours(2) : DateTime.UtcNow.AddDays(1);

        // Événement de domaine
        AddDomainEvent(new BookingCreatedEvent(Id, ClientId, ProviderId, ServiceId, ScheduledDate, TotalAmount));
    }

    /// <summary>
    /// Confirme la réservation
    /// </summary>
    public void ConfirmBooking(string? providerNotes = null)
    {
        if (Status != BookingStatus.Pending)
            throw new InvalidOperationException($"Cannot confirm booking with status {Status}");

        if (DateTime.UtcNow > ExpiresAt)
            throw new InvalidOperationException("Cannot confirm expired booking");

        Status = BookingStatus.Confirmed;
        ConfirmedAt = DateTime.UtcNow;
        ProviderNotes = providerNotes ?? string.Empty;

        AddDomainEvent(new BookingConfirmedEvent(Id, ClientId, ProviderId, ConfirmedAt.Value));
    }

    /// <summary>
    /// Rejette la réservation
    /// </summary>
    public void RejectBooking(string reason)
    {
        if (Status != BookingStatus.Pending)
            throw new InvalidOperationException($"Cannot reject booking with status {Status}");

        Status = BookingStatus.Rejected;
        CancelledAt = DateTime.UtcNow;
        CancellationReason = reason;
        CancelledBy = ProviderId;

        AddDomainEvent(new BookingRejectedEvent(Id, ClientId, ProviderId, reason));
    }

    /// <summary>
    /// Démarre le service
    /// </summary>
    public void StartService()
    {
        if (Status != BookingStatus.Confirmed)
            throw new InvalidOperationException($"Cannot start service with status {Status}");

        Status = BookingStatus.InProgress;
        StartedAt = DateTime.UtcNow;

        AddDomainEvent(new BookingStartedEvent(Id, ClientId, ProviderId, StartedAt.Value));
    }

    /// <summary>
    /// Termine le service
    /// </summary>
    public void CompleteService(int? actualDurationMinutes = null)
    {
        if (Status != BookingStatus.InProgress)
            throw new InvalidOperationException($"Cannot complete service with status {Status}");

        Status = BookingStatus.Completed;
        CompletedAt = DateTime.UtcNow;
        
        if (actualDurationMinutes.HasValue)
        {
            ActualDurationMinutes = actualDurationMinutes.Value;
        }
        else if (StartedAt.HasValue)
        {
            ActualDurationMinutes = (int)(CompletedAt.Value - StartedAt.Value).TotalMinutes;
        }

        AddDomainEvent(new BookingCompletedEvent(Id, ClientId, ProviderId, CompletedAt.Value, ActualDurationMinutes));
    }

    /// <summary>
    /// Annule la réservation
    /// </summary>
    public void CancelBooking(string reason, Guid cancelledBy)
    {
        if (Status == BookingStatus.Completed || Status == BookingStatus.Cancelled)
            throw new InvalidOperationException($"Cannot cancel booking with status {Status}");

        Status = BookingStatus.Cancelled;
        CancelledAt = DateTime.UtcNow;
        CancellationReason = reason;
        CancelledBy = cancelledBy;

        AddDomainEvent(new BookingCancelledEvent(Id, ClientId, ProviderId, reason, cancelledBy));
    }

    /// <summary>
    /// Marque la réservation comme expirée
    /// </summary>
    public void MarkAsExpired()
    {
        if (Status != BookingStatus.Pending)
            return;

        Status = BookingStatus.Expired;
        AddDomainEvent(new BookingExpiredEvent(Id, ClientId, ProviderId));
    }

    /// <summary>
    /// Calcule la commission
    /// </summary>
    public void CalculateCommission(decimal commissionRate)
    {
        CommissionAmount = (long)(TotalAmount * commissionRate / 100);
    }

    /// <summary>
    /// Met à jour les notes du prestataire
    /// </summary>
    public void UpdateProviderNotes(string notes)
    {
        ProviderNotes = notes;
    }

    /// <summary>
    /// Vérifie si la réservation peut être annulée
    /// </summary>
    public bool CanBeCancelled()
    {
        return Status == BookingStatus.Pending || Status == BookingStatus.Confirmed;
    }

    /// <summary>
    /// Vérifie si la réservation peut être modifiée
    /// </summary>
    public bool CanBeModified()
    {
        return Status == BookingStatus.Pending && DateTime.UtcNow < ScheduledDate.AddHours(-2);
    }

    /// <summary>
    /// Obtient le temps restant avant expiration
    /// </summary>
    public TimeSpan? GetTimeUntilExpiration()
    {
        if (Status != BookingStatus.Pending)
            return null;

        var remaining = ExpiresAt - DateTime.UtcNow;
        return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
    }
}
