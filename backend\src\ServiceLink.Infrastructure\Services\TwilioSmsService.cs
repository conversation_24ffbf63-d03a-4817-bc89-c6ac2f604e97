using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using System.Text.RegularExpressions;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Service d'envoi de SMS via Twilio
/// </summary>
public class TwilioSmsService : IExternalNotificationService
{
    private readonly ILogger<TwilioSmsService> _logger;
    private readonly TwilioSettings _settings;
    private readonly INotificationTemplateService _templateService;

    public TwilioSmsService(
        ILogger<TwilioSmsService> logger,
        IConfiguration configuration,
        INotificationTemplateService templateService)
    {
        _logger = logger;
        _settings = configuration.GetSection("Twilio").Get<TwilioSettings>() ?? new TwilioSettings();
        _templateService = templateService;

        // Initialisation du client Twilio
        if (!string.IsNullOrEmpty(_settings.AccountSid) && !string.IsNullOrEmpty(_settings.AuthToken))
        {
            TwilioClient.Init(_settings.AccountSid, _settings.AuthToken);
        }
    }

    /// <inheritdoc />
    public ExternalNotificationType ServiceType => ExternalNotificationType.SMS;

    /// <inheritdoc />
    public async Task<ExternalNotificationResult> SendNotificationAsync(
        ExternalNotificationRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Envoi de SMS via Twilio vers {Recipients}", string.Join(", ", request.Recipients));

            // Validation des destinataires
            var validRecipients = request.Recipients.Where(ValidateDestination).ToList();
            if (!validRecipients.Any())
            {
                return new ExternalNotificationResult
                {
                    Success = false,
                    Type = ServiceType,
                    Status = ExternalNotificationStatus.Failed,
                    ErrorMessage = "Aucun numéro de téléphone valide",
                    FailedRecipients = request.Recipients
                };
            }

            // Préparation du contenu
            var content = request.Content;

            // Utilisation d'un template si spécifié
            if (!string.IsNullOrEmpty(request.TemplateName))
            {
                var template = await _templateService.GetTemplateAsync(request.TemplateName, request.Language);
                if (template != null)
                {
                    content = _templateService.RenderTemplate(template, request.TemplateData);
                }
            }

            // Limitation de la taille du SMS (160 caractères pour un SMS simple)
            if (content.Length > _settings.MaxSmsLength)
            {
                content = content.Substring(0, _settings.MaxSmsLength - 3) + "...";
                _logger.LogWarning("Contenu SMS tronqué à {MaxLength} caractères", _settings.MaxSmsLength);
            }

            var successfulRecipients = new List<string>();
            var failedRecipients = new List<string>();
            var results = new List<MessageResource>();

            // Envoi à chaque destinataire
            foreach (var recipient in validRecipients)
            {
                try
                {
                    var normalizedNumber = NormalizePhoneNumber(recipient);
                    
                    var message = await MessageResource.CreateAsync(
                        body: content,
                        from: new PhoneNumber(_settings.FromPhoneNumber),
                        to: new PhoneNumber(normalizedNumber)
                    );

                    results.Add(message);
                    successfulRecipients.Add(recipient);

                    _logger.LogDebug("SMS envoyé avec succès à {Recipient}. SID: {MessageSid}", 
                        recipient, message.Sid);

                    // Pause entre les envois pour respecter les limites de taux
                    if (validRecipients.Count > 1)
                    {
                        await Task.Delay(_settings.DelayBetweenSms, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de l'envoi SMS à {Recipient}", recipient);
                    failedRecipients.Add(recipient);
                }
            }

            if (successfulRecipients.Any())
            {
                var firstResult = results.First();
                
                return new ExternalNotificationResult
                {
                    Success = true,
                    NotificationId = firstResult.Sid,
                    Type = ServiceType,
                    Status = MapTwilioStatus(firstResult.Status),
                    SentAt = DateTime.UtcNow,
                    SuccessfulRecipients = successfulRecipients,
                    FailedRecipients = failedRecipients,
                    Cost = CalculateTotalCost(results),
                    ProviderData = new Dictionary<string, object>
                    {
                        ["MessageSids"] = results.Select(r => r.Sid).ToList(),
                        ["TotalMessages"] = results.Count,
                        ["Direction"] = firstResult.Direction?.ToString() ?? "outbound"
                    }
                };
            }
            else
            {
                return new ExternalNotificationResult
                {
                    Success = false,
                    Type = ServiceType,
                    Status = ExternalNotificationStatus.Failed,
                    ErrorMessage = "Échec d'envoi à tous les destinataires",
                    FailedRecipients = failedRecipients
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de SMS via Twilio");
            
            return new ExternalNotificationResult
            {
                Success = false,
                Type = ServiceType,
                Status = ExternalNotificationStatus.Failed,
                ErrorMessage = ex.Message,
                FailedRecipients = request.Recipients
            };
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ExternalNotificationResult>> SendBulkNotificationsAsync(
        IEnumerable<ExternalNotificationRequest> requests, 
        CancellationToken cancellationToken = default)
    {
        var results = new List<ExternalNotificationResult>();
        var requestList = requests.ToList();

        _logger.LogInformation("Envoi en lot de {Count} SMS via Twilio", requestList.Count);

        foreach (var request in requestList)
        {
            var result = await SendNotificationAsync(request, cancellationToken);
            results.Add(result);

            // Pause entre les lots pour respecter les limites de taux
            if (requestList.Count > 5)
            {
                await Task.Delay(_settings.DelayBetweenBulk, cancellationToken);
            }
        }

        return results;
    }

    /// <inheritdoc />
    public async Task<ExternalNotificationStatus> GetNotificationStatusAsync(
        string notificationId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Vérification du statut du SMS {NotificationId} via Twilio", notificationId);

            var message = await MessageResource.FetchAsync(notificationId);
            return MapTwilioStatus(message.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification du statut SMS {NotificationId}", notificationId);
            return ExternalNotificationStatus.Failed;
        }
    }

    /// <inheritdoc />
    public bool ValidateDestination(string destination)
    {
        if (string.IsNullOrWhiteSpace(destination))
            return false;

        try
        {
            // Validation basique du numéro de téléphone
            var phoneRegex = new Regex(@"^\+?[1-9]\d{1,14}$");
            var cleanNumber = Regex.Replace(destination, @"[\s\-\(\)]", "");
            
            return phoneRegex.IsMatch(cleanNumber) && cleanNumber.Length >= 10;
        }
        catch
        {
            return false;
        }
    }

    #region Méthodes privées

    /// <summary>
    /// Normalise un numéro de téléphone au format international
    /// </summary>
    /// <param name="phoneNumber">Numéro à normaliser</param>
    /// <returns>Numéro normalisé</returns>
    private string NormalizePhoneNumber(string phoneNumber)
    {
        // Suppression des espaces, tirets et parenthèses
        var cleaned = Regex.Replace(phoneNumber, @"[\s\-\(\)]", "");
        
        // Ajout du préfixe international si manquant
        if (!cleaned.StartsWith("+"))
        {
            // Logique de détection du pays basée sur la longueur et les préfixes
            if (cleaned.StartsWith("0") && cleaned.Length == 10)
            {
                // Numéro français
                cleaned = "+33" + cleaned.Substring(1);
            }
            else if (cleaned.Length == 10 && !cleaned.StartsWith("1"))
            {
                // Numéro US/Canada
                cleaned = "+1" + cleaned;
            }
            else if (!cleaned.StartsWith("+"))
            {
                cleaned = "+" + cleaned;
            }
        }

        return cleaned;
    }

    /// <summary>
    /// Mappe le statut Twilio vers notre énumération
    /// </summary>
    /// <param name="twilioStatus">Statut Twilio</param>
    /// <returns>Statut mappé</returns>
    private static ExternalNotificationStatus MapTwilioStatus(MessageResource.StatusEnum? twilioStatus)
    {
        if (twilioStatus == null)
            return ExternalNotificationStatus.Pending;

        if (twilioStatus == MessageResource.StatusEnum.Queued)
            return ExternalNotificationStatus.Pending;
        if (twilioStatus == MessageResource.StatusEnum.Sending)
            return ExternalNotificationStatus.Sending;
        if (twilioStatus == MessageResource.StatusEnum.Sent)
            return ExternalNotificationStatus.Sent;
        if (twilioStatus == MessageResource.StatusEnum.Delivered)
            return ExternalNotificationStatus.Delivered;
        if (twilioStatus == MessageResource.StatusEnum.Read)
            return ExternalNotificationStatus.Read;
        if (twilioStatus == MessageResource.StatusEnum.Failed)
            return ExternalNotificationStatus.Failed;
        if (twilioStatus == MessageResource.StatusEnum.Undelivered)
            return ExternalNotificationStatus.Failed;

        return ExternalNotificationStatus.Pending;
    }

    /// <summary>
    /// Calcule le coût total des messages envoyés
    /// </summary>
    /// <param name="messages">Messages Twilio</param>
    /// <returns>Coût total</returns>
    private static decimal CalculateTotalCost(IEnumerable<MessageResource> messages)
    {
        return messages.Sum(m => decimal.TryParse(m.Price, out var price) ? Math.Abs(price) : 0);
    }

    #endregion
}

/// <summary>
/// Configuration Twilio
/// </summary>
public class TwilioSettings
{
    /// <summary>
    /// SID du compte Twilio
    /// </summary>
    public string AccountSid { get; set; } = string.Empty;

    /// <summary>
    /// Token d'authentification Twilio
    /// </summary>
    public string AuthToken { get; set; } = string.Empty;

    /// <summary>
    /// Numéro de téléphone expéditeur
    /// </summary>
    public string FromPhoneNumber { get; set; } = string.Empty;

    /// <summary>
    /// Longueur maximale d'un SMS
    /// </summary>
    public int MaxSmsLength { get; set; } = 160;

    /// <summary>
    /// Délai entre les SMS individuels (ms)
    /// </summary>
    public int DelayBetweenSms { get; set; } = 100;

    /// <summary>
    /// Délai entre les lots de SMS (ms)
    /// </summary>
    public int DelayBetweenBulk { get; set; } = 1000;

    /// <summary>
    /// Activer les webhooks de statut
    /// </summary>
    public bool EnableStatusWebhooks { get; set; } = true;

    /// <summary>
    /// URL du webhook de statut
    /// </summary>
    public string StatusWebhookUrl { get; set; } = string.Empty;

    /// <summary>
    /// Limite de taux d'envoi par minute
    /// </summary>
    public int RateLimitPerMinute { get; set; } = 60;

    /// <summary>
    /// Activer la validation des numéros
    /// </summary>
    public bool EnableNumberValidation { get; set; } = true;

    /// <summary>
    /// Préfixe par défaut pour les numéros locaux
    /// </summary>
    public string DefaultCountryCode { get; set; } = "+33"; // France par défaut
}
