import { useCallback } from 'react'
import { useApi, useMutation, usePagination, useSearch } from './useApi'
import { serviceService } from '../services/serviceService'
import type { 
  Service, 
  ServiceCategory, 
  ServiceFilters,
  CreateServiceRequest,
  UpdateServiceRequest 
} from '../types/service'
import type { PaginatedResponse } from '../types/api'

// Hook pour obtenir les catégories
export function useCategories() {
  return useApi(
    () => serviceService.getCategories(),
    [],
    true
  )
}

// Hook pour obtenir une catégorie par ID
export function useCategory(id: string) {
  return useApi(
    () => serviceService.getCategoryById(id),
    [id],
    !!id
  )
}

// Hook pour rechercher des services
export function useServiceSearch(
  filters: ServiceFilters = {},
  immediate: boolean = true
) {
  const pagination = usePagination()
  
  const searchServices = useCallback(() => {
    return serviceService.searchServices(filters, {
      pageNumber: pagination.pagination.pageNumber,
      pageSize: pagination.pagination.pageSize,
    })
  }, [filters, pagination.pagination.pageNumber, pagination.pagination.pageSize])

  const { data, loading, error, success, execute, refetch } = useApi<PaginatedResponse<Service>>(
    searchServices,
    [filters, pagination.pagination.pageNumber, pagination.pagination.pageSize],
    immediate
  )

  // Mettre à jour la pagination quand les données changent
  if (data && data.totalCount !== pagination.pagination.totalCount) {
    pagination.updatePagination({
      totalCount: data.totalCount,
      totalPages: data.totalPages,
      hasPreviousPage: data.hasPreviousPage,
      hasNextPage: data.hasNextPage,
    })
  }

  return {
    services: data?.data || [],
    pagination: pagination.pagination,
    loading,
    error,
    success,
    refetch,
    setPage: pagination.setPage,
    setPageSize: pagination.setPageSize,
    nextPage: pagination.nextPage,
    previousPage: pagination.previousPage,
  }
}

// Hook pour obtenir un service par ID
export function useService(id: string) {
  return useApi(
    () => serviceService.getServiceById(id),
    [id],
    !!id
  )
}

// Hook pour obtenir les services populaires
export function usePopularServices(limit: number = 6) {
  return useApi(
    () => serviceService.getPopularServices(limit),
    [limit],
    true
  )
}

// Hook pour obtenir les services en vedette
export function useFeaturedServices(limit: number = 8) {
  return useApi(
    () => serviceService.getFeaturedServices(limit),
    [limit],
    true
  )
}

// Hook pour obtenir les services d'un prestataire
export function useProviderServices(providerId: string) {
  return useApi(
    () => serviceService.getServicesByProvider(providerId),
    [providerId],
    !!providerId
  )
}

// Hook pour créer un service
export function useCreateService() {
  return useMutation<Service, CreateServiceRequest>(
    (data) => serviceService.createService(data)
  )
}

// Hook pour mettre à jour un service
export function useUpdateService() {
  return useMutation<Service, { id: string; data: UpdateServiceRequest }>(
    ({ id, data }) => serviceService.updateService(id, data)
  )
}

// Hook pour supprimer un service
export function useDeleteService() {
  return useMutation<void, string>(
    (id) => serviceService.deleteService(id)
  )
}

// Hook pour obtenir les statistiques des services
export function useServiceStats() {
  return useApi(
    () => serviceService.getServiceStats(),
    [],
    true
  )
}

// Hook pour la recherche avec suggestions
export function useServiceSuggestions(debounceDelay: number = 300) {
  return useSearch(
    (query: string) => serviceService.getSearchSuggestions(query),
    debounceDelay
  )
}

// Hook pour obtenir les services à proximité
export function useNearbyServices(
  latitude: number,
  longitude: number,
  radius: number = 10,
  limit: number = 10
) {
  return useApi(
    () => serviceService.getNearbyServices(latitude, longitude, radius, limit),
    [latitude, longitude, radius, limit],
    !!(latitude && longitude)
  )
}

// Hook pour gérer les favoris
export function useToggleFavorite() {
  return useMutation<{ isFavorite: boolean }, string>(
    (serviceId) => serviceService.toggleFavorite(serviceId)
  )
}

// Hook pour obtenir les services favoris
export function useFavoriteServices() {
  return useApi(
    () => serviceService.getFavoriteServices(),
    [],
    true
  )
}

// Hook pour signaler un service
export function useReportService() {
  return useMutation<void, { serviceId: string; reason: string; description?: string }>(
    ({ serviceId, reason, description }) => serviceService.reportService(serviceId, reason, description)
  )
}

// Hook combiné pour la page d'accueil
export function useHomePageData() {
  const categories = useCategories()
  const popularServices = usePopularServices(6)
  const featuredServices = useFeaturedServices(8)
  const stats = useServiceStats()

  return {
    categories: categories.data || [],
    popularServices: popularServices.data || [],
    featuredServices: featuredServices.data || [],
    stats: stats.data,
    loading: categories.loading || popularServices.loading || featuredServices.loading || stats.loading,
    error: categories.error || popularServices.error || featuredServices.error || stats.error,
    refetch: () => {
      categories.refetch()
      popularServices.refetch()
      featuredServices.refetch()
      stats.refetch()
    },
  }
}
