using FluentValidation;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Application.Validators;

/// <summary>
/// Validateur pour la commande de création d'utilisateur
/// </summary>
public class CreateUserCommandValidator : AbstractValidator<RegisterCommand>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordService _passwordService;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="userRepository">Repository des utilisateurs</param>
    /// <param name="passwordService">Service de gestion des mots de passe</param>
    public CreateUserCommandValidator(IUserRepository userRepository, IPasswordService passwordService)
    {
        _userRepository = userRepository;
        _passwordService = passwordService;

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("L'adresse email est obligatoire.")
            .EmailAddress().WithMessage("L'adresse email n'est pas valide.")
            .MaximumLength(254).WithMessage("L'adresse email ne peut pas dépasser 254 caractères.")
            .MustAsync(BeUniqueEmail).WithMessage("Cette adresse email est déjà utilisée.");

        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("Le prénom est obligatoire.")
            .Length(2, 50).WithMessage("Le prénom doit contenir entre 2 et 50 caractères.")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$").WithMessage("Le prénom ne peut contenir que des lettres, espaces, tirets et apostrophes.");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Le nom de famille est obligatoire.")
            .Length(2, 50).WithMessage("Le nom de famille doit contenir entre 2 et 50 caractères.")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$").WithMessage("Le nom de famille ne peut contenir que des lettres, espaces, tirets et apostrophes.");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Le mot de passe est obligatoire.")
            .Must(BeStrongPassword).WithMessage("Le mot de passe ne respecte pas les critères de sécurité.");

        RuleFor(x => x.PhoneNumber)
            .Must(BeValidPhoneNumber).WithMessage("Le numéro de téléphone n'est pas valide.")
            .When(x => !string.IsNullOrEmpty(x.PhoneNumber))
            .MustAsync(BeUniquePhoneNumber).WithMessage("Ce numéro de téléphone est déjà utilisé.")
            .When(x => !string.IsNullOrEmpty(x.PhoneNumber));

        RuleFor(x => x.Role)
            .IsInEnum().WithMessage("Le rôle spécifié n'est pas valide.");

        RuleFor(x => x.Language)
            .NotEmpty().WithMessage("La langue est obligatoire.")
            .Matches(@"^[a-z]{2}-[A-Z]{2}$").WithMessage("Le format de langue doit être xx-XX (ex: fr-FR).");

        RuleFor(x => x.TimeZone)
            .NotEmpty().WithMessage("Le fuseau horaire est obligatoire.")
            .Must(BeValidTimeZone).WithMessage("Le fuseau horaire spécifié n'est pas valide.");
    }

    /// <summary>
    /// Vérifie l'unicité de l'email
    /// </summary>
    private async Task<bool> BeUniqueEmail(string email, CancellationToken cancellationToken)
    {
        if (!Email.TryCreate(email, out var emailVO))
            return false;

        return !await _userRepository.EmailExistsAsync(emailVO, cancellationToken: cancellationToken);
    }

    /// <summary>
    /// Vérifie l'unicité du numéro de téléphone
    /// </summary>
    private async Task<bool> BeUniquePhoneNumber(string phoneNumber, CancellationToken cancellationToken)
    {
        if (!PhoneNumber.TryCreate(phoneNumber, out var phoneVO))
            return false;

        return !await _userRepository.PhoneNumberExistsAsync(phoneVO, cancellationToken: cancellationToken);
    }

    /// <summary>
    /// Vérifie la validité du numéro de téléphone
    /// </summary>
    private static bool BeValidPhoneNumber(string? phoneNumber)
    {
        if (string.IsNullOrEmpty(phoneNumber))
            return true;

        return PhoneNumber.TryCreate(phoneNumber, out _);
    }

    /// <summary>
    /// Vérifie la force du mot de passe
    /// </summary>
    private bool BeStrongPassword(string password)
    {
        var strength = _passwordService.ValidatePasswordStrength(password);
        return strength >= PasswordStrength.Medium;
    }

    /// <summary>
    /// Vérifie la validité du fuseau horaire
    /// </summary>
    private static bool BeValidTimeZone(string timeZone)
    {
        try
        {
            TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Validateur pour la commande de mise à jour d'utilisateur
/// </summary>
public class UpdateUserCommandValidator : AbstractValidator<UpdateUserCommand>
{
    private readonly IUserRepository _userRepository;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="userRepository">Repository des utilisateurs</param>
    public UpdateUserCommandValidator(IUserRepository userRepository)
    {
        _userRepository = userRepository;

        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("L'identifiant utilisateur est obligatoire.");

        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("Le prénom est obligatoire.")
            .Length(2, 50).WithMessage("Le prénom doit contenir entre 2 et 50 caractères.")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$").WithMessage("Le prénom ne peut contenir que des lettres, espaces, tirets et apostrophes.");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Le nom de famille est obligatoire.")
            .Length(2, 50).WithMessage("Le nom de famille doit contenir entre 2 et 50 caractères.")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$").WithMessage("Le nom de famille ne peut contenir que des lettres, espaces, tirets et apostrophes.");

        RuleFor(x => x.PhoneNumber)
            .Must(BeValidPhoneNumber).WithMessage("Le numéro de téléphone n'est pas valide.")
            .When(x => !string.IsNullOrEmpty(x.PhoneNumber))
            .MustAsync(BeUniquePhoneNumberForUpdate).WithMessage("Ce numéro de téléphone est déjà utilisé.")
            .When(x => !string.IsNullOrEmpty(x.PhoneNumber));

        RuleFor(x => x.Language)
            .NotEmpty().WithMessage("La langue est obligatoire.")
            .Matches(@"^[a-z]{2}-[A-Z]{2}$").WithMessage("Le format de langue doit être xx-XX (ex: fr-FR).");

        RuleFor(x => x.TimeZone)
            .NotEmpty().WithMessage("Le fuseau horaire est obligatoire.")
            .Must(BeValidTimeZone).WithMessage("Le fuseau horaire spécifié n'est pas valide.");

        RuleFor(x => x.AvatarUrl)
            .Must(BeValidUrl).WithMessage("L'URL de l'avatar n'est pas valide.")
            .When(x => !string.IsNullOrEmpty(x.AvatarUrl));
    }

    /// <summary>
    /// Vérifie l'unicité du numéro de téléphone pour une mise à jour
    /// </summary>
    private async Task<bool> BeUniquePhoneNumberForUpdate(UpdateUserCommand command, string phoneNumber, CancellationToken cancellationToken)
    {
        if (!PhoneNumber.TryCreate(phoneNumber, out var phoneVO))
            return false;

        return !await _userRepository.PhoneNumberExistsAsync(phoneVO, command.UserId, cancellationToken);
    }

    /// <summary>
    /// Vérifie la validité du numéro de téléphone
    /// </summary>
    private static bool BeValidPhoneNumber(string? phoneNumber)
    {
        if (string.IsNullOrEmpty(phoneNumber))
            return true;

        return PhoneNumber.TryCreate(phoneNumber, out _);
    }

    /// <summary>
    /// Vérifie la validité du fuseau horaire
    /// </summary>
    private static bool BeValidTimeZone(string timeZone)
    {
        try
        {
            TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Vérifie la validité d'une URL
    /// </summary>
    private static bool BeValidUrl(string? url)
    {
        if (string.IsNullOrEmpty(url))
            return true;

        return Uri.TryCreate(url, UriKind.Absolute, out var result) && 
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
}

/// <summary>
/// Validateur pour la commande de changement de mot de passe
/// </summary>
public class ChangePasswordCommandValidator : AbstractValidator<ChangePasswordCommand>
{
    private readonly IPasswordService _passwordService;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="passwordService">Service de gestion des mots de passe</param>
    public ChangePasswordCommandValidator(IPasswordService passwordService)
    {
        _passwordService = passwordService;

        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("L'identifiant utilisateur est obligatoire.");

        RuleFor(x => x.CurrentPassword)
            .NotEmpty().WithMessage("Le mot de passe actuel est obligatoire.");

        RuleFor(x => x.NewPassword)
            .NotEmpty().WithMessage("Le nouveau mot de passe est obligatoire.")
            .Must(BeStrongPassword).WithMessage("Le nouveau mot de passe ne respecte pas les critères de sécurité.")
            .NotEqual(x => x.CurrentPassword).WithMessage("Le nouveau mot de passe doit être différent de l'actuel.");
    }

    /// <summary>
    /// Vérifie la force du mot de passe
    /// </summary>
    private bool BeStrongPassword(string password)
    {
        var strength = _passwordService.ValidatePasswordStrength(password);
        return strength >= PasswordStrength.Medium;
    }
}

/// <summary>
/// Validateur pour la commande de confirmation d'email
/// </summary>
public class ConfirmEmailCommandValidator : AbstractValidator<ConfirmEmailCommand>
{
    /// <summary>
    /// Constructeur
    /// </summary>
    public ConfirmEmailCommandValidator()
    {
        RuleFor(x => x.Token)
            .NotEmpty().WithMessage("Le token de confirmation est obligatoire.")
            .MinimumLength(32).WithMessage("Le token de confirmation n'est pas valide.");
    }
}

/// <summary>
/// Validateur pour la commande de réinitialisation de mot de passe
/// </summary>
public class ResetPasswordCommandValidator : AbstractValidator<ResetPasswordCommand>
{
    private readonly IPasswordService _passwordService;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="passwordService">Service de gestion des mots de passe</param>
    public ResetPasswordCommandValidator(IPasswordService passwordService)
    {
        _passwordService = passwordService;

        RuleFor(x => x.Token)
            .NotEmpty().WithMessage("Le token de réinitialisation est obligatoire.")
            .MinimumLength(32).WithMessage("Le token de réinitialisation n'est pas valide.");

        RuleFor(x => x.NewPassword)
            .NotEmpty().WithMessage("Le nouveau mot de passe est obligatoire.")
            .Must(BeStrongPassword).WithMessage("Le nouveau mot de passe ne respecte pas les critères de sécurité.");
    }

    /// <summary>
    /// Vérifie la force du mot de passe
    /// </summary>
    private bool BeStrongPassword(string password)
    {
        var strength = _passwordService.ValidatePasswordStrength(password);
        return strength >= PasswordStrength.Medium;
    }
}
