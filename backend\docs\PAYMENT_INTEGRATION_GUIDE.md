# Guide d'Intégration des Paiements - ServiceLink

## Vue d'ensemble

ServiceLink intègre trois providers de paiement majeurs pour offrir une couverture mondiale complète :

- **Stripe** : Paiements internationaux (cartes, Apple Pay, Google Pay)
- **PayPal** : Alternative populaire mondiale
- **Flutterwave** : Spécialisé pour les marchés africains (Mobile Money, cartes locales)

## Architecture

### Factory Pattern
Le système utilise un pattern Factory pour sélectionner automatiquement le provider optimal selon :
- La devise de transaction
- La méthode de paiement préférée
- La disponibilité du provider

### Interface Unifiée
Tous les providers implémentent `IPaymentService` pour une API cohérente.

## Configuration

### appsettings.json

```json
{
  "Payment": {
    "DefaultProvider": "Stripe",
    "EnabledProviders": ["Stripe", "PayPal", "Flutterwave"],
    "DefaultCurrency": "EUR",
    "SupportedCurrencies": ["EUR", "USD", "XOF", "NGN", "GHS"],
    "WebhookTimeout": 30,
    "MaxRefundDays": 30
  },
  "Stripe": {
    "PublishableKey": "pk_test_...",
    "SecretKey": "sk_test_...",
    "WebhookSecret": "whsec_...",
    "ReturnUrl": "http://localhost:3000/payment/return",
    "TestMode": true
  },
  "PayPal": {
    "ClientId": "your_paypal_client_id",
    "ClientSecret": "your_paypal_client_secret",
    "Environment": "sandbox",
    "ReturnUrl": "http://localhost:3000/payment/paypal/return",
    "CancelUrl": "http://localhost:3000/payment/paypal/cancel"
  },
  "Flutterwave": {
    "PublicKey": "FLWPUBK_TEST-...",
    "SecretKey": "FLWSECK_TEST-...",
    "EncryptionKey": "FLWSECK_TEST...",
    "Environment": "sandbox",
    "WebhookHash": "your_webhook_hash"
  }
}
```

## API Endpoints

### 1. Créer une Intention de Paiement

**POST** `/api/payment/intent`

```json
{
  "amount": 1500,
  "currency": "EUR",
  "bookingId": "123e4567-e89b-12d3-a456-426614174000",
  "description": "Réservation service de nettoyage",
  "allowedPaymentMethods": ["Card", "ApplePay"],
  "successUrl": "https://myapp.com/success",
  "cancelUrl": "https://myapp.com/cancel",
  "metadata": {
    "customField": "value"
  }
}
```

**Réponse :**
```json
{
  "success": true,
  "data": {
    "paymentIntentId": "pi_1234567890",
    "clientSecret": "pi_1234567890_secret_xyz",
    "status": "Pending",
    "amount": 1500,
    "currency": "EUR",
    "redirectUrl": "https://checkout.stripe.com/...",
    "providerData": {
      "stripe_payment_intent_id": "pi_1234567890"
    }
  },
  "provider": "Stripe"
}
```

### 2. Confirmer un Paiement

**POST** `/api/payment/confirm`

```json
{
  "paymentIntentId": "pi_1234567890",
  "paymentMethodId": "pm_1234567890",
  "provider": "Stripe"
}
```

### 3. Vérifier le Statut d'un Paiement

**GET** `/api/payment/{paymentId}/status?provider=Stripe`

**Réponse :**
```json
{
  "success": true,
  "data": {
    "paymentId": "pi_1234567890",
    "status": "Completed",
    "amount": 1500,
    "currency": "EUR",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:35:00Z",
    "metadata": {
      "booking_id": "123e4567-e89b-12d3-a456-426614174000"
    }
  }
}
```

### 4. Effectuer un Remboursement

**POST** `/api/payment/refund` (Admin/Manager uniquement)

```json
{
  "paymentId": "pi_1234567890",
  "provider": "Stripe",
  "amount": 750,
  "reason": "Annulation client",
  "metadata": {
    "refundReason": "customer_request"
  }
}
```

### 5. Historique des Transactions

**GET** `/api/payment/transactions?limit=50`

### 6. Providers Disponibles

**GET** `/api/payment/providers`

### 7. Méthodes Supportées par Provider

**GET** `/api/payment/providers/{provider}/methods`

## Webhooks

### Configuration des Endpoints

- **Stripe** : `POST /api/webhooks/stripe`
- **PayPal** : `POST /api/webhooks/paypal`
- **Flutterwave** : `POST /api/webhooks/flutterwave`

### Validation des Signatures

Chaque webhook valide automatiquement la signature pour garantir l'authenticité.

### Test des Webhooks

**POST** `/api/webhooks/test/{provider}`

## Sélection Automatique du Provider

### Par Devise
- **EUR, USD, GBP** → Stripe
- **XOF, NGN, GHS, KES** → Flutterwave
- **Autres** → Provider par défaut

### Par Méthode de Paiement
- **Mobile Money** → Flutterwave
- **PayPal** → PayPal
- **Apple Pay, Google Pay** → Stripe
- **Carte** → Provider par défaut

## Gestion des Erreurs

### Codes d'Erreur Communs

| Code | Description | Action |
|------|-------------|---------|
| 400 | Requête invalide | Vérifier les paramètres |
| 401 | Non authentifié | Se connecter |
| 403 | Accès refusé | Vérifier les permissions |
| 404 | Paiement non trouvé | Vérifier l'ID |
| 500 | Erreur serveur | Réessayer plus tard |

### Gestion des Échecs de Paiement

```json
{
  "success": false,
  "data": {
    "success": false,
    "transactionId": "pi_1234567890",
    "status": "Failed",
    "errorMessage": "Carte refusée",
    "errorCode": "card_declined"
  }
}
```

## Sécurité

### Audit Trail
Toutes les opérations sont auditées avec :
- ID utilisateur
- Adresse IP
- User Agent
- Timestamp
- Détails de l'opération

### Chiffrement
Les données sensibles sont chiffrées avant stockage.

### Rate Limiting
Protection contre les abus avec limitation du nombre de requêtes.

### Validation des Webhooks
Validation cryptographique des signatures pour tous les providers.

## Tests

### Tests Unitaires
```bash
dotnet test --filter "Category=Payment"
```

### Tests d'Intégration
```bash
dotnet test --filter "Category=PaymentIntegration"
```

### Test des Webhooks
```bash
curl -X POST http://localhost:5000/api/webhooks/test/stripe
```

## Monitoring

### Health Checks
- **Endpoint** : `/health`
- **Tags** : `payment`, `external`

### Métriques
- Nombre de transactions par provider
- Taux de succès/échec
- Temps de réponse moyen
- Volume par devise

## Déploiement

### Variables d'Environnement

```bash
# Stripe
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# PayPal
PAYPAL_CLIENT_ID=...
PAYPAL_CLIENT_SECRET=...
PAYPAL_ENVIRONMENT=production

# Flutterwave
FLUTTERWAVE_SECRET_KEY=FLWSECK-...
FLUTTERWAVE_PUBLIC_KEY=FLWPUBK-...
FLUTTERWAVE_WEBHOOK_HASH=...
```

### Configuration Production

1. Activer HTTPS uniquement
2. Configurer les webhooks avec les bonnes URLs
3. Utiliser des clés de production
4. Activer le monitoring
5. Configurer les alertes

## Exemples d'Intégration Frontend

### React/TypeScript

```typescript
// Créer une intention de paiement
const createPaymentIntent = async (bookingData: BookingData) => {
  const response = await fetch('/api/payment/intent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      amount: bookingData.amount * 100, // Convertir en centimes
      currency: 'EUR',
      bookingId: bookingData.id,
      description: bookingData.description,
      allowedPaymentMethods: ['Card', 'ApplePay']
    })
  });
  
  return response.json();
};

// Confirmer le paiement
const confirmPayment = async (paymentIntentId: string, paymentMethodId: string) => {
  const response = await fetch('/api/payment/confirm', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      paymentIntentId,
      paymentMethodId,
      provider: 'Stripe'
    })
  });
  
  return response.json();
};
```

## Support et Dépannage

### Logs
Les logs détaillés sont disponibles avec le niveau `Information` pour les opérations normales et `Error` pour les problèmes.

### Debug
Activer `EnableDetailedAudit` dans la configuration pour un audit complet.

### Contact
Pour le support technique, consulter la documentation des providers :
- [Stripe Documentation](https://stripe.com/docs)
- [PayPal Developer](https://developer.paypal.com)
- [Flutterwave Documentation](https://developer.flutterwave.com)
