using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using System.Security.Claims;

namespace ServiceLink.Infrastructure.Hubs;

/// <summary>
/// Hub de base avec fonctionnalités communes pour tous les hubs SignalR
/// </summary>
[Authorize]
public abstract class BaseHub : Hub
{
    protected readonly ILogger<BaseHub> _logger;
    protected readonly ICacheService _cacheService;

    protected BaseHub(ILogger<BaseHub> logger, ICacheService cacheService)
    {
        _logger = logger;
        _cacheService = cacheService;
    }

    /// <summary>
    /// Obtient l'ID de l'utilisateur connecté
    /// </summary>
    protected Guid GetUserId()
    {
        var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Utilisateur non authentifié ou ID invalide");
        }
        return userId;
    }

    /// <summary>
    /// Obtient le rôle de l'utilisateur connecté
    /// </summary>
    protected UserRole GetUserRole()
    {
        var roleClaim = Context.User?.FindFirst(ClaimTypes.Role)?.Value;
        if (string.IsNullOrEmpty(roleClaim) || !Enum.TryParse<UserRole>(roleClaim, out var role))
        {
            throw new UnauthorizedAccessException("Rôle utilisateur non trouvé ou invalide");
        }
        return role;
    }

    /// <summary>
    /// Obtient le nom de l'utilisateur connecté
    /// </summary>
    protected string GetUserName()
    {
        return Context.User?.FindFirst(ClaimTypes.Name)?.Value ?? "Utilisateur inconnu";
    }

    /// <summary>
    /// Obtient l'email de l'utilisateur connecté
    /// </summary>
    protected string GetUserEmail()
    {
        return Context.User?.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty;
    }

    /// <summary>
    /// Vérifie si l'utilisateur a un rôle spécifique
    /// </summary>
    protected bool HasRole(UserRole role)
    {
        try
        {
            return GetUserRole() == role;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Vérifie si l'utilisateur a l'un des rôles spécifiés
    /// </summary>
    protected bool HasAnyRole(params UserRole[] roles)
    {
        try
        {
            var userRole = GetUserRole();
            return roles.Contains(userRole);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Génère une clé de cache pour les connexions utilisateur
    /// </summary>
    protected string GetUserConnectionCacheKey(Guid userId)
    {
        return $"signalr:connections:user:{userId}";
    }

    /// <summary>
    /// Génère une clé de cache pour les groupes
    /// </summary>
    protected string GetGroupCacheKey(string groupName)
    {
        return $"signalr:groups:{groupName}";
    }

    /// <summary>
    /// Enregistre la connexion de l'utilisateur
    /// </summary>
    protected async Task RegisterUserConnectionAsync()
    {
        try
        {
            var userId = GetUserId();
            var connectionId = Context.ConnectionId;
            var cacheKey = GetUserConnectionCacheKey(userId);

            // Stocker la connexion en cache avec TTL de 1 heure
            var connectionInfo = new
            {
                ConnectionId = connectionId,
                UserId = userId,
                UserName = GetUserName(),
                Role = GetUserRole().ToString(),
                ConnectedAt = DateTime.UtcNow,
                HubName = GetType().Name
            };

            await _cacheService.SetAsync(cacheKey, connectionInfo, TimeSpan.FromHours(1));

            _logger.LogInformation("Utilisateur {UserId} connecté au hub {HubName} avec la connexion {ConnectionId}",
                userId, GetType().Name, connectionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement de la connexion utilisateur");
        }
    }

    /// <summary>
    /// Supprime l'enregistrement de connexion de l'utilisateur
    /// </summary>
    protected async Task UnregisterUserConnectionAsync()
    {
        try
        {
            var userId = GetUserId();
            var connectionId = Context.ConnectionId;
            var cacheKey = GetUserConnectionCacheKey(userId);

            await _cacheService.RemoveAsync(cacheKey);

            _logger.LogInformation("Utilisateur {UserId} déconnecté du hub {HubName} (connexion {ConnectionId})",
                userId, GetType().Name, connectionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'enregistrement de connexion");
        }
    }

    /// <summary>
    /// Gère la connexion d'un utilisateur
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        await RegisterUserConnectionAsync();
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Gère la déconnexion d'un utilisateur
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        await UnregisterUserConnectionAsync();

        if (exception != null)
        {
            _logger.LogError(exception, "Déconnexion avec erreur pour la connexion {ConnectionId}", Context.ConnectionId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Envoie un message d'erreur au client
    /// </summary>
    protected async Task SendErrorAsync(string method, string message, object? data = null)
    {
        await Clients.Caller.SendAsync("Error", new
        {
            Method = method,
            Message = message,
            Data = data,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// Envoie un message de succès au client
    /// </summary>
    protected async Task SendSuccessAsync(string method, string message, object? data = null)
    {
        await Clients.Caller.SendAsync("Success", new
        {
            Method = method,
            Message = message,
            Data = data,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// Valide les paramètres d'entrée
    /// </summary>
    protected void ValidateInput(object? input, string parameterName)
    {
        if (input == null)
        {
            throw new ArgumentNullException(parameterName, $"Le paramètre {parameterName} ne peut pas être null");
        }

        if (input is string str && string.IsNullOrWhiteSpace(str))
        {
            throw new ArgumentException($"Le paramètre {parameterName} ne peut pas être vide", parameterName);
        }

        if (input is Guid guid && guid == Guid.Empty)
        {
            throw new ArgumentException($"Le paramètre {parameterName} ne peut pas être un GUID vide", parameterName);
        }
    }

    /// <summary>
    /// Exécute une action avec gestion d'erreur
    /// </summary>
    protected async Task ExecuteWithErrorHandlingAsync(string methodName, Func<Task> action)
    {
        try
        {
            await action();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Accès non autorisé dans {MethodName} pour l'utilisateur {ConnectionId}", 
                methodName, Context.ConnectionId);
            await SendErrorAsync(methodName, "Accès non autorisé");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Paramètres invalides dans {MethodName} pour l'utilisateur {ConnectionId}", 
                methodName, Context.ConnectionId);
            await SendErrorAsync(methodName, ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur dans {MethodName} pour l'utilisateur {ConnectionId}", 
                methodName, Context.ConnectionId);
            await SendErrorAsync(methodName, "Une erreur interne s'est produite");
        }
    }
}

/// <summary>
/// Constantes pour les groupes SignalR
/// </summary>
public static class SignalRGroups
{
    /// <summary>
    /// Groupe pour tous les administrateurs
    /// </summary>
    public const string Administrators = "Administrators";

    /// <summary>
    /// Groupe pour tous les managers
    /// </summary>
    public const string Managers = "Managers";

    /// <summary>
    /// Groupe pour le support
    /// </summary>
    public const string Support = "Support";

    /// <summary>
    /// Groupe pour les superviseurs
    /// </summary>
    public const string Supervisors = "Supervisors";

    /// <summary>
    /// Groupe pour tous les clients
    /// </summary>
    public const string Clients = "Clients";

    /// <summary>
    /// Groupe pour tous les prestataires
    /// </summary>
    public const string Providers = "Providers";

    /// <summary>
    /// Préfixe pour les groupes de conversation
    /// </summary>
    public const string ConversationPrefix = "Conversation_";

    /// <summary>
    /// Préfixe pour les groupes de réservation
    /// </summary>
    public const string BookingPrefix = "Booking_";

    /// <summary>
    /// Génère un nom de groupe pour une conversation
    /// </summary>
    public static string GetConversationGroup(Guid conversationId) => $"{ConversationPrefix}{conversationId}";

    /// <summary>
    /// Génère un nom de groupe pour une réservation
    /// </summary>
    public static string GetBookingGroup(Guid bookingId) => $"{BookingPrefix}{bookingId}";

    /// <summary>
    /// Obtient le nom du groupe pour un rôle
    /// </summary>
    public static string GetRoleGroup(UserRole role) => role switch
    {
        UserRole.Admin => Administrators,
        UserRole.Manager => Managers,
        UserRole.Support => Support,
        UserRole.Supervisor => Supervisors,
        UserRole.Client => Clients,
        UserRole.Provider => Providers,
        _ => throw new ArgumentException($"Rôle non supporté: {role}")
    };
}
