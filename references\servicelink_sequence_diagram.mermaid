sequenceDiagram
    participant C as Client
    participant F as Frontend
    participant API as API Gateway
    participant US as UserService
    participant SS as ServiceCatalogService
    participant BS as BookingService
    participant PS as PaymentService
    participant NS as NotificationService
    participant DB as PostgreSQL
    participant Redis as Redis Cache
    participant Hub as SignalR Hub
    participant PG as PaymentGateway

    %% User Registration and Authentication Flow
    Note over C, PG: User Registration & Authentication
    C->>F: Access registration page
    F->>F: Validate form data
    F->>API: POST /api/auth/register
    API->>US: RegisterUser(dto)
    US->>DB: Save user entity
    DB-->>US: User created
    US->>NS: SendWelcomeEmail(user)
    NS-->>US: Email sent
    US-->>API: Return AuthToken
    API-->>F: JWT + RefreshToken
    F->>F: Store tokens in localStorage
    F-->>C: Registration successful

    %% Service Search and Discovery Flow
    Note over C, PG: Service Search & Discovery
    C->>F: Search for services
    F->>API: GET /api/services/search?category=&location=
    API->>Redis: Check cached results
    alt Cache hit
        Redis-->>API: Return cached data
    else Cache miss
        API->>SS: SearchServices(criteria)
        SS->>DB: Query services with filters
        DB-->>SS: Return service list
        SS-->>API: ServiceDto list
        API->>Redis: Cache results (TTL: 5min)
    end
    API-->>F: Service search results
    F-->>C: Display services with filters

    %% Service Provider Profile View
    Note over C, PG: Provider Profile & Reviews
    C->>F: Click on provider profile
    F->>API: GET /api/services/{providerId}
    API->>SS: GetServiceDetails(providerId)
    SS->>DB: Query provider services
    DB-->>SS: Provider services data
    SS-->>API: ServiceDetailsDto
    
    F->>API: GET /api/reviews/provider/{providerId}
    API->>DB: Query provider reviews
    DB-->>API: Reviews list
    API-->>F: Provider profile + reviews
    F-->>C: Display comprehensive profile

    %% Booking Creation Flow
    Note over C, PG: Service Booking Process
    C->>F: Select service and book
    F->>F: Validate booking form
    F->>API: POST /api/bookings
    API->>BS: CreateBooking(dto)
    BS->>DB: Check provider availability
    DB-->>BS: Availability confirmed
    BS->>DB: Create booking entity
    DB-->>BS: Booking created
    
    BS->>NS: SendBookingNotification(provider)
    NS->>Hub: NotifyProvider(providerId)
    Hub-->>Provider: Real-time notification
    NS->>NS: SendBookingEmail(provider)
    
    BS-->>API: BookingDto
    API-->>F: Booking confirmation
    F-->>C: Booking successful

    %% Provider Booking Management
    Note over Provider, Hub: Provider Response
    Provider->>F: View booking request
    F->>API: GET /api/bookings/{bookingId}
    API->>BS: GetBookingDetails(bookingId)
    BS->>DB: Query booking details
    DB-->>BS: Booking data
    BS-->>API: BookingDetailsDto
    API-->>F: Booking details
    
    Provider->>F: Accept booking
    F->>API: PUT /api/bookings/{bookingId}/status
    API->>BS: AcceptBooking(bookingId)
    BS->>DB: Update booking status
    DB-->>BS: Status updated
    
    BS->>NS: NotifyClient(booking confirmed)
    NS->>Hub: NotifyClient(clientId)
    Hub-->>C: Real-time notification
    BS-->>API: Booking confirmed
    API-->>F: Success response

    %% Payment Processing Flow
    Note over C, PG: Payment Process
    C->>F: Proceed to payment
    F->>API: POST /api/payments/create-intent
    API->>PS: ProcessPayment(dto)
    PS->>PG: Create payment intent
    PG-->>PS: Payment intent created
    PS->>DB: Store payment record
    DB-->>PS: Payment saved
    PS-->>API: PaymentIntentDto
    API-->>F: Payment intent
    
    F->>PG: Confirm payment (client-side)
    PG->>API: Payment webhook
    API->>PS: HandlePaymentWebhook(payload)
    PS->>DB: Update payment status
    DB-->>PS: Payment confirmed
    
    PS->>BS: UpdateBookingPayment(bookingId)
    BS->>DB: Update booking payment status
    DB-->>BS: Booking updated
    
    PS->>NS: SendPaymentConfirmation(client, provider)
    NS->>Hub: NotifyUsers(payment confirmed)
    Hub-->>C: Payment successful
    Hub-->>Provider: Payment received

    %% Service Completion and Review Flow
    Note over Provider, C: Service Completion
    Provider->>F: Mark service as completed
    F->>API: PUT /api/bookings/{bookingId}/complete
    API->>BS: CompleteBooking(bookingId)
    BS->>DB: Update booking status to completed
    DB-->>BS: Booking completed
    
    BS->>PS: ProcessCommissionPayment(providerId)
    PS->>DB: Calculate and transfer commission
    DB-->>PS: Commission processed
    
    BS->>NS: RequestReview(clientId, bookingId)
    NS->>Hub: NotifyClientForReview(clientId)
    Hub-->>C: Review request notification
    
    C->>F: Submit review
    F->>API: POST /api/reviews
    API->>DB: Save review
    DB-->>API: Review saved
    API->>NS: NotifyProvider(review received)
    NS->>Hub: NotifyProvider(providerId)
    Hub-->>Provider: New review notification

    %% Admin Dashboard Analytics Flow
    Note over Admin, DB: Admin Analytics
    Admin->>F: Access admin dashboard
    F->>API: GET /api/admin/dashboard
    API->>Redis: Check cached analytics
    alt Cache hit
        Redis-->>API: Return cached data
    else Cache miss
        API->>DB: Query platform statistics
        DB-->>API: Raw analytics data
        API->>API: Process and aggregate data
        API->>Redis: Cache results (TTL: 1hour)
    end
    API-->>F: Dashboard analytics
    F-->>Admin: Display KPIs and charts

    %% Real-time Chat Flow
    Note over C, Provider: In-app Messaging
    C->>Hub: Connect to chat hub
    Hub->>Hub: Authenticate user
    Hub->>DB: Load chat history
    DB-->>Hub: Message history
    Hub-->>C: Chat connected + history
    
    C->>Hub: Send message to provider
    Hub->>DB: Store message
    DB-->>Hub: Message stored
    Hub->>Provider: Deliver message
    Provider-->>Hub: Message received confirmation
    Hub-->>C: Message delivered status

    %% Error Handling and Retry Flow
    Note over F, API: Error Handling
    F->>API: API request
    API-->>F: 401 Unauthorized
    F->>F: Check refresh token
    F->>API: POST /api/auth/refresh
    API-->>F: New access token
    F->>F: Update stored token
    F->>API: Retry original request
    API-->>F: Success response

    %% Background Jobs Flow
    Note over NS, DB: Scheduled Jobs
    loop Every 15 minutes
        NS->>DB: Query upcoming bookings
        DB-->>NS: Bookings in next hour
        NS->>NS: Send reminder notifications
        NS->>Hub: Push real-time reminders
    end
    
    loop Daily at midnight
        PS->>DB: Process pending commission payments
        DB-->>PS: Payments processed
        PS->>NS: Send payment summaries
    end