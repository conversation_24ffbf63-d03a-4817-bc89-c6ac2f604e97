using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;

namespace ServiceLink.Application.Behaviors;

/// <summary>
/// Behavior pour la validation automatique des requêtes avec FluentValidation
/// </summary>
/// <typeparam name="TRequest">Type de requête</typeparam>
/// <typeparam name="TResponse">Type de réponse</typeparam>
public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : class, IRequest<TResponse>
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;
    private readonly ILogger<ValidationBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="validators">Validateurs pour le type de requête</param>
    /// <param name="logger">Logger</param>
    public ValidationBehavior(IEnumerable<IValidator<TRequest>> validators, ILogger<ValidationBehavior<TRequest, TResponse>> logger)
    {
        _validators = validators;
        _logger = logger;
    }

    /// <summary>
    /// Traite la requête avec validation
    /// </summary>
    /// <param name="request">Requête</param>
    /// <param name="next">Délégué suivant dans le pipeline</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Réponse</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;

        if (!_validators.Any())
        {
            _logger.LogDebug("Aucun validateur trouvé pour {RequestName}", requestName);
            return await next();
        }

        _logger.LogDebug("Validation de la requête {RequestName}", requestName);

        var context = new ValidationContext<TRequest>(request);
        var validationResults = await Task.WhenAll(_validators.Select(v => v.ValidateAsync(context, cancellationToken)));

        var failures = validationResults
            .SelectMany(r => r.Errors)
            .Where(f => f != null)
            .ToList();

        if (failures.Any())
        {
            _logger.LogWarning("Échec de validation pour {RequestName}. Erreurs: {Errors}", 
                requestName, 
                string.Join("; ", failures.Select(f => f.ErrorMessage)));

            throw new ValidationException(failures);
        }

        _logger.LogDebug("Validation réussie pour {RequestName}", requestName);
        return await next();
    }
}

/// <summary>
/// Behavior pour le logging des performances des requêtes
/// </summary>
/// <typeparam name="TRequest">Type de requête</typeparam>
/// <typeparam name="TResponse">Type de réponse</typeparam>
public class PerformanceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : class, IRequest<TResponse>
{
    private readonly ILogger<PerformanceBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="logger">Logger</param>
    public PerformanceBehavior(ILogger<PerformanceBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Traite la requête avec mesure de performance
    /// </summary>
    /// <param name="request">Requête</param>
    /// <param name="next">Délégué suivant dans le pipeline</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Réponse</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        _logger.LogDebug("Début du traitement de {RequestName}", requestName);

        try
        {
            var response = await next();
            
            stopwatch.Stop();
            var elapsedMilliseconds = stopwatch.ElapsedMilliseconds;

            if (elapsedMilliseconds > 500) // Log si > 500ms
            {
                _logger.LogWarning("Requête lente détectée: {RequestName} a pris {ElapsedMilliseconds}ms", 
                    requestName, elapsedMilliseconds);
            }
            else
            {
                _logger.LogDebug("Traitement terminé pour {RequestName} en {ElapsedMilliseconds}ms", 
                    requestName, elapsedMilliseconds);
            }

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Erreur lors du traitement de {RequestName} après {ElapsedMilliseconds}ms", 
                requestName, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }
}

/// <summary>
/// Behavior pour le logging détaillé des requêtes
/// </summary>
/// <typeparam name="TRequest">Type de requête</typeparam>
/// <typeparam name="TResponse">Type de réponse</typeparam>
public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : class, IRequest<TResponse>
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="logger">Logger</param>
    public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Traite la requête avec logging détaillé
    /// </summary>
    /// <param name="request">Requête</param>
    /// <param name="next">Délégué suivant dans le pipeline</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Réponse</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var requestId = Guid.NewGuid();

        _logger.LogInformation("Traitement de la requête {RequestName} avec ID {RequestId}", requestName, requestId);

        // Log des propriétés de la requête (sans données sensibles)
        LogRequestProperties(request, requestName, requestId);

        try
        {
            var response = await next();
            
            _logger.LogInformation("Requête {RequestName} avec ID {RequestId} traitée avec succès", requestName, requestId);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de la requête {RequestName} avec ID {RequestId}", requestName, requestId);
            throw;
        }
    }

    /// <summary>
    /// Log les propriétés de la requête (en évitant les données sensibles)
    /// </summary>
    /// <param name="request">Requête</param>
    /// <param name="requestName">Nom de la requête</param>
    /// <param name="requestId">ID de la requête</param>
    private void LogRequestProperties(TRequest request, string requestName, Guid requestId)
    {
        var properties = typeof(TRequest).GetProperties()
            .Where(p => p.CanRead && !IsSensitiveProperty(p.Name))
            .ToDictionary(
                p => p.Name,
                p => p.GetValue(request)?.ToString() ?? "null"
            );

        if (properties.Any())
        {
            _logger.LogDebug("Propriétés de la requête {RequestName} (ID: {RequestId}): {@Properties}", 
                requestName, requestId, properties);
        }
    }

    /// <summary>
    /// Vérifie si une propriété contient des données sensibles
    /// </summary>
    /// <param name="propertyName">Nom de la propriété</param>
    /// <returns>True si la propriété est sensible</returns>
    private static bool IsSensitiveProperty(string propertyName)
    {
        var sensitiveProperties = new[]
        {
            "Password", "CurrentPassword", "NewPassword", "ConfirmPassword", "ConfirmNewPassword",
            "Token", "Secret", "PasswordHash", "PasswordSalt", "TwoFactorCode", "ConfirmationCode"
        };

        return sensitiveProperties.Any(sp => propertyName.Contains(sp, StringComparison.OrdinalIgnoreCase));
    }
}
