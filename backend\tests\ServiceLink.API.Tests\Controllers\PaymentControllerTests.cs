using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using ServiceLink.API.Controllers;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using ServiceLink.Infrastructure.Configuration;
using System.Security.Claims;
using Xunit;

namespace ServiceLink.API.Tests.Controllers;

/// <summary>
/// Tests unitaires pour PaymentController
/// </summary>
public class PaymentControllerTests
{
    private readonly Mock<IPaymentServiceFactory> _paymentServiceFactoryMock;
    private readonly Mock<ILogger<PaymentController>> _loggerMock;
    private readonly PaymentController _controller;
    private readonly Mock<IPaymentService> _paymentServiceMock;

    public PaymentControllerTests()
    {
        _paymentServiceFactoryMock = new Mock<IPaymentServiceFactory>();
        _loggerMock = new Mock<ILogger<PaymentController>>();
        _paymentServiceMock = new Mock<IPaymentService>();
        
        _controller = new PaymentController(_paymentServiceFactoryMock.Object, _loggerMock.Object);

        // Configuration du contexte utilisateur
        var user = new ClaimsPrincipal(new ClaimsIdentity(new[]
        {
            new Claim(ClaimTypes.NameIdentifier, Guid.NewGuid().ToString())
        }, "test"));

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = user }
        };
    }

    [Fact]
    public async Task CreatePaymentIntent_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new CreatePaymentIntentRequest
        {
            Amount = 1500,
            Currency = "EUR",
            BookingId = Guid.NewGuid(),
            Description = "Test payment",
            AllowedPaymentMethods = new List<PaymentMethodType> { PaymentMethodType.Card }
        };

        var expectedResponse = new PaymentIntentResponse
        {
            PaymentIntentId = "pi_test_123",
            ClientSecret = "pi_test_123_secret",
            Status = PaymentStatus.Pending,
            Amount = request.Amount,
            Currency = request.Currency
        };

        _paymentServiceMock.Setup(x => x.Provider).Returns(PaymentProvider.Stripe);
        _paymentServiceMock.Setup(x => x.CreatePaymentIntentAsync(It.IsAny<CreatePaymentIntentRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _paymentServiceFactoryMock.Setup(x => x.GetOptimalPaymentServiceForMethod(It.IsAny<PaymentMethodType>()))
            .Returns(_paymentServiceMock.Object);

        // Act
        var result = await _controller.CreatePaymentIntent(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsAssignableFrom<object>(okResult.Value);
        Assert.NotNull(response);

        // Vérifier que l'UserId a été défini
        _paymentServiceMock.Verify(x => x.CreatePaymentIntentAsync(
            It.Is<CreatePaymentIntentRequest>(r => r.UserId != Guid.Empty), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreatePaymentIntent_WithException_ReturnsBadRequest()
    {
        // Arrange
        var request = new CreatePaymentIntentRequest
        {
            Amount = 1500,
            Currency = "EUR",
            BookingId = Guid.NewGuid(),
            Description = "Test payment"
        };

        _paymentServiceMock.Setup(x => x.CreatePaymentIntentAsync(It.IsAny<CreatePaymentIntentRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Test error"));

        _paymentServiceFactoryMock.Setup(x => x.GetOptimalPaymentServiceForCurrency(It.IsAny<string>()))
            .Returns(_paymentServiceMock.Object);

        // Act
        var result = await _controller.CreatePaymentIntent(request);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
    }

    [Fact]
    public async Task ConfirmPayment_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new ConfirmPaymentRequest
        {
            PaymentIntentId = "pi_test_123",
            PaymentMethodId = "pm_test_123",
            Provider = PaymentProvider.Stripe
        };

        var expectedResponse = new PaymentConfirmationResponse
        {
            Success = true,
            TransactionId = "txn_test_123",
            Status = PaymentStatus.Completed
        };

        _paymentServiceMock.Setup(x => x.ConfirmPaymentAsync(request.PaymentIntentId, request.PaymentMethodId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _paymentServiceFactoryMock.Setup(x => x.CreatePaymentService(request.Provider))
            .Returns(_paymentServiceMock.Object);

        // Act
        var result = await _controller.ConfirmPayment(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsAssignableFrom<object>(okResult.Value);
        Assert.NotNull(response);
    }

    [Fact]
    public async Task GetPaymentStatus_WithValidParameters_ReturnsOkResult()
    {
        // Arrange
        var paymentId = "pi_test_123";
        var provider = PaymentProvider.Stripe;

        var expectedResponse = new PaymentStatusResponse
        {
            PaymentId = paymentId,
            Status = PaymentStatus.Completed,
            Amount = 1500,
            Currency = "EUR",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _paymentServiceMock.Setup(x => x.GetPaymentStatusAsync(paymentId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _paymentServiceFactoryMock.Setup(x => x.CreatePaymentService(provider))
            .Returns(_paymentServiceMock.Object);

        // Act
        var result = await _controller.GetPaymentStatus(paymentId, provider);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsAssignableFrom<object>(okResult.Value);
        Assert.NotNull(response);
    }

    [Fact]
    public async Task RefundPayment_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new RefundPaymentRequest
        {
            PaymentId = "pi_test_123",
            Provider = PaymentProvider.Stripe,
            Amount = 1500,
            Reason = "Customer request"
        };

        var expectedResponse = new RefundResponse
        {
            Success = true,
            RefundId = "re_test_123",
            Amount = request.Amount.Value,
            Status = RefundStatus.Succeeded
        };

        _paymentServiceMock.Setup(x => x.RefundPaymentAsync(It.IsAny<Application.Interfaces.RefundRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _paymentServiceFactoryMock.Setup(x => x.CreatePaymentService(request.Provider))
            .Returns(_paymentServiceMock.Object);

        // Configurer l'utilisateur avec le rôle Admin
        var adminUser = new ClaimsPrincipal(new ClaimsIdentity(new[]
        {
            new Claim(ClaimTypes.NameIdentifier, Guid.NewGuid().ToString()),
            new Claim(ClaimTypes.Role, "Admin")
        }, "test"));

        _controller.ControllerContext.HttpContext.User = adminUser;

        // Act
        var result = await _controller.RefundPayment(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsAssignableFrom<object>(okResult.Value);
        Assert.NotNull(response);
    }

    [Fact]
    public async Task GetUserTransactions_WithValidUser_ReturnsOkResult()
    {
        // Arrange
        var limit = 50;
        var expectedTransactions = new List<PaymentTransactionDto>
        {
            new PaymentTransactionDto
            {
                TransactionId = "txn_test_123",
                Provider = PaymentProvider.Stripe,
                Amount = 1500,
                Currency = "EUR",
                Status = PaymentStatus.Completed,
                Description = "Test transaction",
                CreatedAt = DateTime.UtcNow
            }
        };

        var paymentServices = new List<IPaymentService> { _paymentServiceMock.Object };

        _paymentServiceMock.Setup(x => x.GetUserTransactionsAsync(It.IsAny<Guid>(), limit, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedTransactions);

        _paymentServiceFactoryMock.Setup(x => x.GetAvailablePaymentServices())
            .Returns(paymentServices);

        // Act
        var result = await _controller.GetUserTransactions(limit);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsAssignableFrom<object>(okResult.Value);
        Assert.NotNull(response);
    }

    [Fact]
    public void GetAvailableProviders_ReturnsOkResult()
    {
        // Arrange
        var expectedStats = new
        {
            providers = new[] { "Stripe", "PayPal", "Flutterwave" },
            total_enabled = 3,
            default_provider = "Stripe"
        };

        _paymentServiceFactoryMock.Setup(x => x.GetProvidersStats())
            .Returns(expectedStats);

        // Act
        var result = _controller.GetAvailableProviders();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsAssignableFrom<object>(okResult.Value);
        Assert.NotNull(response);
    }

    [Theory]
    [InlineData(PaymentProvider.Stripe)]
    [InlineData(PaymentProvider.PayPal)]
    [InlineData(PaymentProvider.Flutterwave)]
    public void GetSupportedMethods_WithValidProvider_ReturnsOkResult(PaymentProvider provider)
    {
        // Act
        var result = _controller.GetSupportedMethods(provider);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsAssignableFrom<object>(okResult.Value);
        Assert.NotNull(response);
    }

    [Fact]
    public void GetSupportedMethods_WithException_ReturnsBadRequest()
    {
        // Arrange
        var invalidProvider = (PaymentProvider)999;

        // Act
        var result = _controller.GetSupportedMethods(invalidProvider);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
    }
}

/// <summary>
/// Tests unitaires pour WebhooksController
/// </summary>
public class WebhooksControllerTests
{
    private readonly Mock<IPaymentServiceFactory> _paymentServiceFactoryMock;
    private readonly Mock<ILogger<WebhooksController>> _loggerMock;
    private readonly WebhooksController _controller;
    private readonly Mock<IPaymentService> _paymentServiceMock;

    public WebhooksControllerTests()
    {
        _paymentServiceFactoryMock = new Mock<IPaymentServiceFactory>();
        _loggerMock = new Mock<ILogger<WebhooksController>>();
        _paymentServiceMock = new Mock<IPaymentService>();
        
        _controller = new WebhooksController(_paymentServiceFactoryMock.Object, _loggerMock.Object);

        // Configuration du contexte HTTP
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };
    }

    [Fact]
    public async Task TestWebhook_WithValidProvider_ReturnsOkResult()
    {
        // Arrange
        var provider = "Stripe";
        var expectedResult = new WebhookProcessingResult
        {
            Success = true,
            Message = "Test webhook processed successfully",
            ActionsPerformed = new List<string> { "Test action" }
        };

        _paymentServiceMock.Setup(x => x.ProcessWebhookEventAsync(It.IsAny<WebhookEventDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        _paymentServiceFactoryMock.Setup(x => x.CreatePaymentService(PaymentProvider.Stripe))
            .Returns(_paymentServiceMock.Object);

        // Act
        var result = await _controller.TestWebhook(provider);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsAssignableFrom<object>(okResult.Value);
        Assert.NotNull(response);
    }

    [Fact]
    public async Task TestWebhook_WithInvalidProvider_ReturnsBadRequest()
    {
        // Arrange
        var invalidProvider = "InvalidProvider";

        // Act
        var result = await _controller.TestWebhook(invalidProvider);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
    }

    [Fact]
    public void GetWebhookStats_ReturnsOkResult()
    {
        // Act
        var result = _controller.GetWebhookStats();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsAssignableFrom<object>(okResult.Value);
        Assert.NotNull(response);
    }
}
