import React from 'react';
import { Calendar, Clock, DollarSign, User, MapPin, MessageSquare } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import type { Booking, BookingStatus } from '@/stores/bookingStore';

interface BookingCardProps {
  booking: Booking;
  userRole: 'Client' | 'ServiceProvider' | 'Admin';
  onAction?: (action: string, booking: Booking) => void;
}

export const BookingCard: React.FC<BookingCardProps> = ({ 
  booking, 
  userRole, 
  onAction 
}) => {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(new Date(date));
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(price);
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h${remainingMinutes}min` : `${hours}h`;
  };

  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case 'Pending':
        return 'booking-status-pending';
      case 'Confirmed':
        return 'booking-status-confirmed';
      case 'InProgress':
        return 'booking-status-inprogress';
      case 'Completed':
        return 'booking-status-completed';
      case 'Cancelled':
        return 'booking-status-cancelled';
      case 'Rejected':
        return 'booking-status-rejected';
      case 'Expired':
        return 'booking-status-expired';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getAvailableActions = () => {
    const actions = [];

    if (userRole === 'ServiceProvider') {
      switch (booking.status) {
        case 'Pending':
          actions.push(
            { label: 'Accept', action: 'confirm', variant: 'default' as const },
            { label: 'Reject', action: 'reject', variant: 'destructive' as const }
          );
          break;
        case 'Confirmed':
          actions.push(
            { label: 'Start Service', action: 'start', variant: 'default' as const },
            { label: 'Cancel', action: 'cancel', variant: 'outline' as const }
          );
          break;
        case 'InProgress':
          actions.push(
            { label: 'Complete', action: 'complete', variant: 'default' as const }
          );
          break;
      }
    } else if (userRole === 'Client') {
      switch (booking.status) {
        case 'Pending':
        case 'Confirmed':
          actions.push(
            { label: 'Cancel', action: 'cancel', variant: 'destructive' as const }
          );
          break;
        case 'Completed':
          if (!booking.review) {
            actions.push(
              { label: 'Leave Review', action: 'review', variant: 'default' as const }
            );
          }
          break;
      }
    }

    // Common actions
    actions.push(
      { label: 'View Details', action: 'details', variant: 'outline' as const }
    );

    return actions;
  };

  const otherParty = userRole === 'Client' ? booking.provider : booking.client;

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg">
              {booking.service?.name || 'Service'}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Booking #{booking.id.slice(-8)}
            </p>
          </div>
          <Badge className={getStatusColor(booking.status)}>
            {booking.status}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Date and Time */}
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span>{formatDate(booking.scheduledDate)}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span>
              {formatTime(booking.scheduledDate)} - {formatTime(booking.scheduledEndDate)}
              <span className="text-muted-foreground ml-1">
                ({formatDuration(booking.durationMinutes)})
              </span>
            </span>
          </div>
        </div>

        {/* Other Party Info */}
        {otherParty && (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={otherParty.avatar} alt={otherParty.firstName} />
              <AvatarFallback>
                {otherParty.firstName.charAt(0)}{otherParty.lastName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">
                {userRole === 'Client' ? 'Service Provider' : 'Client'}: {otherParty.firstName} {otherParty.lastName}
              </p>
              <p className="text-xs text-muted-foreground">{otherParty.email}</p>
            </div>
          </div>
        )}

        {/* Service Category */}
        {booking.service?.category && (
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">
              {booking.service.category.name}
            </Badge>
          </div>
        )}

        {/* Price Information */}
        <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
          <div className="space-y-1">
            <div className="flex items-center space-x-2 text-sm">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span>Base Amount: {formatPrice(booking.baseAmount)}</span>
            </div>
            {booking.urgentSurcharge > 0 && (
              <div className="text-xs text-muted-foreground">
                Urgent Surcharge: {formatPrice(booking.urgentSurcharge)}
              </div>
            )}
          </div>
          <div className="text-right">
            <p className="font-semibold">{formatPrice(booking.totalAmount)}</p>
            <p className="text-xs text-muted-foreground">Total</p>
          </div>
        </div>

        {/* Notes */}
        {booking.notes && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-start space-x-2">
              <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div>
                <p className="text-xs font-medium text-muted-foreground mb-1">Client Notes:</p>
                <p className="text-sm">{booking.notes}</p>
              </div>
            </div>
          </div>
        )}

        {/* Provider Notes */}
        {booking.providerNotes && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-start space-x-2">
              <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div>
                <p className="text-xs font-medium text-muted-foreground mb-1">Provider Notes:</p>
                <p className="text-sm">{booking.providerNotes}</p>
              </div>
            </div>
          </div>
        )}

        {/* Review */}
        {booking.review && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-start space-x-2">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <span
                    key={i}
                    className={`text-sm ${
                      i < booking.review!.rating ? 'text-yellow-400' : 'text-gray-300'
                    }`}
                  >
                    ★
                  </span>
                ))}
              </div>
              <div className="flex-1">
                <p className="text-xs font-medium text-muted-foreground mb-1">Review:</p>
                <p className="text-sm">{booking.review.comment}</p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2 pt-2">
          {getAvailableActions().map((action, index) => (
            <Button
              key={index}
              variant={action.variant}
              size="sm"
              onClick={() => onAction?.(action.action, booking)}
            >
              {action.label}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
