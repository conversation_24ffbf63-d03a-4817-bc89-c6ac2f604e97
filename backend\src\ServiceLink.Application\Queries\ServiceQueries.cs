using MediatR;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Common.Models;

namespace ServiceLink.Application.Services.Queries;

// Queries pour les catégories
public class GetServiceCategoriesQuery : IRequest<List<ServiceCategoryDto>>
{
}

public class GetServiceCategoryByIdQuery : IRequest<ServiceCategoryDto?>
{
    public Guid Id { get; set; }
}

// Queries pour les services
public class SearchServicesQuery : IRequest<PaginatedResult<ServiceDto>>
{
    public string? CategoryId { get; set; }
    public string? Location { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public decimal? Rating { get; set; }
    public string? Availability { get; set; }
    public bool? Urgent { get; set; }
    public int? Radius { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public List<string>? Tags { get; set; }
    public string? PricingUnit { get; set; }
    public string? Search { get; set; }
    
    // Pagination
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; }
}

public class GetServiceByIdQuery : IRequest<ServiceDto?>
{
    public Guid Id { get; set; }
}

public class GetPopularServicesQuery : IRequest<List<ServiceDto>>
{
    public int Limit { get; set; } = 6;
}

public class GetFeaturedServicesQuery : IRequest<List<ServiceDto>>
{
    public int Limit { get; set; } = 8;
}

public class GetServiceStatsQuery : IRequest<ServiceStatsDto>
{
}

public class GetSearchSuggestionsQuery : IRequest<List<string>>
{
    public string SearchTerm { get; set; } = string.Empty;
}

public class GetNearbyServicesQuery : IRequest<List<ServiceDto>>
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public int Radius { get; set; } = 10;
    public int Limit { get; set; } = 10;
}

public class GetServicesByProviderQuery : IRequest<List<ServiceDto>>
{
    public Guid ProviderId { get; set; }
}

public class GetFavoriteServicesQuery : IRequest<List<ServiceDto>>
{
    public Guid UserId { get; set; }
}
