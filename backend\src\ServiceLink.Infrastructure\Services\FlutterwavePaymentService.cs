using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Implémentation du service de paiement Flutterwave
/// </summary>
public class FlutterwavePaymentService : IPaymentService
{
    private readonly ILogger<FlutterwavePaymentService> _logger;
    private readonly FlutterwaveSettings _settings;
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public FlutterwavePaymentService(
        IConfiguration configuration,
        ILogger<FlutterwavePaymentService> logger,
        HttpClient httpClient)
    {
        _logger = logger;
        _settings = configuration.GetSection("Flutterwave").Get<FlutterwaveSettings>() ?? new FlutterwaveSettings();
        _httpClient = httpClient;

        // Configuration de l'URL de base selon l'environnement
        var baseUrl = _settings.Environment.ToLowerInvariant() == "production" 
            ? "https://api.flutterwave.com/v3/"
            : "https://api.flutterwave.com/v3/";

        _httpClient.BaseAddress = new Uri(baseUrl);
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_settings.SecretKey}");

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
            WriteIndented = false
        };

        _logger.LogInformation("Service de paiement Flutterwave initialisé en mode {Environment}", _settings.Environment);
    }

    /// <inheritdoc />
    public PaymentProvider Provider => PaymentProvider.Flutterwave;

    /// <inheritdoc />
    public async Task<PaymentIntentResponse> CreatePaymentIntentAsync(
        CreatePaymentIntentRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Création d'un paiement Flutterwave pour {Amount} {Currency}", 
                request.Amount, request.Currency);

            var paymentData = new
            {
                tx_ref = $"TX-{request.BookingId}-{DateTime.UtcNow:yyyyMMddHHmmss}",
                amount = FormatAmount(request.Amount, request.Currency),
                currency = request.Currency.ToUpperInvariant(),
                redirect_url = request.SuccessUrl ?? "http://localhost:3000/payment/success",
                payment_options = GetPaymentOptions(request.AllowedPaymentMethods),
                customer = new
                {
                    email = "<EMAIL>", // À récupérer depuis les données utilisateur
                    phonenumber = "+1234567890",
                    name = "Customer Name"
                },
                customizations = new
                {
                    title = "ServiceLink Payment",
                    description = request.Description,
                    logo = "https://servicelink.com/logo.png"
                },
                meta = new
                {
                    user_id = request.UserId.ToString(),
                    booking_id = request.BookingId.ToString()
                }
            };

            var json = JsonSerializer.Serialize(paymentData, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("payments", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Erreur Flutterwave: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new InvalidOperationException($"Erreur Flutterwave: {response.StatusCode}");
            }

            var result = JsonSerializer.Deserialize<FlutterwavePaymentResponse>(responseContent, _jsonOptions);
            
            if (result?.Status != "success")
            {
                throw new InvalidOperationException($"Erreur Flutterwave: {result?.Message}");
            }

            _logger.LogInformation("Paiement Flutterwave créé: {TxRef}", result.Data?.TxRef);

            return new PaymentIntentResponse
            {
                PaymentIntentId = result.Data?.TxRef ?? string.Empty,
                ClientSecret = result.Data?.TxRef ?? string.Empty,
                Status = PaymentStatus.Pending,
                Amount = request.Amount,
                Currency = request.Currency,
                RedirectUrl = result.Data?.Link,
                ProviderData = new Dictionary<string, object>
                {
                    ["flutterwave_tx_ref"] = result.Data?.TxRef ?? string.Empty,
                    ["flutterwave_link"] = result.Data?.Link ?? string.Empty
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du paiement Flutterwave");
            throw new InvalidOperationException($"Erreur Flutterwave: {ex.Message}", ex);
        }
    }

    /// <inheritdoc />
    public async Task<PaymentConfirmationResponse> ConfirmPaymentAsync(
        string paymentIntentId, 
        string paymentMethodId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Vérification du paiement Flutterwave {TxRef}", paymentIntentId);

            // Flutterwave utilise la vérification de transaction plutôt que la confirmation
            var response = await _httpClient.GetAsync($"transactions/verify_by_reference?tx_ref={paymentIntentId}", cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Erreur lors de la vérification Flutterwave: {StatusCode} - {Content}", 
                    response.StatusCode, responseContent);
                
                return new PaymentConfirmationResponse
                {
                    Success = false,
                    TransactionId = paymentIntentId,
                    Status = PaymentStatus.Failed,
                    ErrorMessage = "Erreur lors de la vérification",
                    ErrorCode = response.StatusCode.ToString()
                };
            }

            var result = JsonSerializer.Deserialize<FlutterwaveVerifyResponse>(responseContent, _jsonOptions);
            
            var success = result?.Status == "success" && result.Data?.Status == "successful";
            
            _logger.LogInformation("Paiement Flutterwave {TxRef} vérifié: {Status}", 
                paymentIntentId, result?.Data?.Status);

            return new PaymentConfirmationResponse
            {
                Success = success,
                TransactionId = result?.Data?.Id?.ToString() ?? paymentIntentId,
                Status = MapFlutterwaveStatus(result?.Data?.Status ?? "failed"),
                ErrorMessage = success ? null : $"Paiement non confirmé: {result?.Data?.Status}",
                ErrorCode = success ? null : result?.Data?.Status
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la confirmation Flutterwave {TxRef}", paymentIntentId);
            
            return new PaymentConfirmationResponse
            {
                Success = false,
                TransactionId = paymentIntentId,
                Status = PaymentStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = "VERIFICATION_ERROR"
            };
        }
    }

    /// <inheritdoc />
    public async Task<PaymentStatusResponse> GetPaymentStatusAsync(
        string paymentId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Récupération du statut Flutterwave {TxRef}", paymentId);

            var response = await _httpClient.GetAsync($"transactions/verify_by_reference?tx_ref={paymentId}", cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                throw new InvalidOperationException($"Erreur Flutterwave: {response.StatusCode}");
            }

            var result = JsonSerializer.Deserialize<FlutterwaveVerifyResponse>(responseContent, _jsonOptions);

            if (result?.Status != "success")
            {
                throw new InvalidOperationException($"Erreur Flutterwave: {result?.Message}");
            }

            return new PaymentStatusResponse
            {
                PaymentId = result.Data?.TxRef ?? paymentId,
                Status = MapFlutterwaveStatus(result.Data?.Status ?? "failed"),
                Amount = (long)((result.Data?.Amount ?? 0m) * 100), // Convertir en centimes
                Currency = result.Data?.Currency ?? "NGN",
                CreatedAt = DateTime.TryParse(result.Data?.CreatedAt, out var createTime) ? createTime : DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, string>
                {
                    ["flw_ref"] = result.Data?.FlwRef ?? string.Empty,
                    ["processor_response"] = result.Data?.ProcessorResponse ?? string.Empty
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du statut Flutterwave {TxRef}", paymentId);
            throw new InvalidOperationException($"Erreur Flutterwave: {ex.Message}", ex);
        }
    }

    /// <inheritdoc />
    public async Task<RefundResponse> RefundPaymentAsync(
        RefundRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Remboursement Flutterwave pour la transaction {TransactionId}", request.PaymentId);

            // Flutterwave nécessite l'ID de transaction numérique pour les remboursements
            // Dans une implémentation complète, on stockerait cette correspondance en base
            _logger.LogWarning("Remboursement Flutterwave nécessite l'ID de transaction numérique - non implémenté");
            
            return new RefundResponse
            {
                Success = false,
                RefundId = string.Empty,
                Amount = 0,
                Status = RefundStatus.Failed,
                ErrorMessage = "Remboursement Flutterwave non implémenté - utiliser le dashboard Flutterwave"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du remboursement Flutterwave {TransactionId}", request.PaymentId);
            
            return new RefundResponse
            {
                Success = false,
                RefundId = string.Empty,
                Amount = 0,
                Status = RefundStatus.Failed,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PaymentTransactionDto>> GetUserTransactionsAsync(
        Guid userId, 
        int limit = 50, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Récupération des transactions Flutterwave pour l'utilisateur {UserId}", userId);

            // Flutterwave ne fournit pas d'API directe pour filtrer par utilisateur
            // Dans une implémentation complète, on stockerait les transactions en base de données
            
            _logger.LogWarning("GetUserTransactionsAsync non implémenté pour Flutterwave - utiliser la base de données locale");
            
            return Enumerable.Empty<PaymentTransactionDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des transactions Flutterwave pour {UserId}", userId);
            throw;
        }
    }

    /// <inheritdoc />
    public bool ValidateWebhookSignature(string payload, string signature, string secret)
    {
        try
        {
            // Flutterwave utilise HMAC-SHA256 pour la validation
            var computedHash = ComputeHmacSha256(payload, secret);
            return string.Equals(computedHash, signature, StringComparison.OrdinalIgnoreCase);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erreur lors de la validation webhook Flutterwave");
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<WebhookProcessingResult> ProcessWebhookEventAsync(
        WebhookEventDto webhookEvent, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Traitement de l'événement webhook Flutterwave: {EventType}", webhookEvent.EventType);

            var result = new WebhookProcessingResult { Success = true };

            switch (webhookEvent.EventType)
            {
                case "charge.completed":
                    result.ActionsPerformed.Add("Paiement complété avec succès");
                    break;

                case "charge.failed":
                    result.ActionsPerformed.Add("Paiement échoué - notification envoyée");
                    break;

                case "transfer.completed":
                    result.ActionsPerformed.Add("Transfert complété");
                    break;

                default:
                    result.Message = $"Événement {webhookEvent.EventType} ignoré";
                    break;
            }

            _logger.LogInformation("Événement webhook Flutterwave {EventType} traité avec succès", webhookEvent.EventType);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de l'événement webhook Flutterwave {EventType}", 
                webhookEvent.EventType);
            
            return new WebhookProcessingResult
            {
                Success = false,
                Message = "Erreur lors du traitement",
                Errors = { ex.Message }
            };
        }
    }

    #region Méthodes privées

    private static PaymentStatus MapFlutterwaveStatus(string flutterwaveStatus)
    {
        return flutterwaveStatus switch
        {
            "successful" => PaymentStatus.Completed,
            "failed" => PaymentStatus.Failed,
            "cancelled" => PaymentStatus.Cancelled,
            "pending" => PaymentStatus.Pending,
            _ => PaymentStatus.Failed
        };
    }

    private static string FormatAmount(long amountInCents, string currency)
    {
        var amount = amountInCents / 100.0m;
        return amount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }

    private static string GetPaymentOptions(List<PaymentMethodType> allowedMethods)
    {
        if (!allowedMethods.Any())
        {
            return "card,mobilemoney,banktransfer";
        }

        var options = new List<string>();
        
        foreach (var method in allowedMethods)
        {
            switch (method)
            {
                case PaymentMethodType.Card:
                    options.Add("card");
                    break;
                case PaymentMethodType.MobileMoney:
                    options.Add("mobilemoney");
                    break;
                case PaymentMethodType.BankTransfer:
                    options.Add("banktransfer");
                    break;
            }
        }

        return string.Join(",", options);
    }

    private static string ComputeHmacSha256(string data, string key)
    {
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(key));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
        return Convert.ToHexString(hash).ToLowerInvariant();
    }

    #endregion

    #region DTOs Flutterwave

    private class FlutterwavePaymentResponse
    {
        public string? Status { get; set; }
        public string? Message { get; set; }
        public FlutterwavePaymentData? Data { get; set; }
    }

    private class FlutterwavePaymentData
    {
        public string? Link { get; set; }
        public string? TxRef { get; set; }
    }

    private class FlutterwaveVerifyResponse
    {
        public string? Status { get; set; }
        public string? Message { get; set; }
        public FlutterwaveTransactionData? Data { get; set; }
    }

    private class FlutterwaveTransactionData
    {
        public int? Id { get; set; }
        public string? TxRef { get; set; }
        public string? FlwRef { get; set; }
        public string? Status { get; set; }
        public decimal Amount { get; set; }
        public string? Currency { get; set; }
        public string? CreatedAt { get; set; }
        public string? ProcessorResponse { get; set; }
    }

    #endregion
}

/// <summary>
/// Configuration pour Flutterwave
/// </summary>
public class FlutterwaveSettings
{
    /// <summary>
    /// Clé publique Flutterwave
    /// </summary>
    public string PublicKey { get; set; } = string.Empty;

    /// <summary>
    /// Clé secrète Flutterwave
    /// </summary>
    public string SecretKey { get; set; } = string.Empty;

    /// <summary>
    /// Clé de chiffrement
    /// </summary>
    public string EncryptionKey { get; set; } = string.Empty;

    /// <summary>
    /// Environnement (sandbox ou production)
    /// </summary>
    public string Environment { get; set; } = "sandbox";

    /// <summary>
    /// Hash du webhook
    /// </summary>
    public string WebhookHash { get; set; } = string.Empty;
}
