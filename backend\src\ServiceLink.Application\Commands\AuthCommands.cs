using MediatR;
using ServiceLink.Application.DTOs;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.Commands;

/// <summary>
/// Commande de connexion
/// </summary>
public class LoginCommand : IRequest<LoginResponse?>
{
    /// <summary>
    /// Adresse email
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Mot de passe
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Se souvenir de moi
    /// </summary>
    public bool RememberMe { get; set; } = false;
}

/// <summary>
/// Commande pour créer un nouvel utilisateur
/// </summary>
public class RegisterCommand : IRequest<UserResponse>
{
    /// <summary>
    /// Adresse email
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Mot de passe
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Confirmation du mot de passe
    /// </summary>
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// Prénom
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Nom
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Type d'utilisateur
    /// </summary>
    public UserRole Role { get; set; } = UserRole.Client;

    /// <summary>
    /// Langue préférée
    /// </summary>
    public string Language { get; set; } = "fr-FR";

    /// <summary>
    /// Fuseau horaire
    /// </summary>
    public string TimeZone { get; set; } = "Europe/Paris";

    /// <summary>
    /// Utilisateur qui crée (pour l'audit)
    /// </summary>
    public Guid? CreatedBy { get; set; }
    /// <summary>
    /// Nom de l'entreprise (pour les prestataires)
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// Numéro SIRET (pour les prestataires)
    /// </summary>
    public string? Siret { get; set; }

    /// <summary>
    /// Description de l'activité (pour les prestataires)
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Numéro de téléphone (pour les prestataires)
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Adresse (pour les prestataires)
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Ville (pour les prestataires)
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// Code postal (pour les prestataires)
    /// </summary>
    public string? PostalCode { get; set; }

    /// <summary>
    /// Pays (pour les prestataires)
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// Acceptation des conditions d'utilisation
    /// </summary>
    public bool AcceptTerms { get; set; } = false;
}

/// <summary>
/// Commande de rafraîchissement de token
/// </summary>
public class RefreshTokenCommand : IRequest<RefreshTokenResponse?>
{
    /// <summary>
    /// Token de rafraîchissement
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// Commande de déconnexion
/// </summary>
public class LogoutCommand : IRequest<bool>
{
    /// <summary>
    /// ID de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Token du rafraîchissement à invalider
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// Commande de mot de passe oublié
/// </summary>
public class ForgotPasswordCommand : IRequest<bool>
{
    /// <summary>
    /// Adresse email
    /// </summary>
    public string Email { get; set; } = string.Empty;
}

/// <summary>
/// Commande de réinitialisation de mot de passe
/// </summary>
public class ResetPasswordCommand : IRequest<bool>
{
    /// <summary>
    /// Token de réinitialisation
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Adresse email
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Nouveau mot de passe
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// Confirmation du nouveau mot de passe
    /// </summary>
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// Commande de confirmation d'email d'un utilisateur
/// </summary>
public class ConfirmEmailCommand : IRequest<bool>
{
    /// <summary>
    /// Token de confirmation d'email
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Utilisateur qui confirme (pour l'audit)
    /// </summary>
    public Guid? ConfirmedBy { get; set; }
}

/// <summary>
/// Commande de renvoi d'email de confirmation
/// </summary>
public class ResendConfirmationEmailCommand : IRequest<bool>
{
    /// <summary>
    /// ID de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }
}
