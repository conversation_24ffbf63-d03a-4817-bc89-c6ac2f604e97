using MediatR;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Interfaces;

namespace ServiceLink.Application.Handlers;

public class RefreshTokenCommandHandler : IRequestHandler<RefreshTokenCommand, RefreshTokenResponse?>
{
    private readonly IUserRepository _userRepository;
    private readonly IJwtService _jwtService;
    private readonly IRefreshTokenRepository _refreshTokenRepository;

    public RefreshTokenCommandHandler(IUserRepository userRepository, IJwtService jwtService, IRefreshTokenRepository refreshTokenRepository)
    {
        _userRepository = userRepository;
        _jwtService = jwtService;
        _refreshTokenRepository = refreshTokenRepository;
    }

    public async Task<RefreshTokenResponse?> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
    {
        // 1. Récupérer le refresh token en base
        var refreshToken = await _refreshTokenRepository.GetByTokenAsync(request.RefreshToken);
        if (refreshToken == null || refreshToken.IsRevoked || refreshToken.ExpiresAt < System.DateTime.UtcNow)
            return null;

        // 2. Récupérer l'utilisateur associé
        var user = await _userRepository.GetByIdAsync(refreshToken.UserId);
        if (user == null || !user.IsActive)
            return null;

        // 3. Invalider l'ancien refresh token
        await _refreshTokenRepository.InvalidateAsync(refreshToken.Token);

        // 4. Générer un nouveau JWT et refresh token
        var jwtToken = _jwtService.GenerateJwtToken(user);
        var newRefreshToken = _jwtService.GenerateRefreshToken(user);
        var expiresAt = System.DateTime.UtcNow.AddMinutes(120); // À adapter selon config

        // 5. Retourner la réponse
        return new RefreshTokenResponse
        {
            Token = jwtToken,
            RefreshToken = newRefreshToken,
            ExpiresAt = expiresAt
        };
    }
}
