import type { Service, ServiceCategory } from '../types/service'

// Données mockées pour les tests
export const mockCategories: ServiceCategory[] = [
  {
    id: '1',
    name: 'Ménage',
    description: 'Services de ménage et nettoyage',
    icon: '🏠',
    serviceCount: 2500,
    isActive: true,
    displayOrder: 1,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Entretien de jardins et espaces verts',
    icon: '🌱',
    serviceCount: 1800,
    isActive: true,
    displayOrder: 2,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '3',
    name: 'Bricolage',
    description: 'Petits travaux et réparations',
    icon: '🔧',
    serviceCount: 3200,
    isActive: true,
    displayOrder: 3,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '4',
    name: '<PERSON>ard<PERSON> d\'enfants',
    description: 'Services de garde et baby-sitting',
    icon: '👶',
    serviceCount: 1500,
    isActive: true,
    displayOrder: 4,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '5',
    name: 'Cours particuliers',
    description: 'Soutien scolaire et formation',
    icon: '📚',
    serviceCount: 2100,
    isActive: true,
    displayOrder: 5,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '6',
    name: 'Livraison',
    description: 'Services de livraison et courses',
    icon: '📦',
    serviceCount: 900,
    isActive: true,
    displayOrder: 6,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

export const mockFeaturedServices: Service[] = [
  {
    id: '1',
    name: 'Ménage complet appartement',
    description: 'Service de ménage complet pour votre appartement',
    shortDescription: 'Ménage professionnel',
    categoryId: '1',
    category: mockCategories[0],
    providerId: '1',
    provider: {
      id: '1',
      firstName: 'Marie-Josette',
      lastName: 'Dupont',
      email: '<EMAIL>',
      avatar: '/api/placeholder/80/80',
      rating: 4.9,
      reviewCount: 127,
      completedBookings: 245,
      responseTime: '< 2h',
      isVerified: true,
      isOnline: true,
      joinedAt: '2023-01-15T00:00:00Z',
      location: {
        address: '15 rue de la Paix',
        city: 'Paris 15e',
        postalCode: '75015',
        latitude: 48.8566,
        longitude: 2.3522
      },
      specialties: ['Ménage régulier', 'Repassage', 'Vitres'],
      languages: ['Français'],
      certifications: ['Certification ménage professionnel']
    },
    basePrice: 15,
    pricingUnit: 'Hourly',
    currency: 'EUR',
    duration: 120,
    isActive: true,
    isUrgent: false,
    isFeatured: true,
    rating: 4.9,
    reviewCount: 127,
    bookingCount: 245,
    images: ['/api/placeholder/400/300'],
    tags: ['Ménage régulier', 'Repassage', 'Vitres'],
    requirements: ['Produits fournis', 'Accès à l\'eau'],
    included: ['Aspirateur', 'Serpillière', 'Produits de base'],
    excluded: ['Produits spéciaux', 'Nettoyage extérieur'],
    location: {
      address: '15 rue de la Paix',
      city: 'Paris 15e',
      postalCode: '75015',
      latitude: 48.8566,
      longitude: 2.3522,
      serviceRadius: 10
    },
    availability: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: true,
      sunday: false,
      startTime: '08:00',
      endTime: '18:00',
      timeZone: 'Europe/Paris'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Bricolage et petites réparations',
    description: 'Service de bricolage pour tous vos petits travaux',
    shortDescription: 'Bricolage professionnel',
    categoryId: '3',
    category: mockCategories[2],
    providerId: '2',
    provider: {
      id: '2',
      firstName: 'Karim',
      lastName: 'Benali',
      email: '<EMAIL>',
      avatar: '/api/placeholder/80/80',
      rating: 4.8,
      reviewCount: 89,
      completedBookings: 156,
      responseTime: '< 1h',
      isVerified: true,
      isOnline: true,
      joinedAt: '2023-03-20T00:00:00Z',
      location: {
        address: '42 avenue de la République',
        city: 'Lyon 3e',
        postalCode: '69003',
        latitude: 45.7640,
        longitude: 4.8357
      },
      specialties: ['Montage meuble', 'Petites réparations', 'Électricité'],
      languages: ['Français', 'Arabe'],
      certifications: ['Habilitation électrique']
    },
    basePrice: 25,
    pricingUnit: 'Hourly',
    currency: 'EUR',
    duration: 60,
    isActive: true,
    isUrgent: false,
    isFeatured: true,
    rating: 4.8,
    reviewCount: 89,
    bookingCount: 156,
    images: ['/api/placeholder/400/300'],
    tags: ['Montage meuble', 'Petites réparations', 'Électricité'],
    requirements: ['Outils fournis'],
    included: ['Outils de base', 'Petites fournitures'],
    excluded: ['Matériaux importants', 'Gros électroménager'],
    location: {
      address: '42 avenue de la République',
      city: 'Lyon 3e',
      postalCode: '69003',
      latitude: 45.7640,
      longitude: 4.8357,
      serviceRadius: 15
    },
    availability: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: true,
      sunday: true,
      startTime: '09:00',
      endTime: '19:00',
      timeZone: 'Europe/Paris'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

export const mockStats = {
  totalServices: 12000,
  totalProviders: 322000,
  totalBookings: 1255000,
  averageRating: 4.9,
  topCategories: [
    { name: 'Ménage', count: 2500 },
    { name: 'Bricolage', count: 3200 },
    { name: 'Jardinage', count: 1800 }
  ]
};

// Service mock qui simule les appels API
export const mockService = {
  async getCategories(): Promise<ServiceCategory[]> {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simule la latence
    return mockCategories;
  },

  async getFeaturedServices(limit: number = 8): Promise<Service[]> {
    await new Promise(resolve => setTimeout(resolve, 800));
    return mockFeaturedServices.slice(0, limit);
  },

  async getServiceStats() {
    await new Promise(resolve => setTimeout(resolve, 600));
    return mockStats;
  }
};
