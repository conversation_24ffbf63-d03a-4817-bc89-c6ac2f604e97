using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Infrastructure.Data;

namespace ServiceLink.Infrastructure.Repositories;

/// <summary>
/// Implémentation du repository pour les avis
/// </summary>
public class ReviewRepository : Repository<Review>, IReviewRepository
{
    public ReviewRepository(ServiceLinkDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient les avis d'un prestataire
    /// </summary>
    public async Task<IEnumerable<Review>> GetByProviderIdAsync(Guid providerId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Review>()
            .Where(r => r.ProviderId == providerId && r.IsPublished)
            .Include(r => r.Client)
            .Include(r => r.Service)
            .Include(r => r.Booking)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les avis d'un client
    /// </summary>
    public async Task<IEnumerable<Review>> GetByClientIdAsync(Guid clientId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Review>()
            .Where(r => r.ClientId == clientId)
            .Include(r => r.Provider)
            .Include(r => r.Service)
            .Include(r => r.Booking)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les avis d'un service
    /// </summary>
    public async Task<IEnumerable<Review>> GetByServiceIdAsync(Guid serviceId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Review>()
            .Where(r => r.ServiceId == serviceId && r.IsPublished)
            .Include(r => r.Client)
            .Include(r => r.Provider)
            .Include(r => r.Booking)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient l'avis d'une réservation
    /// </summary>
    public async Task<Review?> GetByBookingIdAsync(Guid bookingId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Review>()
            .Include(r => r.Client)
            .Include(r => r.Provider)
            .Include(r => r.Service)
            .Include(r => r.Booking)
            .FirstOrDefaultAsync(r => r.BookingId == bookingId, cancellationToken);
    }

    /// <summary>
    /// Vérifie si un avis existe pour une réservation
    /// </summary>
    public async Task<bool> ExistsForBookingAsync(Guid bookingId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Review>()
            .AnyAsync(r => r.BookingId == bookingId, cancellationToken);
    }

    /// <summary>
    /// Obtient un avis avec toutes ses relations
    /// </summary>
    public override async Task<Review?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Review>()
            .Include(r => r.Client)
            .Include(r => r.Provider)
            .Include(r => r.Service)
            .Include(r => r.Booking)
            .FirstOrDefaultAsync(r => r.Id == id, cancellationToken);
    }
}


