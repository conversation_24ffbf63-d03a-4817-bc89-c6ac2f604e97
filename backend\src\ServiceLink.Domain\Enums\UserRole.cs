namespace ServiceLink.Domain.Enums;

/// <summary>
/// Énumération des rôles utilisateur dans ServiceLink
/// Définit les différents niveaux d'accès et permissions
/// </summary>
public enum UserRole
{
    /// <summary>
    /// Client - Utilisateur qui recherche et réserve des services
    /// Permissions : Rechercher prestataires, créer réservations, effectuer paiements, laisser avis
    /// </summary>
    Client = 1,

    /// <summary>
    /// Prestataire - Fournisseur de services à domicile
    /// Permissions : Créer profil, gérer services, accepter réservations, recevoir paiements
    /// </summary>
    Provider = 2,

    /// <summary>
    /// Administrateur Global - Accès complet au système
    /// Permissions : Toutes les permissions, gestion utilisateurs, configuration système
    /// </summary>
    Admin = 3,

    /// <summary>
    /// Support Client - Assistance aux utilisateurs
    /// Permissions : Consulter données, gérer tickets support, chat client
    /// </summary>
    Support = 4,

    /// <summary>
    /// Manager - Gestion des réclamations et litiges
    /// Permissions : Gérer réclamations, arbitrer litiges, sanctions utilisateurs
    /// </summary>
    Manager = 5,

    /// <summary>
    /// Superviseur - Validation des prestataires
    /// Permissions : Valider documents prestataires, approuver profils, modération
    /// </summary>
    Supervisor = 6
}

/// <summary>
/// Extensions pour l'énumération UserRole
/// Fournit des méthodes utilitaires pour travailler avec les rôles
/// </summary>
public static class UserRoleExtensions
{
    /// <summary>
    /// Vérifie si le rôle est un rôle administratif
    /// </summary>
    /// <param name="role">Le rôle à vérifier</param>
    /// <returns>True si le rôle est administratif</returns>
    public static bool IsAdministrative(this UserRole role)
    {
        return role is UserRole.Admin or UserRole.Support or UserRole.Manager or UserRole.Supervisor;
    }

    /// <summary>
    /// Vérifie si le rôle est un rôle utilisateur final
    /// </summary>
    /// <param name="role">Le rôle à vérifier</param>
    /// <returns>True si le rôle est utilisateur final</returns>
    public static bool IsEndUser(this UserRole role)
    {
        return role is UserRole.Client or UserRole.Provider;
    }

    /// <summary>
    /// Obtient la description du rôle
    /// </summary>
    /// <param name="role">Le rôle</param>
    /// <returns>Description du rôle</returns>
    public static string GetDescription(this UserRole role)
    {
        return role switch
        {
            UserRole.Client => "Client - Recherche et réserve des services",
            UserRole.Provider => "Prestataire - Fournit des services à domicile",
            UserRole.Admin => "Administrateur - Accès complet au système",
            UserRole.Support => "Support - Assistance aux utilisateurs",
            UserRole.Manager => "Manager - Gestion des réclamations et litiges",
            UserRole.Supervisor => "Superviseur - Validation des prestataires",
            _ => "Rôle inconnu"
        };
    }

    /// <summary>
    /// Obtient les permissions associées au rôle
    /// </summary>
    /// <param name="role">Le rôle</param>
    /// <returns>Liste des permissions</returns>
    public static IEnumerable<string> GetPermissions(this UserRole role)
    {
        return role switch
        {
            UserRole.Client => new[]
            {
                "search_providers",
                "create_booking",
                "make_payment",
                "leave_review",
                "view_own_bookings",
                "cancel_booking"
            },
            UserRole.Provider => new[]
            {
                "manage_profile",
                "manage_services",
                "accept_booking",
                "receive_payment",
                "view_own_bookings",
                "respond_to_reviews"
            },
            UserRole.Admin => new[]
            {
                "manage_all_users",
                "manage_all_bookings",
                "manage_all_payments",
                "system_configuration",
                "view_analytics",
                "manage_categories",
                "manage_complaints",
                "moderate_content"
            },
            UserRole.Support => new[]
            {
                "view_user_data",
                "manage_support_tickets",
                "chat_with_users",
                "view_basic_analytics",
                "escalate_issues"
            },
            UserRole.Manager => new[]
            {
                "manage_complaints",
                "arbitrate_disputes",
                "apply_sanctions",
                "view_complaint_analytics",
                "manage_refunds"
            },
            UserRole.Supervisor => new[]
            {
                "validate_provider_documents",
                "approve_provider_profiles",
                "moderate_services",
                "suspend_providers",
                "view_provider_analytics"
            },
            _ => Array.Empty<string>()
        };
    }

    /// <summary>
    /// Vérifie si un rôle a une permission spécifique
    /// </summary>
    /// <param name="role">Le rôle</param>
    /// <param name="permission">La permission à vérifier</param>
    /// <returns>True si le rôle a la permission</returns>
    public static bool HasPermission(this UserRole role, string permission)
    {
        // L'admin a toutes les permissions
        if (role == UserRole.Admin)
            return true;

        return role.GetPermissions().Contains(permission, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Obtient le niveau de priorité du rôle (plus le nombre est élevé, plus la priorité est haute)
    /// </summary>
    /// <param name="role">Le rôle</param>
    /// <returns>Niveau de priorité</returns>
    public static int GetPriorityLevel(this UserRole role)
    {
        return role switch
        {
            UserRole.Admin => 100,
            UserRole.Manager => 80,
            UserRole.Supervisor => 70,
            UserRole.Support => 60,
            UserRole.Provider => 20,
            UserRole.Client => 10,
            _ => 0
        };
    }
}
