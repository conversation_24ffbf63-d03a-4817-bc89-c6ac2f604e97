using ServiceLink.Infrastructure.Hubs;

namespace ServiceLink.API.Extensions;

/// <summary>
/// Extensions pour la configuration des hubs SignalR
/// </summary>
public static class SignalRExtensions
{
    /// <summary>
    /// Configure les routes des hubs SignalR
    /// </summary>
    /// <param name="app">Application builder</param>
    public static void MapSignalRHubs(this WebApplication app)
    {
        // Configuration des routes des hubs
        app.MapHub<NotificationHub>("/hubs/notifications", options =>
        {
            options.Transports = Microsoft.AspNetCore.Http.Connections.HttpTransportType.WebSockets |
                                Microsoft.AspNetCore.Http.Connections.HttpTransportType.LongPolling;
        });

        app.MapHub<BookingHub>("/hubs/bookings", options =>
        {
            options.Transports = Microsoft.AspNetCore.Http.Connections.HttpTransportType.WebSockets |
                                Microsoft.AspNetCore.Http.Connections.HttpTransportType.LongPolling;
        });

        app.MapHub<ChatHub>("/hubs/chat", options =>
        {
            options.Transports = Microsoft.AspNetCore.Http.Connections.HttpTransportType.WebSockets |
                                Microsoft.AspNetCore.Http.Connections.HttpTransportType.LongPolling;
        });
    }
}

/// <summary>
/// Constantes pour la configuration SignalR
/// </summary>
public static class SignalRConstants
{
    /// <summary>
    /// Routes des hubs
    /// </summary>
    public static class HubRoutes
    {
        public const string Notifications = "/hubs/notifications";
        public const string Bookings = "/hubs/bookings";
        public const string Chat = "/hubs/chat";
    }

    /// <summary>
    /// Méthodes des hubs côté client
    /// </summary>
    public static class ClientMethods
    {
        // Méthodes communes
        public const string ReceiveNotification = "ReceiveNotification";
        public const string Error = "Error";
        public const string Success = "Success";
        public const string Welcome = "Welcome";
        public const string Pong = "Pong";

        // Méthodes de chat
        public const string ReceiveMessage = "ReceiveMessage";
        public const string UserStartedTyping = "UserStartedTyping";
        public const string UserStoppedTyping = "UserStoppedTyping";
        public const string MessageMarkedAsRead = "MessageMarkedAsRead";
        public const string UserJoinedConversation = "UserJoinedConversation";
        public const string UserLeftConversation = "UserLeftConversation";

        // Méthodes de réservation
        public const string ReceiveBookingNotification = "ReceiveBookingNotification";
        public const string PendingBookingsAvailable = "PendingBookingsAvailable";

        // Méthodes d'administration
        public const string UserConnected = "UserConnected";
        public const string UserDisconnected = "UserDisconnected";
        public const string ConnectedUsersList = "ConnectedUsersList";
        public const string OnlineUsersList = "OnlineUsersList";
    }

    /// <summary>
    /// Méthodes des hubs côté serveur
    /// </summary>
    public static class ServerMethods
    {
        // Méthodes communes
        public const string JoinGroup = "JoinGroup";
        public const string LeaveGroup = "LeaveGroup";
        public const string Ping = "Ping";

        // Méthodes de notifications
        public const string SendPersonalNotification = "SendPersonalNotification";
        public const string BroadcastToRole = "BroadcastToRole";
        public const string GetConnectedUsers = "GetConnectedUsers";

        // Méthodes de chat
        public const string JoinConversation = "JoinConversation";
        public const string LeaveConversation = "LeaveConversation";
        public const string SendMessage = "SendMessage";
        public const string MarkMessageAsRead = "MarkMessageAsRead";
        public const string StartTyping = "StartTyping";
        public const string StopTyping = "StopTyping";
        public const string GetOnlineUsers = "GetOnlineUsers";

        // Méthodes de réservation
        public const string JoinBookingGroup = "JoinBookingGroup";
        public const string LeaveBookingGroup = "LeaveBookingGroup";
        public const string NotifyNewBooking = "NotifyNewBooking";
        public const string ConfirmBooking = "ConfirmBooking";
        public const string RejectBooking = "RejectBooking";
        public const string CancelBooking = "CancelBooking";
        public const string UpdateBookingStatus = "UpdateBookingStatus";
    }
}
