using System.Text.RegularExpressions;

namespace ServiceLink.Domain.ValueObjects;

/// <summary>
/// Value Object représentant un numéro de téléphone valide
/// Support des formats internationaux et locaux
/// </summary>
public sealed class PhoneNumber : IEquatable<PhoneNumber>
{
    private static readonly Regex PhoneRegex = new(
        @"^\+?[1-9]\d{1,14}$",
        RegexOptions.Compiled);

    private static readonly Regex DigitsOnlyRegex = new(
        @"[^\d+]",
        RegexOptions.Compiled);

    /// <summary>
    /// Numéro de téléphone au format international
    /// </summary>
    public string Value { get; }

    /// <summary>
    /// Code pays (ex: +33 pour la France)
    /// </summary>
    public string CountryCode { get; }

    /// <summary>
    /// Numéro national (sans le code pays)
    /// </summary>
    public string NationalNumber { get; }

    /// <summary>
    /// Constructeur privé pour garantir la validation
    /// </summary>
    /// <param name="value">Numéro de téléphone formaté</param>
    /// <param name="countryCode">Code pays</param>
    /// <param name="nationalNumber">Numéro national</param>
    private PhoneNumber(string value, string countryCode, string nationalNumber)
    {
        Value = value;
        CountryCode = countryCode;
        NationalNumber = nationalNumber;
    }

    /// <summary>
    /// Crée une nouvelle instance de PhoneNumber après validation
    /// </summary>
    /// <param name="phoneNumber">Numéro de téléphone à valider</param>
    /// <param name="defaultCountryCode">Code pays par défaut si non spécifié</param>
    /// <returns>Instance de PhoneNumber valide</returns>
    /// <exception cref="ArgumentException">Si le numéro n'est pas valide</exception>
    public static PhoneNumber Create(string phoneNumber, string defaultCountryCode = "+33")
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ArgumentException("Le numéro de téléphone ne peut pas être vide.", nameof(phoneNumber));

        // Nettoyer le numéro (garder seulement les chiffres et le +)
        var cleaned = DigitsOnlyRegex.Replace(phoneNumber.Trim(), "");
        
        // Ajouter le + au début si absent et que le numéro ne commence pas par 0
        if (!cleaned.StartsWith('+') && !cleaned.StartsWith('0'))
        {
            cleaned = "+" + cleaned;
        }

        // Si le numéro commence par 0, remplacer par le code pays par défaut
        if (cleaned.StartsWith('0'))
        {
            cleaned = defaultCountryCode + cleaned[1..];
        }

        // Validation du format
        if (!PhoneRegex.IsMatch(cleaned))
            throw new ArgumentException($"Le numéro de téléphone '{phoneNumber}' n'est pas valide.", nameof(phoneNumber));

        // Validation de la longueur
        if (cleaned.Length < 8 || cleaned.Length > 16)
            throw new ArgumentException("Le numéro de téléphone doit contenir entre 8 et 15 chiffres.", nameof(phoneNumber));

        // Extraire le code pays et le numéro national
        var (countryCode, nationalNumber) = ExtractCountryCodeAndNational(cleaned);

        return new PhoneNumber(cleaned, countryCode, nationalNumber);
    }

    /// <summary>
    /// Tente de créer une instance de PhoneNumber
    /// </summary>
    /// <param name="phoneNumber">Numéro de téléphone à valider</param>
    /// <param name="result">Instance de PhoneNumber si valide</param>
    /// <param name="defaultCountryCode">Code pays par défaut</param>
    /// <returns>True si le numéro est valide</returns>
    public static bool TryCreate(string phoneNumber, out PhoneNumber? result, string defaultCountryCode = "+33")
    {
        try
        {
            result = Create(phoneNumber, defaultCountryCode);
            return true;
        }
        catch
        {
            result = null;
            return false;
        }
    }

    /// <summary>
    /// Extrait le code pays et le numéro national
    /// </summary>
    /// <param name="phoneNumber">Numéro complet</param>
    /// <returns>Tuple (code pays, numéro national)</returns>
    private static (string countryCode, string nationalNumber) ExtractCountryCodeAndNational(string phoneNumber)
    {
        // Codes pays courants (liste simplifiée)
        var countryCodes = new[]
        {
            "+1", "+7", "+20", "+27", "+30", "+31", "+32", "+33", "+34", "+36", "+39", "+40", "+41", "+43", "+44", "+45", "+46", "+47", "+48", "+49",
            "+51", "+52", "+53", "+54", "+55", "+56", "+57", "+58", "+60", "+61", "+62", "+63", "+64", "+65", "+66", "+81", "+82", "+84", "+86", "+90",
            "+91", "+92", "+93", "+94", "+95", "+98", "+212", "+213", "+216", "+218", "+220", "+221", "+222", "+223", "+224", "+225", "+226", "+227",
            "+228", "+229", "+230", "+231", "+232", "+233", "+234", "+235", "+236", "+237", "+238", "+239", "+240", "+241", "+242", "+243", "+244",
            "+245", "+246", "+247", "+248", "+249", "+250", "+251", "+252", "+253", "+254", "+255", "+256", "+257", "+258", "+260", "+261", "+262",
            "+263", "+264", "+265", "+266", "+267", "+268", "+269", "+290", "+291", "+297", "+298", "+299"
        };

        foreach (var code in countryCodes.OrderByDescending(c => c.Length))
        {
            if (phoneNumber.StartsWith(code))
            {
                return (code, phoneNumber[code.Length..]);
            }
        }

        // Si aucun code pays trouvé, considérer que c'est +1 par défaut
        return ("+1", phoneNumber[1..]);
    }

    /// <summary>
    /// Formate le numéro pour l'affichage selon le pays
    /// </summary>
    /// <returns>Numéro formaté</returns>
    public string ToFormattedString()
    {
        return CountryCode switch
        {
            "+33" => FormatFrench(),
            "+1" => FormatNorthAmerican(),
            "+44" => FormatUK(),
            "+49" => FormatGerman(),
            _ => Value
        };
    }

    /// <summary>
    /// Formate selon le standard français
    /// </summary>
    private string FormatFrench()
    {
        if (NationalNumber.Length == 9)
        {
            return $"{CountryCode} {NationalNumber[0]} {NationalNumber[1..3]} {NationalNumber[3..5]} {NationalNumber[5..7]} {NationalNumber[7..9]}";
        }
        return Value;
    }

    /// <summary>
    /// Formate selon le standard nord-américain
    /// </summary>
    private string FormatNorthAmerican()
    {
        if (NationalNumber.Length == 10)
        {
            return $"{CountryCode} ({NationalNumber[..3]}) {NationalNumber[3..6]}-{NationalNumber[6..]}";
        }
        return Value;
    }

    /// <summary>
    /// Formate selon le standard britannique
    /// </summary>
    private string FormatUK()
    {
        if (NationalNumber.Length == 10)
        {
            return $"{CountryCode} {NationalNumber[..4]} {NationalNumber[4..7]} {NationalNumber[7..]}";
        }
        return Value;
    }

    /// <summary>
    /// Formate selon le standard allemand
    /// </summary>
    private string FormatGerman()
    {
        if (NationalNumber.Length >= 10)
        {
            return $"{CountryCode} {NationalNumber[..3]} {NationalNumber[3..6]} {NationalNumber[6..]}";
        }
        return Value;
    }

    /// <summary>
    /// Obtient le nom du pays basé sur le code pays
    /// </summary>
    /// <returns>Nom du pays</returns>
    public string GetCountryName()
    {
        return CountryCode switch
        {
            "+1" => "États-Unis/Canada",
            "+33" => "France",
            "+44" => "Royaume-Uni",
            "+49" => "Allemagne",
            "+39" => "Italie",
            "+34" => "Espagne",
            "+32" => "Belgique",
            "+41" => "Suisse",
            "+31" => "Pays-Bas",
            "+46" => "Suède",
            "+47" => "Norvège",
            "+45" => "Danemark",
            "+358" => "Finlande",
            "+212" => "Maroc",
            "+213" => "Algérie",
            "+216" => "Tunisie",
            "+221" => "Sénégal",
            "+225" => "Côte d'Ivoire",
            "+237" => "Cameroun",
            "+243" => "République Démocratique du Congo",
            "+234" => "Nigeria",
            "+27" => "Afrique du Sud",
            _ => "Inconnu"
        };
    }

    /// <summary>
    /// Vérifie si le numéro est un mobile
    /// </summary>
    /// <returns>True si c'est probablement un mobile</returns>
    public bool IsMobile()
    {
        return CountryCode switch
        {
            "+33" => NationalNumber.StartsWith('6') || NationalNumber.StartsWith('7'),
            "+1" => true, // Difficile à déterminer pour l'Amérique du Nord
            "+44" => NationalNumber.StartsWith('7'),
            "+49" => NationalNumber.StartsWith("15") || NationalNumber.StartsWith("16") || NationalNumber.StartsWith("17"),
            _ => true // Par défaut, considérer comme mobile
        };
    }

    /// <summary>
    /// Masque le numéro pour l'affichage (ex: +33 6 ** ** ** 89)
    /// </summary>
    /// <returns>Numéro masqué</returns>
    public string ToMaskedString()
    {
        if (NationalNumber.Length <= 4)
            return $"{CountryCode} {NationalNumber[0]}***";

        var lastTwo = NationalNumber[^2..];
        var firstTwo = NationalNumber[..2];
        var masked = new string('*', NationalNumber.Length - 4);

        return $"{CountryCode} {firstTwo}{masked}{lastTwo}";
    }

    /// <summary>
    /// Conversion implicite vers string
    /// </summary>
    public static implicit operator string(PhoneNumber phone) => phone.Value;

    /// <summary>
    /// Conversion explicite depuis string
    /// </summary>
    public static explicit operator PhoneNumber(string phone) => Create(phone);

    /// <summary>
    /// Représentation string du numéro
    /// </summary>
    public override string ToString() => Value;

    /// <summary>
    /// Égalité basée sur la valeur
    /// </summary>
    public bool Equals(PhoneNumber? other)
    {
        return other is not null && Value == other.Value;
    }

    /// <summary>
    /// Égalité avec object
    /// </summary>
    public override bool Equals(object? obj)
    {
        return obj is PhoneNumber other && Equals(other);
    }

    /// <summary>
    /// Hash code basé sur la valeur
    /// </summary>
    public override int GetHashCode()
    {
        return Value.GetHashCode();
    }

    /// <summary>
    /// Opérateur d'égalité
    /// </summary>
    public static bool operator ==(PhoneNumber? left, PhoneNumber? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// Opérateur d'inégalité
    /// </summary>
    public static bool operator !=(PhoneNumber? left, PhoneNumber? right)
    {
        return !Equals(left, right);
    }
}
