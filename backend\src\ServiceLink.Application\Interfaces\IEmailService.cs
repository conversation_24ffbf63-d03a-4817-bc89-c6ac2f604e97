namespace ServiceLink.Application.Interfaces;

/// <summary>
/// Service pour l'envoi d'emails
/// </summary>
public interface IEmailService
{
    /// <summary>
    /// Envoie un email de confirmation d'inscription
    /// </summary>
    /// <param name="email">Adresse email du destinataire</param>
    /// <param name="firstName">Prénom du destinataire</param>
    /// <param name="confirmationToken">Token de confirmation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendWelcomeEmailAsync(string email, string firstName, string confirmationToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie un email de confirmation d'adresse email
    /// </summary>
    /// <param name="email">Adresse email du destinataire</param>
    /// <param name="firstName">Prénom du destinataire</param>
    /// <param name="confirmationToken">Token de confirmation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendEmailConfirmationAsync(string email, string firstName, string confirmationToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie un email de réinitialisation de mot de passe
    /// </summary>
    /// <param name="email">Adresse email du destinataire</param>
    /// <param name="firstName">Prénom du destinataire</param>
    /// <param name="resetToken">Token de réinitialisation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendPasswordResetEmailAsync(string email, string firstName, string resetToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie un email de notification de changement de mot de passe
    /// </summary>
    /// <param name="email">Adresse email du destinataire</param>
    /// <param name="firstName">Prénom du destinataire</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendPasswordChangedNotificationAsync(string email, string firstName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie un email de notification de verrouillage de compte
    /// </summary>
    /// <param name="email">Adresse email du destinataire</param>
    /// <param name="firstName">Prénom du destinataire</param>
    /// <param name="lockedUntil">Date de fin de verrouillage</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendAccountLockedNotificationAsync(string email, string firstName, DateTime lockedUntil, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie un email de notification de déverrouillage de compte
    /// </summary>
    /// <param name="email">Adresse email du destinataire</param>
    /// <param name="firstName">Prénom du destinataire</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendAccountUnlockedNotificationAsync(string email, string firstName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie un email de notification d'activation de 2FA
    /// </summary>
    /// <param name="email">Adresse email du destinataire</param>
    /// <param name="firstName">Prénom du destinataire</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendTwoFactorEnabledNotificationAsync(string email, string firstName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie un email de notification de désactivation de 2FA
    /// </summary>
    /// <param name="email">Adresse email du destinataire</param>
    /// <param name="firstName">Prénom du destinataire</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendTwoFactorDisabledNotificationAsync(string email, string firstName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie un email générique
    /// </summary>
    /// <param name="to">Adresse email du destinataire</param>
    /// <param name="subject">Sujet de l'email</param>
    /// <param name="htmlBody">Corps de l'email en HTML</param>
    /// <param name="textBody">Corps de l'email en texte brut (optionnel)</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendEmailAsync(string to, string subject, string htmlBody, string? textBody = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie un email avec template
    /// </summary>
    /// <param name="to">Adresse email du destinataire</param>
    /// <param name="templateName">Nom du template</param>
    /// <param name="templateData">Données pour le template</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email a été envoyé avec succès</returns>
    Task<bool> SendTemplatedEmailAsync(string to, string templateName, object templateData, CancellationToken cancellationToken = default);

    /// <summary>
    /// Valide une adresse email
    /// </summary>
    /// <param name="email">Adresse email à valider</param>
    /// <returns>True si l'adresse est valide</returns>
    bool IsValidEmail(string email);

    /// <summary>
    /// Vérifie si un domaine email est autorisé
    /// </summary>
    /// <param name="email">Adresse email</param>
    /// <returns>True si le domaine est autorisé</returns>
    bool IsAllowedEmailDomain(string email);
}
