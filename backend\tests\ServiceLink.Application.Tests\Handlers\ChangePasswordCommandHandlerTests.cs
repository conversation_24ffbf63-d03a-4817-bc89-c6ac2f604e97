using System;
using System.Threading;
using System.Threading.Tasks;
using Moq;
using Xunit;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Handlers;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;

public class ChangePasswordCommandHandlerTests
{
    [Fact]
    public async Task Handle_ValidCurrentPassword_ChangesPasswordAndReturnsTrue()
    {
        // Arrange
        var userRepository = new Mock<IUserRepository>();
        var passwordService = new Mock<IPasswordService>();
        var user = new User(
            ServiceLink.Domain.ValueObjects.Email.Create("<EMAIL>"),
            "Test",
            "User",
            ServiceLink.Domain.Enums.UserRole.Client,
            null,
            null
        );
        user.SetPassword("oldhash", "oldsalt");
        user.Activate();
        userRepository.Setup(r => r.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .Returns((Guid id, CancellationToken ct) => Task.FromResult(id == user.Id ? user : null));
        passwordService.Setup(p => p.VerifyPassword("current", "oldhash", "oldsalt")).Returns(true);
        passwordService.Setup(p => p.HashPassword("new")).Returns(("newhash", "newsalt"));
        userRepository.Setup(r => r.UpdateAsync(It.IsAny<User>(), It.IsAny<CancellationToken>())).Returns((User u, CancellationToken ct) => Task.CompletedTask);
        var handler = new ChangePasswordCommandHandler(userRepository.Object, passwordService.Object);
        var command = new ChangePasswordCommand { UserId = user.Id, CurrentPassword = "current", NewPassword = "new" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.True(result);
    }
    [Fact]
    public async Task Handle_InvalidCurrentPassword_ReturnsFalse()
    {
        // Arrange
        var userRepository = new Mock<IUserRepository>();
        var passwordService = new Mock<IPasswordService>();
        var user = new User(
            ServiceLink.Domain.ValueObjects.Email.Create("<EMAIL>"),
            "Test",
            "User",
            ServiceLink.Domain.Enums.UserRole.Client,
            null,
            null
        );
        user.SetPassword("oldhash", "oldsalt");
        user.Activate();
        userRepository.Setup(r => r.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .Returns((Guid id, CancellationToken ct) => Task.FromResult<User?>(null));
        passwordService.Setup(p => p.VerifyPassword("wrong", "oldhash", "oldsalt")).Returns(false);
        var handler = new ChangePasswordCommandHandler(userRepository.Object, passwordService.Object);
        var command = new ChangePasswordCommand { UserId = user.Id, CurrentPassword = "wrong", NewPassword = "new" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.False(result);
    }
}
