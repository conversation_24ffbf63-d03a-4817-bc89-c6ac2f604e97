import type { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  RegisterResponse,
  RefreshTokenResponse,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
  UpdateProfileRequest,
  User
} from '../types/auth'
import { get, post, put } from '../lib/api'

export const authService = {
  // Connexion
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await post<LoginResponse>('/auth/login', credentials)

    // Stocker les tokens
    if (response.token) {
      localStorage.setItem('token', response.token)
      localStorage.setItem('refreshToken', response.refreshToken)
    }

    return response
  },

  // Inscription
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    return await post<RegisterResponse>('/auth/register', data)
  },

  // Déconnexion
  async logout(): Promise<void> {
    try {
      await post('/auth/logout')
    } finally {
      // Nettoyer le localStorage même si l'appel API échoue
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
    }
  },

  // Refresh token
  async refreshToken(): Promise<RefreshTokenResponse> {
    const refreshToken = localStorage.getItem('refreshToken')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await post<RefreshTokenResponse>('/auth/refresh', {
      refreshToken,
    })

    // Mettre à jour les tokens
    localStorage.setItem('token', response.token)
    localStorage.setItem('refreshToken', response.refreshToken)

    return response
  },

  // Mot de passe oublié
  async forgotPassword(data: ForgotPasswordRequest): Promise<void> {
    await post('/auth/forgot-password', data)
  },

  // Réinitialiser le mot de passe
  async resetPassword(data: ResetPasswordRequest): Promise<void> {
    await post('/auth/reset-password', data)
  },

  // Changer le mot de passe
  async changePassword(data: ChangePasswordRequest): Promise<void> {
    await post('/auth/change-password', data)
  },

  // Confirmer l'email
  async confirmEmail(token: string): Promise<void> {
    await post('/auth/confirm-email', { token })
  },

  // Renvoyer l'email de confirmation
  async resendConfirmationEmail(): Promise<void> {
    await post('/auth/resend-confirmation')
  },

  // Obtenir le profil utilisateur
  async getProfile(): Promise<User> {
    return await get<User>('/auth/profile')
  },

  // Mettre à jour le profil
  async updateProfile(data: UpdateProfileRequest): Promise<User> {
    return await put<User>('/auth/profile', data)
  },

  // Vérifier si l'utilisateur est connecté
  isAuthenticated(): boolean {
    const token = localStorage.getItem('token')
    if (!token) return false

    try {
      // Vérifier si le token n'est pas expiré
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Date.now() / 1000
      return payload.exp > currentTime
    } catch {
      return false
    }
  },

  // Obtenir le token actuel
  getToken(): string | null {
    return localStorage.getItem('token')
  },

  // Obtenir les informations utilisateur depuis le token
  getUserFromToken(): User | null {
    const token = localStorage.getItem('token')
    if (!token) return null

    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return {
        id: payload.sub || payload.userId,
        email: payload.email,
        firstName: payload.firstName || payload.given_name,
        lastName: payload.lastName || payload.family_name,
        role: payload.role,
        isEmailConfirmed: payload.emailConfirmed === 'true' || payload.emailConfirmed === true,
        createdAt: payload.createdAt || new Date().toISOString(),
      }
    } catch {
      return null
    }
  },
}
