import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, MoreHorizontal, Edit, Trash2, Shield, UserCheck, UserX } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useAuthStore } from '@/stores/authStore';
import { mapFrontendRoleToBackend } from '@/lib/roleMapping';
import { toast } from 'sonner';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: number; // Le backend retourne un nombre (1=Client, 2=Provider, 3=Admin, etc.)
  status: 'Active' | 'Suspended' | 'Pending';
  createdAt: string;
  lastLogin?: string;
}

// Mock data pour les utilisateurs existants
const mockUsers: User[] = [
  {
    id: '1',
    firstName: 'Sophie',
    lastName: 'Dubois',
    email: '<EMAIL>',
    role: 1, // Client
    status: 'Active',
    createdAt: '2025-07-11',
    lastLogin: '2025-07-11 14:30'
  },
  {
    id: '2',
    firstName: 'Pierre',
    lastName: 'Martin',
    email: '<EMAIL>',
    role: 2, // Provider
    status: 'Active',
    createdAt: '2025-07-11',
    lastLogin: '2025-07-11 15:45'
  },
  {
    id: '3',
    firstName: 'Admin',
    lastName: 'Global',
    email: '<EMAIL>',
    role: 3, // Admin
    status: 'Active',
    createdAt: '2025-01-01',
    lastLogin: '2025-07-11 16:00'
  }
];

export const AdminUsers: React.FC = () => {
  const { user, token } = useAuthStore();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newUser, setNewUser] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    role: '',
  });

  // Charger les utilisateurs depuis le backend avec pagination
  const loadUsers = async (page = 1, pageSize = 50) => {
    try {
      setIsLoading(true);
      const response = await fetch(`https://localhost:7276/api/users?page=${page}&pageSize=${pageSize}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des utilisateurs');
      }

      const data = await response.json();
      console.log('Utilisateurs chargés:', data);
      console.log('🔍 Premier utilisateur détaillé:', data.users?.[0]);
      console.log('🔍 Admin user détaillé:', data.users?.find((u: any) => u.email === '<EMAIL>'));

      // Mapper les données du backend vers l'interface frontend
      const mappedUsers = (data.users || []).map((user: any) => ({
        ...user,
        lastLogin: user.lastLoginAt || user.lastLogin // Mapper lastLoginAt vers lastLogin
      }));

      setUsers(mappedUsers);
    } catch (error) {
      console.error('Erreur chargement utilisateurs:', error);
      toast.error('Erreur lors du chargement des utilisateurs');
      // Fallback vers les données mock en cas d'erreur
      setUsers(mockUsers);
    } finally {
      setIsLoading(false);
    }
  };

  // Charger les utilisateurs au montage du composant
  useEffect(() => {
    if (token) {
      loadUsers();
    }
  }, [token]);

  // Filtrer les utilisateurs selon les critères de recherche et filtres
  const filteredUsers = users.filter(user => {
    // Filtre par terme de recherche (nom, prénom, email)
    const matchesSearch = searchTerm === '' ||
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    // Filtre par rôle
    const matchesRole = roleFilter === 'all' || user.role.toString() === roleFilter;

    // Filtre par statut
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;

    return matchesSearch && matchesRole && matchesStatus;
  });

  // Fonction pour obtenir le nom du rôle
  const getRoleName = (role: number): string => {
    const roleMap: { [key: number]: string } = {
      1: 'Client',
      2: 'Provider',
      3: 'Admin',
      4: 'Support',
      5: 'Manager',
      6: 'Supervisor'
    };
    return roleMap[role] || 'Inconnu';
  };

  // Fonction pour obtenir la couleur du badge selon le rôle
  const getRoleBadgeVariant = (role: number): "default" | "secondary" | "destructive" | "outline" => {
    switch (role) {
      case 3: return "destructive"; // Admin - rouge
      case 5: return "default"; // Manager - bleu
      case 4: return "secondary"; // Support - gris
      case 6: return "outline"; // Supervisor - outline
      case 2: return "default"; // Provider - bleu
      case 1: return "secondary"; // Client - gris
      default: return "outline";
    }
  };

  // Fonction pour formater les dates
  const formatDate = (dateString: string): string => {
    if (!dateString || dateString === 'Jamais') return 'Jamais';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Date invalide';
    }
  };



  const handleCreateUser = async () => {
    if (!newUser.firstName || !newUser.lastName || !newUser.email || !newUser.password || !newUser.role) {
      toast.error('Veuillez remplir tous les champs');
      return;
    }

    try {
      setIsLoading(true);

      // Convertir le rôle frontend en numéro backend
      const backendRole = mapFrontendRoleToBackend(newUser.role as any);

      console.log('🚀 Création utilisateur:', {
        email: newUser.email,
        role: newUser.role,
        backendRole,
      });

      const response = await fetch('https://localhost:7276/api/users/register', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: newUser.firstName,
          lastName: newUser.lastName,
          email: newUser.email,
          password: newUser.password,
          confirmPassword: newUser.password,
          role: backendRole,
          acceptTerms: true,
          language: 'fr-FR',
          timeZone: 'Europe/Paris',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la création du compte');
      }

      const createdUser = await response.json();
      console.log('✅ Utilisateur créé:', createdUser);

      // Recharger la liste des utilisateurs
      await loadUsers();

      // Réinitialiser le formulaire
      setNewUser({ firstName: '', lastName: '', email: '', password: '', role: '' });
      setIsCreateDialogOpen(false);

      toast.success(`Compte ${newUser.role} créé avec succès pour ${newUser.email}`);

    } catch (error: any) {
      console.error('❌ Erreur création utilisateur:', error);
      toast.error(error.message || 'Erreur lors de la création du compte');
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour éditer un utilisateur
  const handleEditUser = async (userId: string, updatedData: Partial<User>) => {
    try {
      const response = await fetch(`https://localhost:7276/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedData),
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la modification');
      }

      toast.success('Utilisateur modifié avec succès');
      await loadUsers(); // Recharger la liste
    } catch (error) {
      console.error('Erreur modification utilisateur:', error);
      toast.error('Erreur lors de la modification');
    }
  };

  // Fonction pour supprimer un utilisateur
  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
      return;
    }

    try {
      const response = await fetch(`https://localhost:7276/api/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression');
      }

      toast.success('Utilisateur supprimé avec succès');
      await loadUsers(); // Recharger la liste
    } catch (error) {
      console.error('Erreur suppression utilisateur:', error);
      toast.error('Erreur lors de la suppression');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Utilisateurs</h1>
          <p className="text-gray-600">Créez et gérez tous les comptes utilisateurs de la plateforme</p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-green-600 hover:bg-green-700">
              <Plus className="h-4 w-4 mr-2" />
              Créer un Utilisateur
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Créer un Nouveau Compte</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">Prénom</Label>
                  <Input
                    id="firstName"
                    value={newUser.firstName}
                    onChange={(e) => setNewUser({ ...newUser, firstName: e.target.value })}
                    placeholder="Jean"
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Nom</Label>
                  <Input
                    id="lastName"
                    value={newUser.lastName}
                    onChange={(e) => setNewUser({ ...newUser, lastName: e.target.value })}
                    placeholder="Dupont"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <Label htmlFor="password">Mot de passe</Label>
                <Input
                  id="password"
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                  placeholder="Mot de passe sécurisé"
                />
              </div>
              
              <div>
                <Label htmlFor="role">Rôle</Label>
                <Select value={newUser.role} onValueChange={(value) => setNewUser({ ...newUser, role: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un rôle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Manager">Manager</SelectItem>
                    <SelectItem value="Support">Support</SelectItem>
                    <SelectItem value="Supervisor">Superviseur</SelectItem>
                    <SelectItem value="Client">Client</SelectItem>
                    <SelectItem value="Provider">Prestataire</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)} disabled={isLoading}>
                  Annuler
                </Button>
                <Button
                  onClick={handleCreateUser}
                  className="bg-green-600 hover:bg-green-700"
                  disabled={isLoading}
                >
                  {isLoading ? 'Création...' : 'Créer le Compte'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Utilisateurs</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.length}</div>
            <p className="text-xs text-muted-foreground">Tous rôles confondus</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Administratifs</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {users.filter(u => [3, 4, 5, 6].includes(u.role)).length}
            </div>
            <p className="text-xs text-muted-foreground">Admin, Manager, Support, Superviseur</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Prestataires</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.filter(u => u.role === 2).length}</div>
            <p className="text-xs text-muted-foreground">Fournisseurs de services</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clients</CardTitle>
            <UserX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.filter(u => u.role === 1).length}</div>
            <p className="text-xs text-muted-foreground">Utilisateurs finaux</p>
          </CardContent>
        </Card>
      </div>

      {/* Filtres et Recherche */}
      <Card>
        <CardHeader>
          <CardTitle>Filtres et Recherche</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher par nom ou email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filtrer par rôle" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les rôles</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="Manager">Manager</SelectItem>
                <SelectItem value="Support">Support</SelectItem>
                <SelectItem value="Supervisor">Superviseur</SelectItem>
                <SelectItem value="Provider">Prestataire</SelectItem>
                <SelectItem value="Client">Client</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filtrer par statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les statuts</SelectItem>
                <SelectItem value="Active">Actif</SelectItem>
                <SelectItem value="Suspended">Suspendu</SelectItem>
                <SelectItem value="Pending">En attente</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Table des Utilisateurs */}
      <Card>
        <CardHeader>
          <CardTitle>Liste des Utilisateurs ({filteredUsers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nom</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Rôle</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Créé le</TableHead>
                <TableHead>Dernière connexion</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">
                    {user.firstName} {user.lastName}
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role)}>
                      {getRoleName(user.role)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.status === 'Active' ? 'default' : user.status === 'Suspended' ? 'destructive' : 'secondary'}>
                      {user.status || 'Active'}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(user.createdAt)}</TableCell>
                  <TableCell>{formatDate(user.lastLogin || 'Jamais')}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditUser(user.id, { status: user.status === 'Active' ? 'Suspended' : 'Active' })}>
                          {user.status === 'Active' ? (
                            <>
                              <UserX className="mr-2 h-4 w-4" />
                              Suspendre
                            </>
                          ) : (
                            <>
                              <UserCheck className="mr-2 h-4 w-4" />
                              Activer
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditUser(user.id, { role: user.role === 3 ? 1 : 3 })}>
                          <Shield className="mr-2 h-4 w-4" />
                          {user.role === 3 ? 'Retirer Admin' : 'Promouvoir Admin'}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Supprimer
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};
