# Guide des Services de Notification Externes

## Vue d'ensemble

ServiceLink intègre trois services de notification externes majeurs pour couvrir tous les canaux de communication :

- **SendGrid** : Emails transactionnels et marketing
- **Twilio** : SMS et communications téléphoniques
- **Firebase** : Push notifications mobiles

## Architecture

### Interface Unifiée

Tous les services implémentent `IExternalNotificationService` :

```csharp
public interface IExternalNotificationService
{
    ExternalNotificationType ServiceType { get; }
    Task<ExternalNotificationResult> SendNotificationAsync(ExternalNotificationRequest request, CancellationToken cancellationToken = default);
    Task<IEnumerable<ExternalNotificationResult>> SendBulkNotificationsAsync(IEnumerable<ExternalNotificationRequest> requests, CancellationToken cancellationToken = default);
    Task<ExternalNotificationStatus> GetNotificationStatusAsync(string notificationId, CancellationToken cancellationToken = default);
    bool ValidateDestination(string destination);
}
```

### Factory Pattern

`ExternalNotificationServiceFactory` gère la création et la sélection automatique des services :

```csharp
var factory = serviceProvider.GetService<IExternalNotificationServiceFactory>();
var result = await factory.SendNotificationAsync(request);
```

## Configuration

### appsettings.json

```json
{
  "ExternalNotifications": {
    "EnabledServices": ["Email", "SMS", "Push"],
    "DefaultEmailService": "SendGrid",
    "DefaultSmsService": "Twilio",
    "DefaultPushService": "Firebase",
    "MaxRetryAttempts": 3,
    "RetryDelaySeconds": 30,
    "EnableTemplates": true,
    "EnableBulkSending": true,
    "MaxBulkSize": 100,
    "EnableFallback": true,
    "FallbackConfiguration": {
      "Email": ["SMS"],
      "SMS": ["Push"],
      "Push": ["Email"]
    }
  },

  "SendGrid": {
    "ApiKey": "YOUR_SENDGRID_API_KEY",
    "FromEmail": "<EMAIL>",
    "FromName": "ServiceLink",
    "ReplyToEmail": "<EMAIL>",
    "EnableOpenTracking": true,
    "EnableClickTracking": true,
    "EnableWebhooks": true,
    "WebhookUrl": "https://your-domain.com/api/webhooks/notifications/sendgrid",
    "RateLimitPerMinute": 100
  },

  "Twilio": {
    "AccountSid": "YOUR_TWILIO_ACCOUNT_SID",
    "AuthToken": "YOUR_TWILIO_AUTH_TOKEN",
    "FromPhoneNumber": "+***********",
    "MaxSmsLength": 160,
    "DelayBetweenSms": 100,
    "DelayBetweenBulk": 1000,
    "EnableStatusWebhooks": true,
    "StatusWebhookUrl": "https://your-domain.com/api/webhooks/notifications/twilio",
    "RateLimitPerMinute": 60,
    "EnableNumberValidation": true,
    "DefaultCountryCode": "+33"
  },

  "Firebase": {
    "ProjectId": "your-firebase-project-id",
    "ServiceAccountKeyPath": "path/to/firebase-service-account.json",
    "ServiceAccountKeyJson": "",
    "DefaultIcon": "ic_notification",
    "DefaultColor": "#FF6B35",
    "EnableAnalytics": true,
    "RateLimitPerMinute": 600,
    "DefaultTtl": 3600,
    "EnableCompression": true
  }
}
```

## Utilisation

### Envoi Simple

```csharp
var request = new ExternalNotificationRequest
{
    Type = ExternalNotificationType.Email,
    Recipients = new List<string> { "<EMAIL>" },
    Subject = "Bienvenue sur ServiceLink",
    Content = "Merci de vous être inscrit !",
    Priority = ExternalNotificationPriority.Normal
};

var result = await notificationFactory.SendNotificationAsync(request);
```

### Avec Template

```csharp
var request = new ExternalNotificationRequest
{
    Type = ExternalNotificationType.Email,
    Recipients = new List<string> { "<EMAIL>" },
    TemplateName = "welcome",
    TemplateData = new Dictionary<string, object>
    {
        ["userName"] = "John Doe",
        ["activationLink"] = "https://servicelink.com/activate/123"
    },
    Language = "fr"
};

var result = await notificationFactory.SendNotificationAsync(request);
```

### Envoi Multicanal

```csharp
var emailRequest = new ExternalNotificationRequest
{
    Type = ExternalNotificationType.Email,
    Recipients = new List<string> { "<EMAIL>" },
    Subject = "Nouvelle réservation",
    Content = "Votre réservation a été confirmée."
};

var smsRequest = new ExternalNotificationRequest
{
    Type = ExternalNotificationType.SMS,
    Recipients = new List<string> { "+***********" },
    Content = "Réservation confirmée. Détails par email."
};

var pushRequest = new ExternalNotificationRequest
{
    Type = ExternalNotificationType.Push,
    Recipients = new List<string> { "fcm_token_123" },
    Subject = "Réservation confirmée",
    Content = "Votre réservation a été confirmée."
};

var result = await factory.SendMultiChannelNotificationAsync(
    emailRequest, smsRequest, pushRequest);
```

## Templates

### Système de Templates

Le système de templates utilise une syntaxe simple avec des variables entre accolades :

```html
<h1>Bienvenue {{userName}} !</h1>
<p>Votre compte a été créé le {{currentDate}}.</p>
<p>Cliquez <a href="{{activationLink}}">ici</a> pour activer votre compte.</p>
```

### Variables Système

Variables automatiquement disponibles :
- `{{currentDate}}` : Date actuelle (dd/MM/yyyy)
- `{{currentTime}}` : Heure actuelle (HH:mm)
- `{{currentYear}}` : Année actuelle
- `{{platformName}}` : "ServiceLink"
- `{{supportEmail}}` : "<EMAIL>"
- `{{websiteUrl}}` : "https://servicelink.com"

### Templates Prédéfinis

- **welcome** : Email de bienvenue
- **booking_confirmation** : Confirmation de réservation
- **booking_reminder** : Rappel de réservation (SMS)
- **new_message** : Nouvelle message (Push)

## API Endpoints

### Envoi de Notifications

```http
POST /api/externalnotification/send
Content-Type: application/json

{
  "type": "Email",
  "recipients": ["<EMAIL>"],
  "subject": "Test",
  "content": "Message de test",
  "priority": "Normal"
}
```

### Envoi en Lot

```http
POST /api/externalnotification/send-bulk
Content-Type: application/json

[
  {
    "type": "Email",
    "recipients": ["<EMAIL>"],
    "subject": "Test 1",
    "content": "Message 1"
  },
  {
    "type": "SMS",
    "recipients": ["+***********"],
    "content": "Message SMS"
  }
]
```

### Vérification de Statut

```http
GET /api/externalnotification/status/Email/msg_123456789
```

### Gestion des Templates

```http
GET /api/externalnotification/templates
GET /api/externalnotification/templates/welcome?language=fr
POST /api/externalnotification/templates
POST /api/externalnotification/templates/welcome/test
```

## Webhooks

### Configuration des Webhooks

Les webhooks permettent de recevoir les mises à jour de statut des providers :

- **SendGrid** : `/api/webhooks/notifications/sendgrid`
- **Twilio** : `/api/webhooks/notifications/twilio`
- **Firebase** : `/api/webhooks/notifications/firebase`

### Événements Supportés

- **delivered** : Message délivré
- **opened** : Email ouvert
- **clicked** : Lien cliqué
- **bounced** : Email rejeté
- **failed** : Échec de livraison
- **unsubscribed** : Désabonnement

## Monitoring et Health Checks

### Health Checks

```http
GET /health
```

Vérifie la disponibilité de tous les services de notification.

### Métriques

- Taux de livraison par provider
- Temps de réponse moyen
- Nombre d'erreurs par type
- Utilisation des quotas

## Sécurité

### Validation des Webhooks

Tous les webhooks sont validés avec des signatures cryptographiques :

- **SendGrid** : Signature HMAC-SHA256
- **Twilio** : Signature X-Twilio-Signature
- **Firebase** : JWT token validation

### Rate Limiting

- **SendGrid** : 100 emails/minute par défaut
- **Twilio** : 60 SMS/minute par défaut
- **Firebase** : 600 notifications/minute par défaut

### Données Sensibles

- Chiffrement des tokens d'accès
- Logs sans données personnelles
- Rotation automatique des clés

## Dépannage

### Erreurs Communes

1. **Configuration manquante** : Vérifier les clés API dans appsettings.json
2. **Quota dépassé** : Vérifier les limites de taux
3. **Webhook non reçu** : Vérifier les URLs et la connectivité
4. **Template non trouvé** : Vérifier le nom et la langue du template

### Logs

```csharp
// Activer les logs détaillés
"Logging": {
  "LogLevel": {
    "ServiceLink.Infrastructure.Services": "Debug"
  }
}
```

### Tests

```bash
# Tester la connectivité
curl -X GET https://your-api.com/health

# Tester l'envoi d'email
curl -X POST https://your-api.com/api/externalnotification/send \
  -H "Content-Type: application/json" \
  -d '{"type":"Email","recipients":["<EMAIL>"],"subject":"Test","content":"Test message"}'
```

## Bonnes Pratiques

1. **Utiliser les templates** pour la cohérence
2. **Implémenter les fallbacks** pour la fiabilité
3. **Monitorer les webhooks** pour le suivi
4. **Respecter les quotas** des providers
5. **Tester en développement** avant la production
6. **Chiffrer les données sensibles**
7. **Implémenter la retry logic** pour les échecs temporaires
8. **Utiliser les health checks** pour la surveillance
