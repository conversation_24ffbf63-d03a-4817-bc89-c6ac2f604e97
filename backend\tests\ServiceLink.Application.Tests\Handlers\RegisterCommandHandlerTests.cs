using System;
using System.Threading;
using System.Threading.Tasks;
using Moq;
using Xunit;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Handlers;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;

public class RegisterCommandHandlerTests
{
    [Fact]
    public async Task Handle_ValidRequest_CreatesUserAndReturnsUserResponse()
    {
        // Arrange
        var unitOfWork = new Mock<IUnitOfWork>();
        var passwordService = new Mock<IPasswordService>();
        var emailService = new Mock<IEmailService>();
        var logger = new Mock<Microsoft.Extensions.Logging.ILogger<CreateUserCommandHandler>>();

        unitOfWork.Setup(u => u.Users.GetByEmailAsync(It.IsAny<ServiceLink.Domain.ValueObjects.Email>(), It.IsAny<CancellationToken>())).Returns((ServiceLink.Domain.ValueObjects.Email email, CancellationToken ct) => Task.FromResult<User>(null));
        passwordService.Setup(p => p.HashPassword(It.IsAny<string>())).Returns(("hash", "salt"));
        emailService.Setup(e => e.SendWelcomeEmailAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>())).Returns((string a, string b, string c, CancellationToken d) => Task.FromResult(true));
        unitOfWork.Setup(u => u.Users.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>())).Returns((User u, CancellationToken ct) => Task.CompletedTask);

        var handler = new CreateUserCommandHandler(unitOfWork.Object, passwordService.Object, emailService.Object, logger.Object);
        var command = new RegisterCommand { Email = "<EMAIL>", Password = "password", FirstName = "Test", LastName = "User", Role = ServiceLink.Domain.Enums.UserRole.Client };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(command.Email, result.Email);
        Assert.Equal(command.FirstName, result.FirstName);
        Assert.Equal(command.LastName, result.LastName);
    }

    [Fact]
    public async Task Handle_ExistingEmail_ThrowsInvalidOperationException()
    {
        // Arrange
        var unitOfWork = new Mock<IUnitOfWork>();
        var passwordService = new Mock<IPasswordService>();
        var emailService = new Mock<IEmailService>();
        var logger = new Mock<Microsoft.Extensions.Logging.ILogger<CreateUserCommandHandler>>();

        var existingUser = new User(
            ServiceLink.Domain.ValueObjects.Email.Create("<EMAIL>"),
            "Test",
            "User",
            ServiceLink.Domain.Enums.UserRole.Client,
            null,
            null
        );
        unitOfWork.Setup(u => u.Users.GetByEmailAsync(It.IsAny<ServiceLink.Domain.ValueObjects.Email>(), It.IsAny<CancellationToken>())).Returns((ServiceLink.Domain.ValueObjects.Email email, CancellationToken ct) => Task.FromResult(existingUser));

        var handler = new CreateUserCommandHandler(unitOfWork.Object, passwordService.Object, emailService.Object, logger.Object);
        var command = new RegisterCommand { Email = "<EMAIL>", Password = "password", FirstName = "Test", LastName = "User", Role = ServiceLink.Domain.Enums.UserRole.Client };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => handler.Handle(command, CancellationToken.None));
    }
}
