import React, { useState, useEffect } from 'react';
import { Calendar, Clock, AlertCircle, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useBookingStore, useServiceStore } from '@/stores';
import type { Service } from '@/stores/serviceStore';
import type { CreateBookingRequest } from '@/stores/bookingStore';

interface BookingFormProps {
  service: Service;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const BookingForm: React.FC<BookingFormProps> = ({ 
  service, 
  onSuccess, 
  onCancel 
}) => {
  const { createBooking, isLoading, error } = useBookingStore();
  const { checkAvailability, calculatePrice } = useServiceStore();

  const [selectedDate, setSelectedDate] = useState<Date>();
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [duration, setDuration] = useState<number>(service.minDurationMinutes);
  const [isUrgent, setIsUrgent] = useState(false);
  const [notes, setNotes] = useState('');
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [estimatedPrice, setEstimatedPrice] = useState<number | null>(null);
  const [checkingAvailability, setCheckingAvailability] = useState(false);

  // Generate time slots
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 8; hour <= 20; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        slots.push(timeString);
      }
    }
    return slots;
  };

  const timeSlots = generateTimeSlots();

  // Generate duration options
  const generateDurationOptions = () => {
    const options = [];
    const step = 30; // 30 minutes step
    for (let minutes = service.minDurationMinutes; minutes <= service.maxDurationMinutes; minutes += step) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      const label = hours > 0 
        ? `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}min` : ''}`
        : `${minutes}min`;
      options.push({ value: minutes, label });
    }
    return options;
  };

  const durationOptions = generateDurationOptions();

  // Check availability when date, time, or duration changes
  useEffect(() => {
    if (selectedDate && selectedTime && duration) {
      const checkServiceAvailability = async () => {
        setCheckingAvailability(true);
        try {
          const [hours, minutes] = selectedTime.split(':').map(Number);
          const scheduledDate = new Date(selectedDate);
          scheduledDate.setHours(hours, minutes, 0, 0);

          const available = await checkAvailability(service.id, scheduledDate, duration);
          setIsAvailable(available);

          // Calculate price
          const price = await calculatePrice(service.id, duration, isUrgent);
          setEstimatedPrice(price);
        } catch (error) {
          console.error('Error checking availability:', error);
          setIsAvailable(false);
        } finally {
          setCheckingAvailability(false);
        }
      };

      checkServiceAvailability();
    }
  }, [selectedDate, selectedTime, duration, isUrgent, service.id, checkAvailability, calculatePrice]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedDate || !selectedTime || !isAvailable) {
      return;
    }

    try {
      const [hours, minutes] = selectedTime.split(':').map(Number);
      const scheduledDate = new Date(selectedDate);
      scheduledDate.setHours(hours, minutes, 0, 0);

      const bookingRequest: CreateBookingRequest = {
        serviceId: service.id,
        scheduledDate,
        durationMinutes: duration,
        isUrgent,
        notes: notes.trim() || undefined,
      };

      await createBooking(bookingRequest);
      onSuccess?.();
    } catch (error) {
      console.error('Error creating booking:', error);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(price);
  };

  const isFormValid = selectedDate && selectedTime && isAvailable && !checkingAvailability;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>Book Service: {service.name}</span>
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Date Selection */}
          <div>
            <Label>Select Date</Label>
            <div className="mt-2">
              <CalendarComponent
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                disabled={(date) => date < new Date() || date < new Date(Date.now() + service.minAdvanceHours * 60 * 60 * 1000)}
                className="rounded-md border"
              />
            </div>
          </div>

          {/* Time Selection */}
          {selectedDate && (
            <div>
              <Label htmlFor="time">Select Time</Label>
              <Select value={selectedTime} onValueChange={setSelectedTime}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a time slot" />
                </SelectTrigger>
                <SelectContent>
                  {timeSlots.map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Duration Selection */}
          {selectedTime && (
            <div>
              <Label htmlFor="duration">Duration</Label>
              <Select value={duration.toString()} onValueChange={(value) => setDuration(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {durationOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Urgent Booking */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="urgent"
              checked={isUrgent}
              onChange={(e) => setIsUrgent(e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor="urgent" className="flex items-center space-x-2">
              <span>Urgent booking</span>
              <Badge variant="secondary">+20% surcharge</Badge>
            </Label>
          </div>

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Additional Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Any special requirements or notes for the service provider..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          {/* Availability Status */}
          {selectedDate && selectedTime && (
            <div className="p-4 rounded-lg border">
              {checkingAvailability ? (
                <div className="flex items-center space-x-2 text-muted-foreground">
                  <Clock className="h-4 w-4 animate-spin" />
                  <span>Checking availability...</span>
                </div>
              ) : isAvailable ? (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-green-600">
                    <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                    <span className="font-medium">Available</span>
                  </div>
                  {estimatedPrice && (
                    <div className="flex items-center space-x-2 text-foreground">
                      <DollarSign className="h-4 w-4" />
                      <span>Estimated total: {formatPrice(estimatedPrice)}</span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center space-x-2 text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span>Not available at this time</span>
                </div>
              )}
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="p-4 rounded-lg bg-red-50 border border-red-200 text-red-700">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!isFormValid || isLoading}
              className="flex-1"
            >
              {isLoading ? 'Creating Booking...' : 'Confirm Booking'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
