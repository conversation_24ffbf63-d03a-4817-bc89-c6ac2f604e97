import React from 'react'
import { User, Briefcase, Check } from 'lucide-react'
import { Button } from '../ui/Button'

interface UserTypeSelectionProps {
  selectedType: 'User' | 'Provider' | null
  onSelect: (type: 'User' | 'Provider') => void
  onContinue: () => void
}

export const UserTypeSelection: React.FC<UserTypeSelectionProps> = ({
  selectedType,
  onSelect,
  onContinue,
}) => {
  const userTypes = [
    {
      type: 'User' as const,
      title: 'Client',
      description: 'Je souhaite réserver des services',
      icon: User,
      features: [
        'Rechercher et réserver des services',
        'Gérer mes réservations',
        'Laisser des avis',
        'Messagerie avec les prestataires',
      ],
    },
    {
      type: 'Provider' as const,
      title: 'Prestataire',
      description: 'Je souhaite proposer mes services',
      icon: Briefcase,
      features: [
        'Créer et gérer mes services',
        'Recevoir des réservations',
        '<PERSON><PERSON>rer mon planning',
        'Communiquer avec les clients',
      ],
    },
  ]

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Rejoignez ServiceLink
          </h2>
          <p className="text-lg text-gray-600">
            Choisissez le type de compte qui vous correspond
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {userTypes.map((userType) => {
            const Icon = userType.icon
            const isSelected = selectedType === userType.type
            
            return (
              <div
                key={userType.type}
                className={`
                  relative p-6 rounded-lg border-2 cursor-pointer transition-all
                  ${isSelected
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }
                `}
                onClick={() => onSelect(userType.type)}
              >
                {isSelected && (
                  <div className="absolute top-4 right-4">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <Check className="w-4 h-4 text-white" />
                    </div>
                  </div>
                )}
                
                <div className="flex items-center mb-4">
                  <div className={`
                    w-12 h-12 rounded-lg flex items-center justify-center mr-4
                    ${isSelected ? 'bg-blue-500' : 'bg-gray-100'}
                  `}>
                    <Icon className={`w-6 h-6 ${isSelected ? 'text-white' : 'text-gray-600'}`} />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      {userType.title}
                    </h3>
                    <p className="text-gray-600">
                      {userType.description}
                    </p>
                  </div>
                </div>

                <ul className="space-y-2">
                  {userType.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            )
          })}
        </div>

        {selectedType && (
          <div className="text-center">
            <Button
              onClick={onContinue}
              size="lg"
              className="px-8"
            >
              Continuer en tant que {selectedType === 'User' ? 'Client' : 'Prestataire'}
            </Button>
          </div>
        )}

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Vous avez déjà un compte ?{' '}
            <button
              type="button"
              className="text-blue-600 hover:text-blue-500 font-medium"
              onClick={() => window.location.href = '/login'}
            >
              Se connecter
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
