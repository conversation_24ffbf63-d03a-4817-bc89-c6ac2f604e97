using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.Interfaces;

/// <summary>
/// Interface unifiée pour tous les services de paiement
/// Abstraction commune pour Stripe, PayPal, Flutterwave, etc.
/// </summary>
public interface IPaymentService
{
    /// <summary>
    /// Provider de paiement (Stripe, PayPal, Flutterwave)
    /// </summary>
    PaymentProvider Provider { get; }

    /// <summary>
    /// Crée une intention de paiement
    /// </summary>
    /// <param name="request">Détails de la demande de paiement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Réponse avec détails de l'intention de paiement</returns>
    Task<PaymentIntentResponse> CreatePaymentIntentAsync(CreatePaymentIntentRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Confirme un paiement
    /// </summary>
    /// <param name="paymentIntentId">ID de l'intention de paiement</param>
    /// <param name="paymentMethodId">ID de la méthode de paiement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de la confirmation</returns>
    Task<PaymentConfirmationResponse> ConfirmPaymentAsync(string paymentIntentId, string paymentMethodId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Récupère le statut d'un paiement
    /// </summary>
    /// <param name="paymentId">ID du paiement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Statut du paiement</returns>
    Task<PaymentStatusResponse> GetPaymentStatusAsync(string paymentId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Effectue un remboursement
    /// </summary>
    /// <param name="request">Détails du remboursement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du remboursement</returns>
    Task<RefundResponse> RefundPaymentAsync(RefundRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Récupère l'historique des transactions d'un utilisateur
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="limit">Nombre maximum de transactions à retourner</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des transactions</returns>
    Task<IEnumerable<PaymentTransactionDto>> GetUserTransactionsAsync(Guid userId, int limit = 50, CancellationToken cancellationToken = default);

    /// <summary>
    /// Valide une signature de webhook
    /// </summary>
    /// <param name="payload">Contenu du webhook</param>
    /// <param name="signature">Signature à valider</param>
    /// <param name="secret">Secret de validation</param>
    /// <returns>True si la signature est valide</returns>
    bool ValidateWebhookSignature(string payload, string signature, string secret);

    /// <summary>
    /// Traite un événement de webhook
    /// </summary>
    /// <param name="webhookEvent">Événement reçu</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du traitement</returns>
    Task<WebhookProcessingResult> ProcessWebhookEventAsync(WebhookEventDto webhookEvent, CancellationToken cancellationToken = default);
}

/// <summary>
/// Factory pour créer des instances de services de paiement
/// </summary>
public interface IPaymentServiceFactory
{
    /// <summary>
    /// Crée un service de paiement pour le provider spécifié
    /// </summary>
    /// <param name="provider">Provider de paiement</param>
    /// <returns>Instance du service de paiement</returns>
    IPaymentService CreatePaymentService(PaymentProvider provider);

    /// <summary>
    /// Obtient le service de paiement par défaut
    /// </summary>
    /// <returns>Service de paiement par défaut</returns>
    IPaymentService GetDefaultPaymentService();

    /// <summary>
    /// Obtient tous les services de paiement disponibles
    /// </summary>
    /// <returns>Liste des services disponibles</returns>
    IEnumerable<IPaymentService> GetAvailablePaymentServices();

    /// <summary>
    /// Obtient les statistiques des providers de paiement
    /// </summary>
    /// <returns>Statistiques des providers</returns>
    object GetProvidersStats();

    /// <summary>
    /// Obtient le service de paiement optimal pour une devise donnée
    /// </summary>
    /// <param name="currency">Code de devise</param>
    /// <returns>Service de paiement optimal</returns>
    IPaymentService GetOptimalPaymentServiceForCurrency(string currency);

    /// <summary>
    /// Obtient le service de paiement optimal pour une méthode de paiement donnée
    /// </summary>
    /// <param name="paymentMethod">Méthode de paiement</param>
    /// <returns>Service de paiement optimal</returns>
    IPaymentService GetOptimalPaymentServiceForMethod(PaymentMethodType paymentMethod);
}

/// <summary>
/// Demande de création d'intention de paiement
/// </summary>
public class CreatePaymentIntentRequest
{
    /// <summary>
    /// Montant en centimes (ex: 1500 = 15.00 EUR)
    /// </summary>
    public long Amount { get; set; }

    /// <summary>
    /// Code de devise (EUR, USD, XOF, etc.)
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// ID de l'utilisateur qui effectue le paiement
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// ID de la réservation associée
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// Description du paiement
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Métadonnées additionnelles
    /// </summary>
    public Dictionary<string, string> Metadata { get; set; } = new();

    /// <summary>
    /// URL de retour après paiement réussi
    /// </summary>
    public string? SuccessUrl { get; set; }

    /// <summary>
    /// URL de retour après paiement annulé
    /// </summary>
    public string? CancelUrl { get; set; }

    /// <summary>
    /// Méthodes de paiement autorisées
    /// </summary>
    public List<PaymentMethodType> AllowedPaymentMethods { get; set; } = new();
}

/// <summary>
/// Réponse de création d'intention de paiement
/// </summary>
public class PaymentIntentResponse
{
    /// <summary>
    /// ID de l'intention de paiement
    /// </summary>
    public string PaymentIntentId { get; set; } = string.Empty;

    /// <summary>
    /// Secret client pour finaliser le paiement côté frontend
    /// </summary>
    public string ClientSecret { get; set; } = string.Empty;

    /// <summary>
    /// Statut de l'intention
    /// </summary>
    public PaymentStatus Status { get; set; }

    /// <summary>
    /// Montant
    /// </summary>
    public long Amount { get; set; }

    /// <summary>
    /// Devise
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// URL de redirection pour certains providers
    /// </summary>
    public string? RedirectUrl { get; set; }

    /// <summary>
    /// Données additionnelles spécifiques au provider
    /// </summary>
    public Dictionary<string, object> ProviderData { get; set; } = new();
}

/// <summary>
/// Réponse de confirmation de paiement
/// </summary>
public class PaymentConfirmationResponse
{
    /// <summary>
    /// Succès de la confirmation
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// ID de la transaction
    /// </summary>
    public string TransactionId { get; set; } = string.Empty;

    /// <summary>
    /// Statut du paiement
    /// </summary>
    public PaymentStatus Status { get; set; }

    /// <summary>
    /// Message d'erreur si échec
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Code d'erreur si échec
    /// </summary>
    public string? ErrorCode { get; set; }
}

/// <summary>
/// Réponse de statut de paiement
/// </summary>
public class PaymentStatusResponse
{
    /// <summary>
    /// ID du paiement
    /// </summary>
    public string PaymentId { get; set; } = string.Empty;

    /// <summary>
    /// Statut actuel
    /// </summary>
    public PaymentStatus Status { get; set; }

    /// <summary>
    /// Montant
    /// </summary>
    public long Amount { get; set; }

    /// <summary>
    /// Devise
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Date de création
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date de dernière mise à jour
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Métadonnées
    /// </summary>
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Demande de remboursement
/// </summary>
public class RefundRequest
{
    /// <summary>
    /// ID du paiement à rembourser
    /// </summary>
    public string PaymentId { get; set; } = string.Empty;

    /// <summary>
    /// Montant à rembourser (null = remboursement total)
    /// </summary>
    public long? Amount { get; set; }

    /// <summary>
    /// Raison du remboursement
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// Métadonnées additionnelles
    /// </summary>
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Réponse de remboursement
/// </summary>
public class RefundResponse
{
    /// <summary>
    /// Succès du remboursement
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// ID du remboursement
    /// </summary>
    public string RefundId { get; set; } = string.Empty;

    /// <summary>
    /// Montant remboursé
    /// </summary>
    public long Amount { get; set; }

    /// <summary>
    /// Statut du remboursement
    /// </summary>
    public RefundStatus Status { get; set; }

    /// <summary>
    /// Message d'erreur si échec
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// DTO pour les transactions de paiement
/// </summary>
public class PaymentTransactionDto
{
    /// <summary>
    /// ID de la transaction
    /// </summary>
    public string TransactionId { get; set; } = string.Empty;

    /// <summary>
    /// Provider de paiement
    /// </summary>
    public PaymentProvider Provider { get; set; }

    /// <summary>
    /// Montant
    /// </summary>
    public long Amount { get; set; }

    /// <summary>
    /// Devise
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Statut
    /// </summary>
    public PaymentStatus Status { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Date de création
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// ID de la réservation associée
    /// </summary>
    public Guid? BookingId { get; set; }
}

/// <summary>
/// DTO pour les événements de webhook
/// </summary>
public class WebhookEventDto
{
    /// <summary>
    /// ID de l'événement
    /// </summary>
    public string EventId { get; set; } = string.Empty;

    /// <summary>
    /// Type d'événement
    /// </summary>
    public string EventType { get; set; } = string.Empty;

    /// <summary>
    /// Provider source
    /// </summary>
    public PaymentProvider Provider { get; set; }

    /// <summary>
    /// Données de l'événement
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// Date de l'événement
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Résultat du traitement d'un webhook
/// </summary>
public class WebhookProcessingResult
{
    /// <summary>
    /// Succès du traitement
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Message de résultat
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Actions effectuées
    /// </summary>
    public List<string> ActionsPerformed { get; set; } = new();

    /// <summary>
    /// Erreurs rencontrées
    /// </summary>
    public List<string> Errors { get; set; } = new();
}
