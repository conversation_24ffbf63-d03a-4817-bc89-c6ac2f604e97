import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authService } from '../services/authService';
import { mapBackendRoleToFrontend, type FrontendRole } from '@/lib/roleMapping';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'Client' | 'Provider' | 'ServiceProvider' | 'Prestataire' | 'Admin' | 'Admin Global' | 'Manager' | 'Support' | 'Supervisor' | 'Superviseur';
  isEmailConfirmed: boolean;
  isPhoneConfirmed: boolean;
  profileCompletionPercentage: number;
  avatar?: string;
  phoneNumber?: string;
}

export interface AuthState {
  // State
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitialized: boolean;
  error: string | null;

  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  refreshAuth: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  confirmEmail: (token: string) => Promise<void>;
  resendConfirmation: () => Promise<void>;
  clearError: () => void;
  setUser: (user: User) => void;
  setLoading: (loading: boolean) => void;
  clearAuth: () => void;
  refreshAuthToken: () => Promise<any>;
  initializeAuth: () => void;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'Client' | 'Provider' | 'ServiceProvider' | 'Prestataire' | 'Admin' | 'Admin Global' | 'Manager' | 'Support' | 'Supervisor' | 'Superviseur';
  phoneNumber?: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      isInitialized: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authService.login({ email, password });

          // Convertir le rôle numérique du backend en chaîne pour le frontend
          const mappedRole = mapBackendRoleToFrontend(response.user.role as any);
          const userWithMappedRole: User = {
            ...response.user,
            role: mappedRole,
            isPhoneConfirmed: false,
            profileCompletionPercentage: 0,
          };

          console.log('🔄 Utilisateur après mapping rôle:', userWithMappedRole);

          set({
            user: userWithMappedRole,
            token: response.token,
            refreshToken: response.refreshToken,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          });
          throw error;
        }
      },

      register: async (userData: RegisterData) => {
        set({ isLoading: true, error: null });

        try {
          await authService.register(userData);
          set({ isLoading: false, error: null });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Registration failed',
          });
          throw error;
        }
      },

      logout: async () => {
        try {
          await authService.logout();
        } finally {
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            error: null,
          });
          // Rediriger vers la page de connexion
          window.location.href = '/login';
        }
      },

      refreshAuth: async () => {
        try {
          const response = await authService.refreshToken();
          set({
            user: response.user,
            token: response.token,
            refreshToken: response.refreshToken,
            isAuthenticated: true,
          });
        } catch (error) {
          // If refresh fails, logout user
          get().logout();
          throw error;
        }
      },

      updateProfile: async (userData: Partial<User>) => {
        set({ isLoading: true, error: null });

        try {
          // TODO: Implémenter updateProfile dans authService
          const updatedUser = { ...get().user!, ...userData };

          set({
            user: updatedUser,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Profile update failed',
          });
          throw error;
        }
      },

      forgotPassword: async (email: string) => {
        set({ isLoading: true, error: null });

        try {
          // TODO: Implémenter forgotPassword dans authService
          await authService.forgotPassword(email);
          set({ isLoading: false, error: null });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Password reset request failed',
          });
          throw error;
        }
      },

      resetPassword: async (token: string, newPassword: string) => {
        set({ isLoading: true, error: null });

        try {
          // TODO: Implémenter resetPassword dans authService
          await authService.resetPassword(token, newPassword);
          set({ isLoading: false, error: null });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Password reset failed',
          });
          throw error;
        }
      },

      confirmEmail: async (token: string) => {
        set({ isLoading: true, error: null });

        try {
          // TODO: Implémenter confirmEmail dans authService
          await authService.confirmEmail(token);

          // Update user's email confirmation status
          const { user } = get();
          if (user) {
            set({
              user: { ...user, isEmailConfirmed: true },
              isLoading: false,
              error: null,
            });
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Email confirmation failed',
          });
          throw error;
        }
      },

      resendConfirmation: async () => {
        set({ isLoading: true, error: null });

        try {
          // TODO: Implémenter resendConfirmation dans authService
          await authService.resendConfirmation();
          set({ isLoading: false, error: null });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Resend confirmation failed',
          });
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setUser: (user: User) => {
        set({ user, isAuthenticated: true });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      clearAuth: () => {
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          error: null,
        });
      },

      // Refresh automatique du token
      refreshAuthToken: async () => {
        try {
          const response = await authService.refreshToken();

          // Récupérer l'utilisateur depuis le nouveau token
          const userFromToken = authService.getUserFromToken();

          set({
            user: userFromToken,
            token: response.token,
            refreshToken: response.refreshToken,
            isAuthenticated: true,
            error: null,
          });

          console.log('✅ Token rafraîchi avec succès');
          return response;
        } catch (error) {
          // Si le refresh échoue, déconnecter l'utilisateur
          console.error('❌ Erreur refresh token:', error);
          get().logout();
          throw error;
        }
      },

      // Initialiser l'authentification avec vérification localStorage
      initializeAuth: () => {
        try {
          // Vérifier si les données existent dans localStorage mais pas dans le store
          const storedAuth = localStorage.getItem('auth-storage');
          if (storedAuth) {
            const parsedAuth = JSON.parse(storedAuth);
            const currentState = get();

            // Si localStorage a des données valides, restaurer (même si store déjà initialisé)
            if (parsedAuth.state && parsedAuth.state.user && parsedAuth.state.token && parsedAuth.state.isAuthenticated) {
              console.log('🔄 Restauration de la session depuis localStorage:', { email: parsedAuth.state.user.email });
              set({
                user: parsedAuth.state.user,
                token: parsedAuth.state.token,
                refreshToken: parsedAuth.state.refreshToken,
                isAuthenticated: parsedAuth.state.isAuthenticated,
                isLoading: false,
                isInitialized: true,
                error: null,
              });
              return;
            }
          }

          // Marquer comme initialisé même si pas de données
          set({ isInitialized: true });
        } catch (error) {
          console.error('❌ Erreur lors de l\'initialisation auth:', error);
          // En cas d'erreur, nettoyer le localStorage corrompu
          localStorage.removeItem('auth-storage');
          set({ isInitialized: true });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
