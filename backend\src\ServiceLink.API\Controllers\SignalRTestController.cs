using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour tester les fonctionnalités SignalR
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SignalRTestController : ControllerBase
{
    private readonly INotificationService _notificationService;
    private readonly IChatService _chatService;
    private readonly IBookingRealtimeService _bookingRealtimeService;
    private readonly ILogger<SignalRTestController> _logger;

    public SignalRTestController(
        INotificationService notificationService,
        IChatService chatService,
        IBookingRealtimeService bookingRealtimeService,
        ILogger<SignalRTestController> logger)
    {
        _notificationService = notificationService;
        _chatService = chatService;
        _bookingRealtimeService = bookingRealtimeService;
        _logger = logger;
    }

    /// <summary>
    /// Teste l'envoi d'une notification à un utilisateur spécifique
    /// </summary>
    /// <param name="userId">ID de l'utilisateur destinataire</param>
    /// <param name="message">Message de test</param>
    [HttpPost("notification/user/{userId}")]
    public async Task<IActionResult> SendTestNotificationToUser(Guid userId, [FromBody] string message)
    {
        try
        {
            var notification = new NotificationDto
            {
                Type = "TestNotification",
                Title = "Test SignalR",
                Message = message,
                Priority = NotificationPriority.Normal,
                Data = new
                {
                    TestData = "Ceci est un test SignalR",
                    Timestamp = DateTime.UtcNow
                }
            };

            await _notificationService.SendToUserAsync(userId, notification);

            _logger.LogInformation("Notification de test envoyée à l'utilisateur {UserId}", userId);

            return Ok(new
            {
                Success = true,
                Message = "Notification envoyée avec succès",
                UserId = userId,
                NotificationId = notification.Id
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification de test à l'utilisateur {UserId}", userId);
            return StatusCode(500, new { Error = "Erreur lors de l'envoi de la notification" });
        }
    }

    /// <summary>
    /// Teste l'envoi d'une notification à tous les utilisateurs d'un rôle
    /// </summary>
    /// <param name="role">Rôle des utilisateurs destinataires</param>
    /// <param name="message">Message de test</param>
    [HttpPost("notification/role/{role}")]
    public async Task<IActionResult> SendTestNotificationToRole(UserRole role, [FromBody] string message)
    {
        try
        {
            var notification = new NotificationDto
            {
                Type = "RoleNotification",
                Title = $"Test SignalR - Rôle {role}",
                Message = message,
                Priority = NotificationPriority.Normal,
                Data = new
                {
                    TargetRole = role.ToString(),
                    TestData = "Notification de test pour un rôle",
                    Timestamp = DateTime.UtcNow
                }
            };

            await _notificationService.SendToRoleAsync(role, notification);

            _logger.LogInformation("Notification de test envoyée au rôle {Role}", role);

            return Ok(new
            {
                Success = true,
                Message = "Notification envoyée avec succès",
                Role = role.ToString(),
                NotificationId = notification.Id
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification de test au rôle {Role}", role);
            return StatusCode(500, new { Error = "Erreur lors de l'envoi de la notification" });
        }
    }

    /// <summary>
    /// Teste l'envoi d'un message de chat
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    /// <param name="senderId">ID de l'expéditeur</param>
    /// <param name="message">Message de test</param>
    [HttpPost("chat/{conversationId}/message")]
    public async Task<IActionResult> SendTestChatMessage(Guid conversationId, [FromQuery] Guid senderId, [FromBody] string message)
    {
        try
        {
            var chatMessage = new ChatMessageDto
            {
                ConversationId = conversationId,
                SenderId = senderId,
                SenderName = "Utilisateur Test",
                Content = message,
                MessageType = "text"
            };

            await _chatService.SendMessageAsync(conversationId, chatMessage);

            _logger.LogInformation("Message de chat de test envoyé dans la conversation {ConversationId}", conversationId);

            return Ok(new
            {
                Success = true,
                Message = "Message de chat envoyé avec succès",
                ConversationId = conversationId,
                MessageId = chatMessage.Id
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de message de chat de test dans la conversation {ConversationId}", conversationId);
            return StatusCode(500, new { Error = "Erreur lors de l'envoi du message" });
        }
    }

    /// <summary>
    /// Teste la notification d'une nouvelle réservation
    /// </summary>
    /// <param name="clientId">ID du client</param>
    /// <param name="providerId">ID du prestataire</param>
    /// <param name="serviceName">Nom du service</param>
    [HttpPost("booking/new")]
    public async Task<IActionResult> SendTestBookingNotification(
        [FromQuery] Guid clientId, 
        [FromQuery] Guid providerId, 
        [FromQuery] string serviceName)
    {
        try
        {
            var bookingNotification = new BookingNotificationDto
            {
                BookingId = Guid.NewGuid(),
                ClientId = clientId,
                ProviderId = providerId,
                ServiceName = serviceName,
                BookingDate = DateTime.UtcNow.AddDays(1),
                Status = "Pending",
                Price = 150.00m
            };

            await _bookingRealtimeService.NotifyNewBookingAsync(bookingNotification);

            _logger.LogInformation("Notification de nouvelle réservation de test envoyée pour le service {ServiceName}", serviceName);

            return Ok(new
            {
                Success = true,
                Message = "Notification de réservation envoyée avec succès",
                BookingId = bookingNotification.BookingId,
                ServiceName = serviceName
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification de réservation de test pour le service {ServiceName}", serviceName);
            return StatusCode(500, new { Error = "Erreur lors de l'envoi de la notification" });
        }
    }

    /// <summary>
    /// Teste la vérification de connexion d'un utilisateur
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    [HttpGet("user/{userId}/connected")]
    public async Task<IActionResult> CheckUserConnection(Guid userId)
    {
        try
        {
            var isConnected = await _notificationService.IsUserConnectedAsync(userId);

            return Ok(new
            {
                UserId = userId,
                IsConnected = isConnected,
                CheckedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de connexion pour l'utilisateur {UserId}", userId);
            return StatusCode(500, new { Error = "Erreur lors de la vérification" });
        }
    }

    /// <summary>
    /// Obtient la liste des utilisateurs connectés
    /// </summary>
    [HttpGet("users/connected")]
    public async Task<IActionResult> GetConnectedUsers()
    {
        try
        {
            var connectedUsers = await _notificationService.GetConnectedUsersAsync();

            return Ok(new
            {
                ConnectedUsers = connectedUsers,
                Count = connectedUsers.Count(),
                CheckedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des utilisateurs connectés");
            return StatusCode(500, new { Error = "Erreur lors de la récupération" });
        }
    }

    /// <summary>
    /// Teste les indicateurs de frappe dans une conversation
    /// </summary>
    /// <param name="conversationId">ID de la conversation</param>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="isTyping">Indique si l'utilisateur tape ou arrête de taper</param>
    [HttpPost("chat/{conversationId}/typing")]
    public async Task<IActionResult> SendTypingIndicator(Guid conversationId, [FromQuery] Guid userId, [FromQuery] bool isTyping)
    {
        try
        {
            if (isTyping)
            {
                await _chatService.UserTypingAsync(conversationId, userId);
            }
            else
            {
                await _chatService.UserStoppedTypingAsync(conversationId, userId);
            }

            _logger.LogInformation("Indicateur de frappe envoyé pour l'utilisateur {UserId} dans la conversation {ConversationId}: {IsTyping}", 
                userId, conversationId, isTyping);

            return Ok(new
            {
                Success = true,
                Message = $"Indicateur de frappe {(isTyping ? "démarré" : "arrêté")} avec succès",
                ConversationId = conversationId,
                UserId = userId,
                IsTyping = isTyping
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi d'indicateur de frappe pour l'utilisateur {UserId}", userId);
            return StatusCode(500, new { Error = "Erreur lors de l'envoi de l'indicateur" });
        }
    }

    /// <summary>
    /// Teste la mise à jour du statut d'une réservation
    /// </summary>
    /// <param name="bookingId">ID de la réservation</param>
    /// <param name="newStatus">Nouveau statut</param>
    [HttpPut("booking/{bookingId}/status")]
    public async Task<IActionResult> UpdateBookingStatus(Guid bookingId, [FromBody] string newStatus)
    {
        try
        {
            await _bookingRealtimeService.NotifyBookingStatusChangeAsync(bookingId, newStatus);

            _logger.LogInformation("Notification de changement de statut envoyée pour la réservation {BookingId}: {NewStatus}", 
                bookingId, newStatus);

            return Ok(new
            {
                Success = true,
                Message = "Notification de changement de statut envoyée avec succès",
                BookingId = bookingId,
                NewStatus = newStatus
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la notification de changement de statut pour la réservation {BookingId}", bookingId);
            return StatusCode(500, new { Error = "Erreur lors de la notification" });
        }
    }
}
