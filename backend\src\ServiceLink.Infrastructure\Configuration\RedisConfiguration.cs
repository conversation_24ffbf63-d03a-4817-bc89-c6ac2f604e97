using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Infrastructure.Services;
using StackExchange.Redis;

namespace ServiceLink.Infrastructure.Configuration;

/// <summary>
/// Configuration pour Redis et les services de cache
/// </summary>
public static class RedisConfiguration
{
    /// <summary>
    /// Ajoute les services Redis à la collection de services
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration de l'application</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddRedisServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configuration Redis depuis appsettings
        var redisSettings = new RedisSettings();
        configuration.GetSection("Redis").Bind(redisSettings);
        
        // Validation de la configuration
        if (string.IsNullOrEmpty(redisSettings.ConnectionString))
        {
            throw new InvalidOperationException("La chaîne de connexion Redis est requise. Configurez 'Redis:ConnectionString' dans appsettings.json");
        }

        // Enregistrement des settings
        services.Configure<RedisSettings>(options => configuration.GetSection("Redis").Bind(options));

        // Configuration de StackExchange.Redis
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<IConnectionMultiplexer>>();
            
            try
            {
                var configurationOptions = ConfigurationOptions.Parse(redisSettings.ConnectionString);
                
                // Configuration des options avancées
                configurationOptions.AbortOnConnectFail = redisSettings.AbortOnConnectFail;
                configurationOptions.ConnectTimeout = redisSettings.ConnectTimeoutMs;
                configurationOptions.SyncTimeout = redisSettings.SyncTimeoutMs;
                configurationOptions.AsyncTimeout = redisSettings.AsyncTimeoutMs;
                configurationOptions.ConnectRetry = redisSettings.ConnectRetry;
                configurationOptions.KeepAlive = redisSettings.KeepAliveSeconds;
                
                // Configuration SSL si nécessaire
                if (redisSettings.UseSsl)
                {
                    configurationOptions.Ssl = true;
                    configurationOptions.SslHost = redisSettings.SslHost;
                }

                // Authentification si configurée
                if (!string.IsNullOrEmpty(redisSettings.Password))
                {
                    configurationOptions.Password = redisSettings.Password;
                }

                logger.LogInformation("Connexion à Redis: {ConnectionString}", 
                    configurationOptions.ToString().Replace(redisSettings.Password ?? "", "***"));

                var connection = ConnectionMultiplexer.Connect(configurationOptions);
                
                // Événements de connexion pour le logging
                connection.ConnectionFailed += (sender, args) =>
                {
                    logger.LogError("Connexion Redis échouée: {EndPoint} - {FailureType}", 
                        args.EndPoint, args.FailureType);
                };
                
                connection.ConnectionRestored += (sender, args) =>
                {
                    logger.LogInformation("Connexion Redis restaurée: {EndPoint}", args.EndPoint);
                };

                connection.ErrorMessage += (sender, args) =>
                {
                    logger.LogError("Erreur Redis: {Message}", args.Message);
                };

                logger.LogInformation("Connexion Redis établie avec succès");
                return connection;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Erreur lors de la connexion à Redis");
                throw;
            }
        });

        // Configuration du cache distribué Redis
        services.AddStackExchangeRedisCache(options =>
        {
            options.Configuration = redisSettings.ConnectionString;
            options.InstanceName = redisSettings.InstanceName;
        });

        // Enregistrement du service de cache personnalisé
        services.AddScoped<ICacheService, RedisCacheService>();

        return services;
    }

    /// <summary>
    /// Ajoute les health checks pour Redis
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddRedisHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        var redisSettings = new RedisSettings();
        configuration.GetSection("Redis").Bind(redisSettings);

        services.AddHealthChecks()
            .AddRedis(redisSettings.ConnectionString, name: "redis", tags: new[] { "redis", "cache" });

        return services;
    }
}

/// <summary>
/// Paramètres de configuration pour Redis
/// </summary>
public class RedisSettings
{
    /// <summary>
    /// Chaîne de connexion Redis
    /// </summary>
    public string ConnectionString { get; set; } = "localhost:6379";

    /// <summary>
    /// Nom de l'instance Redis (préfixe pour les clés)
    /// </summary>
    public string InstanceName { get; set; } = "ServiceLink";

    /// <summary>
    /// Mot de passe Redis (optionnel)
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Utiliser SSL pour la connexion
    /// </summary>
    public bool UseSsl { get; set; } = false;

    /// <summary>
    /// Nom d'hôte SSL
    /// </summary>
    public string? SslHost { get; set; }

    /// <summary>
    /// Abandonner si la connexion échoue au démarrage
    /// </summary>
    public bool AbortOnConnectFail { get; set; } = false;

    /// <summary>
    /// Timeout de connexion en millisecondes
    /// </summary>
    public int ConnectTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// Timeout synchrone en millisecondes
    /// </summary>
    public int SyncTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// Timeout asynchrone en millisecondes
    /// </summary>
    public int AsyncTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// Nombre de tentatives de reconnexion
    /// </summary>
    public int ConnectRetry { get; set; } = 3;

    /// <summary>
    /// Intervalle keep-alive en secondes
    /// </summary>
    public int KeepAliveSeconds { get; set; } = 60;

    /// <summary>
    /// Base de données Redis à utiliser (0-15)
    /// </summary>
    public int Database { get; set; } = 0;

    /// <summary>
    /// Activer le mode développement (logs détaillés)
    /// </summary>
    public bool EnableDevelopmentMode { get; set; } = false;

    /// <summary>
    /// Valide la configuration Redis
    /// </summary>
    /// <returns>True si la configuration est valide</returns>
    public bool IsValid()
    {
        return !string.IsNullOrEmpty(ConnectionString) &&
               !string.IsNullOrEmpty(InstanceName) &&
               ConnectTimeoutMs > 0 &&
               SyncTimeoutMs > 0 &&
               AsyncTimeoutMs > 0 &&
               ConnectRetry >= 0 &&
               KeepAliveSeconds > 0 &&
               Database >= 0 && Database <= 15;
    }

    /// <summary>
    /// Obtient la chaîne de connexion complète avec tous les paramètres
    /// </summary>
    /// <returns>Chaîne de connexion formatée</returns>
    public string GetFullConnectionString()
    {
        var builder = new List<string> { ConnectionString };

        if (!string.IsNullOrEmpty(Password))
        {
            builder.Add($"password={Password}");
        }

        if (UseSsl)
        {
            builder.Add("ssl=true");
            if (!string.IsNullOrEmpty(SslHost))
            {
                builder.Add($"sslHost={SslHost}");
            }
        }

        builder.Add($"connectTimeout={ConnectTimeoutMs}");
        builder.Add($"syncTimeout={SyncTimeoutMs}");
        builder.Add($"asyncTimeout={AsyncTimeoutMs}");
        builder.Add($"connectRetry={ConnectRetry}");
        builder.Add($"keepAlive={KeepAliveSeconds}");
        builder.Add($"abortConnect={AbortOnConnectFail.ToString().ToLower()}");

        return string.Join(",", builder);
    }
}
