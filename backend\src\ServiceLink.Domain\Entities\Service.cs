using ServiceLink.Domain.Enums;

namespace ServiceLink.Domain.Entities;

/// <summary>
/// Entité représentant un service proposé par un prestataire
/// </summary>
public class Service : BaseEntity
{
    /// <summary>
    /// ID du prestataire qui propose ce service
    /// </summary>
    public Guid ProviderId { get; private set; }

    /// <summary>
    /// Nom du service
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// Description détaillée du service
    /// </summary>
    public string Description { get; private set; } = string.Empty;

    /// <summary>
    /// Description courte pour les listes
    /// </summary>
    public string ShortDescription { get; private set; } = string.Empty;

    /// <summary>
    /// ID de la catégorie de service
    /// </summary>
    public Guid CategoryId { get; private set; }

    /// <summary>
    /// Prix de base du service en centimes
    /// </summary>
    public long BasePrice { get; private set; }

    /// <summary>
    /// Unité de tarification (heure, forfait, etc.)
    /// </summary>
    public PricingUnit PricingUnit { get; private set; }

    /// <summary>
    /// Durée minimale en minutes
    /// </summary>
    public int MinDurationMinutes { get; private set; }

    /// <summary>
    /// Durée maximale en minutes
    /// </summary>
    public int MaxDurationMinutes { get; private set; }

    /// <summary>
    /// Durée par défaut en minutes
    /// </summary>
    public int DefaultDurationMinutes { get; private set; }

    /// <summary>
    /// Zone de service (rayon en kilomètres)
    /// </summary>
    public int ServiceRadiusKm { get; private set; }

    /// <summary>
    /// Indique si le service est disponible
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// Indique si le service est disponible pour réservation urgente
    /// </summary>
    public bool IsUrgentAvailable { get; private set; }

    /// <summary>
    /// Supplément pour réservation urgente (pourcentage)
    /// </summary>
    public decimal UrgentSurcharge { get; private set; }

    /// <summary>
    /// Préavis minimum requis en heures
    /// </summary>
    public int MinAdvanceHours { get; private set; }

    /// <summary>
    /// Préavis maximum en jours
    /// </summary>
    public int MaxAdvanceDays { get; private set; }

    /// <summary>
    /// Indique si le service nécessite une validation manuelle
    /// </summary>
    public bool RequiresApproval { get; private set; }

    /// <summary>
    /// Tags pour la recherche
    /// </summary>
    public string Tags { get; private set; } = string.Empty;

    /// <summary>
    /// Images du service (URLs séparées par des virgules)
    /// </summary>
    public string Images { get; private set; } = string.Empty;

    /// <summary>
    /// Équipements fournis
    /// </summary>
    public string EquipmentProvided { get; private set; } = string.Empty;

    /// <summary>
    /// Équipements requis du client
    /// </summary>
    public string EquipmentRequired { get; private set; } = string.Empty;

    /// <summary>
    /// Instructions spéciales
    /// </summary>
    public string SpecialInstructions { get; private set; } = string.Empty;

    /// <summary>
    /// Politique d'annulation
    /// </summary>
    public string CancellationPolicy { get; private set; } = string.Empty;

    /// <summary>
    /// Note moyenne du service
    /// </summary>
    public decimal AverageRating { get; private set; }

    /// <summary>
    /// Nombre total d'avis
    /// </summary>
    public int TotalReviews { get; private set; }

    /// <summary>
    /// Nombre total de réservations
    /// </summary>
    public int TotalBookings { get; private set; }

    /// <summary>
    /// Nombre de réservations terminées
    /// </summary>
    public int CompletedBookings { get; private set; }

    /// <summary>
    /// Devise du prix
    /// </summary>
    public string Currency { get; private set; } = "EUR";

    /// <summary>
    /// Métadonnées additionnelles (JSON)
    /// </summary>
    public string? Metadata { get; private set; }

    /// <summary>
    /// Navigation vers le prestataire
    /// </summary>
    public virtual User? Provider { get; set; }

    /// <summary>
    /// Navigation vers la catégorie
    /// </summary>
    public virtual ServiceCategory? Category { get; set; }

    /// <summary>
    /// Réservations pour ce service
    /// </summary>
    public virtual ICollection<Booking> Bookings { get; set; } = new List<Booking>();

    /// <summary>
    /// Avis pour ce service
    /// </summary>
    public virtual ICollection<Review> Reviews { get; set; } = new List<Review>();

    /// <summary>
    /// Disponibilités du service
    /// </summary>
    public virtual ICollection<ServiceAvailability> Availabilities { get; set; } = new List<ServiceAvailability>();

    /// <summary>
    /// Constructeur privé pour EF Core
    /// </summary>
    private Service() { }

    /// <summary>
    /// Constructeur pour créer un nouveau service
    /// </summary>
    public Service(
        Guid providerId,
        string name,
        string description,
        Guid categoryId,
        long basePrice,
        PricingUnit pricingUnit,
        int defaultDurationMinutes,
        int serviceRadiusKm,
        string currency = "EUR")
    {
        ProviderId = providerId;
        Name = name;
        Description = description;
        CategoryId = categoryId;
        BasePrice = basePrice;
        PricingUnit = pricingUnit;
        DefaultDurationMinutes = defaultDurationMinutes;
        MinDurationMinutes = defaultDurationMinutes;
        MaxDurationMinutes = defaultDurationMinutes * 3; // Par défaut 3x la durée de base
        ServiceRadiusKm = serviceRadiusKm;
        Currency = currency;
        IsActive = true;
        MinAdvanceHours = 2; // 2 heures de préavis minimum par défaut
        MaxAdvanceDays = 30; // 30 jours maximum par défaut
        UrgentSurcharge = 0.25m; // 25% de supplément par défaut
    }

    /// <summary>
    /// Met à jour les informations de base du service
    /// </summary>
    public void UpdateBasicInfo(string name, string description, string shortDescription)
    {
        Name = name;
        Description = description;
        ShortDescription = shortDescription;
    }

    /// <summary>
    /// Met à jour la tarification
    /// </summary>
    public void UpdatePricing(long basePrice, PricingUnit pricingUnit, string currency = "EUR")
    {
        BasePrice = basePrice;
        PricingUnit = pricingUnit;
        Currency = currency;
    }

    /// <summary>
    /// Met à jour les durées
    /// </summary>
    public void UpdateDurations(int minDurationMinutes, int maxDurationMinutes, int defaultDurationMinutes)
    {
        if (minDurationMinutes > maxDurationMinutes)
            throw new ArgumentException("Min duration cannot be greater than max duration");

        if (defaultDurationMinutes < minDurationMinutes || defaultDurationMinutes > maxDurationMinutes)
            throw new ArgumentException("Default duration must be between min and max duration");

        MinDurationMinutes = minDurationMinutes;
        MaxDurationMinutes = maxDurationMinutes;
        DefaultDurationMinutes = defaultDurationMinutes;
    }

    /// <summary>
    /// Met à jour la zone de service
    /// </summary>
    public void UpdateServiceRadius(int radiusKm)
    {
        if (radiusKm <= 0)
            throw new ArgumentException("Service radius must be positive");

        ServiceRadiusKm = radiusKm;
    }

    /// <summary>
    /// Active ou désactive le service
    /// </summary>
    public void SetActive(bool isActive)
    {
        IsActive = isActive;
    }

    /// <summary>
    /// Configure la disponibilité urgente
    /// </summary>
    public void ConfigureUrgentAvailability(bool isAvailable, decimal surcharge = 0.25m)
    {
        IsUrgentAvailable = isAvailable;
        UrgentSurcharge = surcharge;
    }

    /// <summary>
    /// Met à jour les préavis requis
    /// </summary>
    public void UpdateAdvanceNotice(int minHours, int maxDays)
    {
        if (minHours < 0 || maxDays < 0)
            throw new ArgumentException("Advance notice values must be positive");

        MinAdvanceHours = minHours;
        MaxAdvanceDays = maxDays;
    }

    /// <summary>
    /// Met à jour les tags
    /// </summary>
    public void UpdateTags(IEnumerable<string> tags)
    {
        Tags = string.Join(",", tags.Where(t => !string.IsNullOrWhiteSpace(t)));
    }

    /// <summary>
    /// Met à jour les images
    /// </summary>
    public void UpdateImages(IEnumerable<string> imageUrls)
    {
        Images = string.Join(",", imageUrls.Where(url => !string.IsNullOrWhiteSpace(url)));
    }

    /// <summary>
    /// Met à jour les équipements
    /// </summary>
    public void UpdateEquipment(string provided, string required)
    {
        EquipmentProvided = provided;
        EquipmentRequired = required;
    }

    /// <summary>
    /// Met à jour les instructions et politiques
    /// </summary>
    public void UpdatePolicies(string specialInstructions, string cancellationPolicy)
    {
        SpecialInstructions = specialInstructions;
        CancellationPolicy = cancellationPolicy;
    }

    /// <summary>
    /// Met à jour les statistiques après une réservation
    /// </summary>
    public void UpdateBookingStats(bool isCompleted)
    {
        TotalBookings++;
        if (isCompleted)
        {
            CompletedBookings++;
        }
    }

    /// <summary>
    /// Met à jour les statistiques d'avis
    /// </summary>
    public void UpdateReviewStats(decimal newRating)
    {
        var totalRating = AverageRating * TotalReviews + newRating;
        TotalReviews++;
        AverageRating = totalRating / TotalReviews;
    }

    /// <summary>
    /// Calcule le prix pour une durée donnée
    /// </summary>
    public long CalculatePrice(int durationMinutes, bool isUrgent = false)
    {
        long price = PricingUnit switch
        {
            PricingUnit.Hourly => (long)(BasePrice * (durationMinutes / 60.0m)),
            PricingUnit.Fixed => BasePrice,
            PricingUnit.Daily => BasePrice,
            _ => BasePrice
        };

        if (isUrgent && IsUrgentAvailable)
        {
            price += (long)(price * UrgentSurcharge);
        }

        return price;
    }

    /// <summary>
    /// Vérifie si le service est disponible pour une date donnée
    /// </summary>
    public bool IsAvailableAt(DateTime requestedDate)
    {
        if (!IsActive)
            return false;

        var now = DateTime.UtcNow;
        var minDate = now.AddHours(MinAdvanceHours);
        var maxDate = now.AddDays(MaxAdvanceDays);

        return requestedDate >= minDate && requestedDate <= maxDate;
    }

    /// <summary>
    /// Obtient la liste des tags
    /// </summary>
    public IEnumerable<string> GetTags()
    {
        return string.IsNullOrWhiteSpace(Tags) 
            ? Enumerable.Empty<string>() 
            : Tags.Split(',', StringSplitOptions.RemoveEmptyEntries);
    }

    /// <summary>
    /// Obtient la liste des images
    /// </summary>
    public IEnumerable<string> GetImages()
    {
        return string.IsNullOrWhiteSpace(Images) 
            ? Enumerable.Empty<string>() 
            : Images.Split(',', StringSplitOptions.RemoveEmptyEntries);
    }
}
