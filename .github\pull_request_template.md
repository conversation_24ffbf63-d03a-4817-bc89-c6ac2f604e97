# 🔄 Pull Request - ServiceLink

## 📋 Description
<!-- Décrivez brièvement les changements apportés -->

### 🎯 Contexte
<!-- Pourquoi ces changements sont-ils nécessaires ? -->

### 🔧 Changements Apportés
<!-- Liste détaillée des modifications -->
- 
- 
- 

## 🏷️ Type de Changement
<!-- Cochez les cases appropriées -->
- [ ] 🐛 **Bug fix** (correction qui résout un problème)
- [ ] ✨ **New feature** (ajout de fonctionnalité)
- [ ] 💥 **Breaking change** (changement qui casse la compatibilité)
- [ ] 📚 **Documentation** (mise à jour de la documentation)
- [ ] 🎨 **Style** (formatage, style, pas de changement de logique)
- [ ] ♻️ **Refactor** (refactoring sans changement de fonctionnalité)
- [ ] ⚡ **Performance** (amélioration des performances)
- [ ] 🧪 **Test** (ajout ou correction de tests)
- [ ] 🔧 **Chore** (maintenance, configuration)

## 🧪 Tests
<!-- Dé<PERSON>rivez les tests effectués -->

### Tests Automatisés
- [ ] Tests unitaires passent
- [ ] Tests d'intégration passent
- [ ] Tests end-to-end passent (si applicable)
- [ ] Couverture de code maintenue/améliorée

### Tests Manuels
- [ ] Tests fonctionnels effectués
- [ ] Tests de régression effectués
- [ ] Tests sur différents navigateurs (frontend)
- [ ] Tests responsive (frontend)

### Environnements Testés
- [ ] Développement local
- [ ] Environnement de staging
- [ ] Base de données de test

## 📸 Screenshots / Vidéos
<!-- Ajoutez des captures d'écran pour les changements UI -->

### Avant
<!-- Screenshot de l'état avant -->

### Après
<!-- Screenshot de l'état après -->

## 🔗 Issues Liées
<!-- Référencez les issues GitHub -->
- Closes #
- Fixes #
- Related to #

## 📝 Checklist Développeur
<!-- Vérifiez avant de soumettre la PR -->

### Code Quality
- [ ] Le code suit les conventions du projet
- [ ] Auto-review effectuée
- [ ] Code commenté où nécessaire
- [ ] Pas de code mort ou commenté
- [ ] Variables et fonctions nommées clairement

### Documentation
- [ ] Documentation mise à jour
- [ ] README mis à jour si nécessaire
- [ ] Commentaires API mis à jour
- [ ] Changelog mis à jour

### Sécurité
- [ ] Pas de données sensibles exposées
- [ ] Validation des entrées utilisateur
- [ ] Authentification/autorisation vérifiée
- [ ] Pas de vulnérabilités introduites

### Performance
- [ ] Pas de régression de performance
- [ ] Requêtes DB optimisées
- [ ] Pas de memory leaks
- [ ] Bundle size vérifié (frontend)

### Compatibilité
- [ ] Compatible avec les navigateurs supportés
- [ ] Responsive design vérifié
- [ ] Pas de breaking changes non documentés
- [ ] Migration DB fournie si nécessaire

## 🚀 Déploiement
<!-- Instructions spéciales pour le déploiement -->

### Prérequis
- [ ] Migration de base de données requise
- [ ] Variables d'environnement à ajouter
- [ ] Configuration serveur à modifier
- [ ] Redémarrage de services requis

### Instructions
<!-- Étapes spécifiques pour le déploiement -->

## 📊 Impact
<!-- Évaluez l'impact des changements -->

### Risque
- [ ] 🟢 **Faible** - Changements mineurs, bien testés
- [ ] 🟡 **Moyen** - Changements modérés, tests complets
- [ ] 🔴 **Élevé** - Changements majeurs, tests extensifs requis

### Zones Affectées
- [ ] Frontend (React)
- [ ] Backend API (.NET)
- [ ] Base de données
- [ ] Infrastructure (Docker)
- [ ] CI/CD
- [ ] Documentation

## 👥 Reviewers
<!-- Mentionnez les reviewers spécifiques si nécessaire -->
@reviewer1 @reviewer2

## 📋 Notes Additionnelles
<!-- Informations supplémentaires pour les reviewers -->

### Points d'Attention
<!-- Zones spécifiques à examiner -->

### Questions Ouvertes
<!-- Questions pour discussion -->

---

## 🔍 Checklist Reviewer

### Code Review
- [ ] Code lisible et maintenable
- [ ] Architecture respectée
- [ ] Pas de code dupliqué
- [ ] Gestion d'erreurs appropriée
- [ ] Performance acceptable

### Tests
- [ ] Tests pertinents et complets
- [ ] Couverture de code suffisante
- [ ] Tests passent localement
- [ ] Pas de tests flaky

### Sécurité
- [ ] Pas de vulnérabilités
- [ ] Données sensibles protégées
- [ ] Validation des entrées
- [ ] Autorisation correcte

### Documentation
- [ ] Code auto-documenté
- [ ] Documentation technique à jour
- [ ] API documentée
- [ ] Exemples fournis si nécessaire

---

**🎯 Objectif** : Maintenir la qualité et la stabilité du code ServiceLink  
**⏱️ SLA Review** : 24h pour les features, 4h pour les hotfixes  
**🔄 Process** : Squash and merge recommandé pour garder un historique propre
