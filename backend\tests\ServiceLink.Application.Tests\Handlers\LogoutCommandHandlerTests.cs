using System.Threading;
using System.Threading.Tasks;
using Moq;
using Xunit;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Handlers;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;
using System;

public class LogoutCommandHandlerTests
{
    [Fact]
    public async Task Handle_ValidRefreshToken_RevokesTokenAndReturnsTrue()
    {
        // Arrange
        var refreshTokenRepository = new Mock<IRefreshTokenRepository>();
        refreshTokenRepository.Setup(r => r.InvalidateAsync(It.IsAny<string>())).Returns(Task.CompletedTask);
        var handler = new LogoutCommandHandler(refreshTokenRepository.Object);
        var command = new LogoutCommand { RefreshToken = "refresh-token" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.True(result);
    }
    [Fact]
    public async Task Handle_EmptyRefreshToken_ReturnsFalse()
    {
        // Arrange
        var refreshTokenRepository = new Mock<IRefreshTokenRepository>();
        var handler = new LogoutCommandHandler(refreshTokenRepository.Object);
        var command = new LogoutCommand { RefreshToken = "" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.False(result);
    }
}
