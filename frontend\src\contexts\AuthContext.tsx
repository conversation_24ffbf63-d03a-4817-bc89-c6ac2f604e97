import React, { createContext, useContext, useEffect, useState,type ReactNode } from 'react'
import type { 
  User, 
  AuthContextType, 
  LoginRequest, 
  RegisterRequest, 
  UpdateProfileRequest 
} from '../types/auth'
import { authService } from '../services/authService'
import { toast } from 'sonner'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
  initialUser?: User | null
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ 
  children, 
  initialUser = null 
}) => {
  const [user, setUser] = useState<User | null>(initialUser)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Initialiser l'état d'authentification au chargement
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedToken = authService.getToken()
        
        if (storedToken && authService.isAuthenticated()) {
          setToken(storedToken)
          
          // Essayer de récupérer les infos utilisateur depuis le token
          const userFromToken = authService.getUserFromToken()
          if (userFromToken) {
            setUser(userFromToken)
          } else {
            // Si on ne peut pas décoder le token, récupérer le profil depuis l'API
            try {
              const profile = await authService.getProfile()
              setUser(profile)
            } catch (error) {
              // Si l'API échoue, déconnecter l'utilisateur
              await logout()
            }
          }
        }
      } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'authentification:', error)
        await logout()
      } finally {
        setIsLoading(false)
      }
    }

    // Ne pas initialiser si on a déjà un utilisateur initial (pour les tests)
    if (initialUser) {
      setIsLoading(false)
    } else {
      initializeAuth()
    }
  }, [initialUser])

  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      setIsLoading(true)
      const response = await authService.login(credentials)
      
      setToken(response.token)
      setUser(response.user)
      
      toast.success('Connexion réussie!')
    } catch (error: any) {
      toast.error(error.message || 'Erreur lors de la connexion')
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (data: RegisterRequest): Promise<void> => {
    try {
      setIsLoading(true)
      await authService.register(data)
      
      toast.success('Inscription réussie! Vérifiez votre email pour confirmer votre compte.')
    } catch (error: any) {
      toast.error(error.message || 'Erreur lors de l\'inscription')
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async (): Promise<void> => {
    try {
      await authService.logout()
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error)
    } finally {
      setUser(null)
      setToken(null)
      toast.success('Déconnexion réussie')
    }
  }

  const refreshToken = async (): Promise<void> => {
    try {
      const response = await authService.refreshToken()
      setToken(response.token)
    } catch (error) {
      console.error('Erreur lors du refresh du token:', error)
      await logout()
      throw error
    }
  }

  const updateProfile = async (data: UpdateProfileRequest): Promise<void> => {
    try {
      setIsLoading(true)
      const updatedUser = await authService.updateProfile(data)
      setUser(updatedUser)
      
      toast.success('Profil mis à jour avec succès!')
    } catch (error: any) {
      toast.error(error.message || 'Erreur lors de la mise à jour du profil')
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated: !!user && !!token,
    isLoading,
    login,
    register,
    logout,
    refreshToken,
    updateProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext
