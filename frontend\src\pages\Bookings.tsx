import React, { useEffect, useState } from 'react';
import { Calendar, Filter, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BookingCard } from '@/components/booking/BookingCard';
import { useBookingStore, useUIStore } from '@/stores';
import { useAuthStore } from '@/stores/authStore'; 
import type { BookingStatus, BookingFilters } from '@/stores/bookingStore';

export const Bookings: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    clientBookings, 
    providerBookings, 
    isLoading, 
    getClientBookings, 
    getProviderBookings,
    confirmBooking,
    rejectBooking,
    startService,
    completeService,
    cancelBooking
  } = useBookingStore();
  const { showToast } = useUIStore();

  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<BookingStatus | 'all'>('all');
  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'week' | 'month'>('all');

  useEffect(() => {
    if (user) {
      const filters = buildFilters();
      if (user.role === 'Client' || user.role === 'Admin') {
        getClientBookings(filters);
      }
      if (user.role === 'ServiceProvider' || user.role === 'Admin') {
        getProviderBookings(filters);
      }
    }
  }, [user, statusFilter, dateFilter, getClientBookings, getProviderBookings]);

  const buildFilters = (): BookingFilters => {
    const filters: BookingFilters = {};

    if (statusFilter !== 'all') {
      filters.status = [statusFilter as BookingStatus];
    }

    if (dateFilter !== 'all') {
      const now = new Date();
      switch (dateFilter) {
        case 'today':
          filters.startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          filters.endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
          break;
        case 'week':
          const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          filters.startDate = weekStart;
          filters.endDate = now;
          break;
        case 'month':
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
          filters.startDate = monthStart;
          filters.endDate = now;
          break;
      }
    }

    return filters;
  };

  const handleBookingAction = async (action: string, booking: any) => {
    try {
      switch (action) {
        case 'confirm':
          await confirmBooking(booking.id);
          showToast({
            type: 'success',
            title: 'Réservation confirmée',
            message: 'La réservation a été confirmée avec succès.',
          });
          break;
        case 'reject':
          await rejectBooking(booking.id, 'Indisponible à cette date');
          showToast({
            type: 'success',
            title: 'Réservation refusée',
            message: 'La réservation a été refusée.',
          });
          break;
        case 'start':
          await startService(booking.id);
          showToast({
            type: 'success',
            title: 'Service démarré',
            message: 'Le service a été marqué comme démarré.',
          });
          break;
        case 'complete':
          await completeService(booking.id);
          showToast({
            type: 'success',
            title: 'Service terminé',
            message: 'Le service a été marqué comme terminé.',
          });
          break;
        case 'cancel':
          await cancelBooking(booking.id, 'Annulation client');
          showToast({
            type: 'success',
            title: 'Réservation annulée',
            message: 'La réservation a été annulée.',
          });
          break;
        case 'details':
          // Navigate to booking details
          window.location.href = `/bookings/${booking.id}`;
          break;
        case 'review':
          // Open review modal
          showToast({
            type: 'info',
            title: 'Fonctionnalité à venir',
            message: 'La fonctionnalité d\'évaluation sera bientôt disponible.',
          });
          break;
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Erreur',
        message: 'Une erreur est survenue lors de l\'action.',
      });
    }
  };

  const filterBookings = (bookings: any[]) => {
    return bookings.filter(booking => {
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          booking.service?.name.toLowerCase().includes(searchLower) ||
          booking.client?.firstName.toLowerCase().includes(searchLower) ||
          booking.client?.lastName.toLowerCase().includes(searchLower) ||
          booking.provider?.firstName.toLowerCase().includes(searchLower) ||
          booking.provider?.lastName.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  };

  const getBookingsByStatus = (bookings: any[], status: string) => {
    if (status === 'all') return bookings;
    return bookings.filter(booking => booking.status === status);
  };

  const renderBookingList = (bookings: any[], userRole: 'Client' | 'ServiceProvider' | 'Admin') => {
    const filteredBookings = filterBookings(bookings);

    if (filteredBookings.length === 0) {
      return (
        <div className="text-center py-12">
          <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">Aucune réservation</h3>
          <p className="text-muted-foreground">
            {searchTerm ? 'Aucune réservation ne correspond à votre recherche.' : 'Vous n\'avez pas encore de réservations.'}
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {filteredBookings.map((booking) => (
          <BookingCard
            key={booking.id}
            booking={booking}
            userRole={userRole}
            onAction={handleBookingAction}
          />
        ))}
      </div>
    );
  };

  const currentBookings = user?.role === 'ServiceProvider' ? providerBookings : clientBookings;
  const userRole = user?.role || 'Client';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold mb-2">Mes réservations</h1>
        <p className="text-muted-foreground">
          {user?.role === 'ServiceProvider' 
            ? 'Gérez vos demandes de réservation et vos services'
            : 'Suivez l\'état de vos réservations'
          }
        </p>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filtres</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les statuts</SelectItem>
                <SelectItem value="Pending">En attente</SelectItem>
                <SelectItem value="Confirmed">Confirmé</SelectItem>
                <SelectItem value="InProgress">En cours</SelectItem>
                <SelectItem value="Completed">Terminé</SelectItem>
                <SelectItem value="Cancelled">Annulé</SelectItem>
                <SelectItem value="Rejected">Refusé</SelectItem>
              </SelectContent>
            </Select>

            {/* Date Filter */}
            <Select value={dateFilter} onValueChange={(value) => setDateFilter(value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Période" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les dates</SelectItem>
                <SelectItem value="today">Aujourd'hui</SelectItem>
                <SelectItem value="week">Cette semaine</SelectItem>
                <SelectItem value="month">Ce mois</SelectItem>
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            <Button 
              variant="outline" 
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setDateFilter('all');
              }}
            >
              Effacer les filtres
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Bookings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">
            Toutes ({currentBookings.length})
          </TabsTrigger>
          <TabsTrigger value="Pending">
            En attente ({getBookingsByStatus(currentBookings, 'Pending').length})
          </TabsTrigger>
          <TabsTrigger value="Confirmed">
            Confirmées ({getBookingsByStatus(currentBookings, 'Confirmed').length})
          </TabsTrigger>
          <TabsTrigger value="InProgress">
            En cours ({getBookingsByStatus(currentBookings, 'InProgress').length})
          </TabsTrigger>
          <TabsTrigger value="Completed">
            Terminées ({getBookingsByStatus(currentBookings, 'Completed').length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            renderBookingList(currentBookings, userRole)
          )}
        </TabsContent>

        <TabsContent value="Pending" className="mt-6">
          {renderBookingList(getBookingsByStatus(currentBookings, 'Pending'), userRole)}
        </TabsContent>

        <TabsContent value="Confirmed" className="mt-6">
          {renderBookingList(getBookingsByStatus(currentBookings, 'Confirmed'), userRole)}
        </TabsContent>

        <TabsContent value="InProgress" className="mt-6">
          {renderBookingList(getBookingsByStatus(currentBookings, 'InProgress'), userRole)}
        </TabsContent>

        <TabsContent value="Completed" className="mt-6">
          {renderBookingList(getBookingsByStatus(currentBookings, 'Completed'), userRole)}
        </TabsContent>
      </Tabs>
    </div>
  );
};
