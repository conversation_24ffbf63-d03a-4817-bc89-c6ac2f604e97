classDiagram
    %% Core Domain Entities
    class User {
        +Guid Id
        +string Email
        +string PasswordHash
        +string FirstName
        +string LastName
        +string PhoneNumber
        +DateTime CreatedAt
        +DateTime UpdatedAt
        +bool IsActive
        +UserRole Role
        +Address Address
        +List~Notification~ Notifications
        +ValidatePassword(password: string) bool
        +UpdateProfile(profile: UserProfileDto) void
        +DeactivateAccount() void
    }

    class Client {
        +Guid UserId
        +string PreferredPaymentMethod
        +List~Booking~ Bookings
        +List~Review~ ReviewsGiven
        +bool CanBookForThirdParty
        +CreateBooking(service: Service, provider: Provider) Booking
        +CancelBooking(bookingId: Guid) void
        +LeaveReview(booking: Booking, rating: int, comment: string) Review
    }

    class Provider {
        +Guid UserId
        +string BusinessName
        +string Description
        +decimal HourlyRate
        +List~string~ Certifications
        +List~ServiceCategory~ ServiceCategories
        +Schedule AvailabilitySchedule
        +List~Booking~ Bookings
        +List~Review~ ReviewsReceived
        +bool IsVerified
        +decimal AverageRating
        +int TotalReviews
        +UpdateSchedule(schedule: Schedule) void
        +AcceptBooking(bookingId: Guid) void
        +CompleteService(bookingId: Guid) void
    }

    class Admin {
        +Guid UserId
        +AdminRole AdminType
        +List~string~ Permissions
        +DateTime LastLoginAt
        +ManageUser(userId: Guid, action: AdminAction) void
        +ApproveProvider(providerId: Guid) void
        +HandleDispute(disputeId: Guid) void
    }

    class Service {
        +Guid Id
        +string Title
        +string Description
        +decimal BasePrice
        +ServiceCategory Category
        +Guid ProviderId
        +List~string~ RequiredSkills
        +int EstimatedDurationHours
        +bool IsActive
        +DateTime CreatedAt
        +List~Booking~ Bookings
        +UpdatePricing(newPrice: decimal) void
        +DeactivateService() void
    }

    class ServiceCategory {
        +Guid Id
        +string Name
        +string Description
        +string IconUrl
        +List~ServiceSubCategory~ SubCategories
        +bool IsActive
        +AddSubCategory(name: string, description: string) ServiceSubCategory
    }

    class Booking {
        +Guid Id
        +Guid ClientId
        +Guid ProviderId
        +Guid ServiceId
        +DateTime ScheduledDate
        +BookingStatus Status
        +decimal TotalAmount
        +decimal CommissionAmount
        +string ClientNotes
        +string ProviderNotes
        +Address ServiceAddress
        +DateTime CreatedAt
        +DateTime UpdatedAt
        +List~Payment~ Payments
        +ConfirmBooking() void
        +CancelBooking(reason: string) void
        +CompleteBooking() void
        +CalculateCommission() decimal
    }

    class Payment {
        +Guid Id
        +Guid BookingId
        +decimal Amount
        +PaymentMethod Method
        +PaymentStatus Status
        +string TransactionId
        +string GatewayReference
        +DateTime ProcessedAt
        +string FailureReason
        +ProcessPayment() PaymentResult
        +RefundPayment(amount: decimal) PaymentResult
    }

    class Review {
        +Guid Id
        +Guid BookingId
        +Guid ClientId
        +Guid ProviderId
        +int Rating
        +string Comment
        +DateTime CreatedAt
        +bool IsVisible
        +ReviewType Type
        +ModerateReview(isApproved: bool) void
    }

    class Notification {
        +Guid Id
        +Guid UserId
        +NotificationType Type
        +string Title
        +string Message
        +bool IsRead
        +DateTime CreatedAt
        +DateTime ReadAt
        +Dictionary~string,object~ Metadata
        +MarkAsRead() void
        +SendPushNotification() void
    }

    class Address {
        +Guid Id
        +string Street
        +string City
        +string PostalCode
        +string Country
        +decimal Latitude
        +decimal Longitude
        +bool IsDefault
        +CalculateDistance(other: Address) decimal
    }

    class Schedule {
        +Guid Id
        +Guid ProviderId
        +List~TimeSlot~ AvailableSlots
        +List~TimeSlot~ BookedSlots
        +AddAvailability(startTime: DateTime, endTime: DateTime) void
        +IsAvailable(requestedTime: DateTime, duration: int) bool
        +BookSlot(startTime: DateTime, endTime: DateTime) void
    }

    %% Application Services
    class UserService {
        +RegisterUser(dto: UserRegistrationDto) Result~User~
        +AuthenticateUser(email: string, password: string) Result~AuthToken~
        +GetUserProfile(userId: Guid) UserProfileDto
        +UpdateUserProfile(userId: Guid, dto: UserProfileDto) Result
        +DeactivateUser(userId: Guid) result
    }

    class BookingService {
        +CreateBooking(dto: CreateBookingDto) Result~Booking~
        +GetBookingDetails(bookingId: Guid) BookingDetailsDto
        +GetUserBookings(userId: Guid, role: UserRole) List~BookingDto~
        +CancelBooking(bookingId: Guid, reason: string) Result
        +CompleteBooking(bookingId: Guid) Result
        +GetBookingHistory(userId: Guid) List~BookingHistoryDto~
    }

    class PaymentService {
        +ProcessPayment(dto: PaymentRequestDto) Result~Payment~
        +RefundPayment(paymentId: Guid, amount: decimal) Result~Payment~
        +GetPaymentHistory(userId: Guid) List~PaymentDto~
        +CalculateCommission(booking: Booking) decimal
        +ProcessCommissionPayment(providerId: Guid) Result
    }

    class ServiceCatalogService {
        +CreateService(dto: CreateServiceDto) Result~Service~
        +SearchServices(criteria: SearchCriteria) List~ServiceDto~
        +GetServiceDetails(serviceId: Guid) ServiceDetailsDto
        +UpdateService(serviceId: Guid, dto: UpdateServiceDto) Result
        +GetServicesByProvider(providerId: Guid) List~ServiceDto~
        +GetServiceCategories() List~ServiceCategoryDto~
    }

    class NotificationService {
        +SendNotification(notification: Notification) Result
        +GetUserNotifications(userId: Guid) List~NotificationDto~
        +MarkNotificationAsRead(notificationId: Guid) Result
        +SendBookingReminder(bookingId: Guid) Result
        +SendPaymentNotification(paymentId: Guid) Result
    }

    class ReviewService {
        +CreateReview(dto: CreateReviewDto) Result~Review~
        +GetServiceReviews(serviceId: Guid) List~ReviewDto~
        +GetProviderReviews(providerId: Guid) List~ReviewDto~
        +ModerateReview(reviewId: Guid, isApproved: bool) Result
        +CalculateAverageRating(providerId: Guid) decimal
    }

    %% Infrastructure Services
    class PaymentGatewayService {
        +ProcessStripePayment(dto: StripePaymentDto) PaymentResult
        +ProcessPayPalPayment(dto: PayPalPaymentDto) PaymentResult
        +ProcessMobileMoneyPayment(dto: MobileMoneyDto) PaymentResult
        +HandleWebhook(provider: string, payload: string) Result
    }

    class EmailService {
        +SendWelcomeEmail(user: User) Result
        +SendBookingConfirmation(booking: Booking) Result
        +SendPasswordReset(email: string, token: string) Result
        +SendInvoice(payment: Payment) Result
    }

    class SMSService {
        +SendBookingReminder(phoneNumber: string, booking: Booking) Result
        +SendVerificationCode(phoneNumber: string, code: string) Result
        +SendPaymentNotification(phoneNumber: string, payment: Payment) Result
    }

    class GeolocationService {
        +CalculateDistance(address1: Address, address2: Address) decimal
        +FindNearbyProviders(address: Address, radius: decimal) List~Provider~
        +GeocodeAddress(address: string) Coordinates
        +GetAddressFromCoordinates(lat: decimal, lng: decimal) string
    }

    %% Enums
    class UserRole {
        <<enumeration>>
        AdminGlobal
        Manager
        Support
        Supervisor
        Client
        Provider
    }

    class BookingStatus {
        <<enumeration>>
        Pending
        Confirmed
        InProgress
        Completed
        Cancelled
        Disputed
    }

    class PaymentStatus {
        <<enumeration>>
        Pending
        Processing
        Completed
        Failed
        Refunded
        Cancelled
    }

    class PaymentMethod {
        <<enumeration>>
        CreditCard
        PayPal
        BankTransfer
        MobileMoneyMTN
        MobileMoneyOrange
        Flutterwave
    }

    class NotificationType {
        <<enumeration>>
        BookingCreated
        BookingConfirmed
        BookingReminder
        PaymentProcessed
        ReviewReceived
        SystemAlert
    }

    %% Relationships
    User ||--|| Client : "extends"
    User ||--|| Provider : "extends"
    User ||--|| Admin : "extends"
    User ||--o{ Notification : "receives"
    User ||--|| Address : "has"

    Provider ||--o{ Service : "offers"
    Provider ||--|| Schedule : "has"
    Provider ||--o{ Review : "receives"

    Client ||--o{ Booking : "creates"
    Client ||--o{ Review : "writes"

    Service ||--|| ServiceCategory : "belongs to"
    Service ||--o{ Booking : "booked for"

    Booking ||--|| Client : "made by"
    Booking ||--|| Provider : "assigned to"
    Booking ||--|| Service : "for"
    Booking ||--o{ Payment : "has"
    Booking ||--o| Review : "generates"

    Payment ||--|| Booking : "for"

    Review ||--|| Booking : "about"
    Review ||--|| Client : "by"
    Review ||--|| Provider : "for"

    UserService ..> User : "manages"
    BookingService ..> Booking : "manages"
    PaymentService ..> Payment : "manages"
    ServiceCatalogService ..> Service : "manages"
    NotificationService ..> Notification : "manages"
    ReviewService ..> Review : "manages"

    PaymentService ..> PaymentGatewayService : "uses"
    NotificationService ..> EmailService : "uses"
    NotificationService ..> SMSService : "uses"
    BookingService ..> GeolocationService : "uses"