using MediatR;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Application.Handlers;

/// <summary>
/// Handler pour la création d'une réservation
/// </summary>
public class CreateBookingCommandHandler : IRequestHandler<CreateBookingCommand, BookingResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IBookingRepository _bookingRepository;
    private readonly IServiceRepository _serviceRepository;
    private readonly IUserRepository _userRepository;
    private readonly ICommissionService _commissionService;
    private readonly INotificationService _notificationService;
    private readonly IBookingRealtimeService _realtimeService;

    public CreateBookingCommandHandler(
        IUnitOfWork unitOfWork,
        IBookingRepository bookingRepository,
        IServiceRepository serviceRepository,
        IUserRepository userRepository,
        ICommissionService commissionService,
        INotificationService notificationService,
        IBookingRealtimeService realtimeService)
    {
        _unitOfWork = unitOfWork;
        _bookingRepository = bookingRepository;
        _serviceRepository = serviceRepository;
        _userRepository = userRepository;
        _commissionService = commissionService;
        _notificationService = notificationService;
        _realtimeService = realtimeService;
    }

    public async Task<BookingResponse> Handle(CreateBookingCommand request, CancellationToken cancellationToken)
    {
        // 1. Valider les données de base
        await ValidateBasicData(request, cancellationToken);

        // 2. Récupérer les entités nécessaires
        var client = await _userRepository.GetByIdAsync(request.ClientId, cancellationToken);
        var service = await _serviceRepository.GetByIdAsync(request.BookingData.ServiceId, cancellationToken);
        var provider = await _userRepository.GetByIdAsync(request.BookingData.ProviderId, cancellationToken);

        if (client == null)
            throw new ArgumentException("Client not found");
        if (service == null)
            throw new ArgumentException("Service not found");
        if (provider == null)
            throw new ArgumentException("Provider not found");

        // 3. Valider la disponibilité
        await ValidateAvailability(service, request.BookingData, cancellationToken);

        // 4. Valider la zone de service
        await ValidateServiceArea(service, request.BookingData.ServiceAddress, cancellationToken);

        // 5. Calculer le prix
        var pricing = await CalculatePricing(service, request.BookingData, cancellationToken);

        // 6. Créer la réservation
        var serviceAddress = request.BookingData.ServiceAddress.ToServiceAddress();
        var booking = new Booking(
            request.ClientId,
            request.BookingData.ProviderId,
            request.BookingData.ServiceId,
            request.BookingData.ScheduledDate,
            pricing.TotalPrice,
            serviceAddress,
            request.BookingData.EstimatedDurationMinutes,
            request.BookingData.ClientNotes,
            request.BookingData.IsUrgent);

        // 7. Calculer et appliquer la commission
        var commissionRate = await _commissionService.GetCommissionRateAsync(
            service.CategoryId, 
            request.BookingData.ProviderId, 
            cancellationToken);
        booking.CalculateCommission(commissionRate);

        // 8. Sauvegarder
        await _bookingRepository.AddAsync(booking, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // 9. Envoyer les notifications
        await SendNotifications(booking, client, provider, service, cancellationToken);

        // 10. Notifier en temps réel
        await _realtimeService.NotifyBookingCreatedAsync(booking.Id, cancellationToken);

        // 11. Retourner la réponse
        return await MapToBookingResponse(booking, client, provider, service);
    }

    private async Task ValidateBasicData(CreateBookingCommand request, CancellationToken cancellationToken)
    {
        // Valider que la date n'est pas dans le passé
        if (request.BookingData.ScheduledDate <= DateTime.UtcNow)
            throw new ArgumentException("Scheduled date must be in the future");

        // Valider la durée
        if (request.BookingData.EstimatedDurationMinutes < 15)
            throw new ArgumentException("Duration must be at least 15 minutes");

        // Valider que le client et le prestataire sont différents
        if (request.ClientId == request.BookingData.ProviderId)
            throw new ArgumentException("Client and provider cannot be the same");
    }

    private async Task ValidateAvailability(Service service, CreateBookingRequest bookingData, CancellationToken cancellationToken)
    {
        // Vérifier que le service est actif
        if (!service.IsActive)
            throw new ArgumentException("Service is not active");

        // Vérifier les contraintes de préavis
        var minAdvanceTime = DateTime.UtcNow.AddHours(service.MinAdvanceHours);
        var maxAdvanceTime = DateTime.UtcNow.AddDays(service.MaxAdvanceDays);

        if (bookingData.ScheduledDate < minAdvanceTime)
            throw new ArgumentException($"Booking must be made at least {service.MinAdvanceHours} hours in advance");

        if (bookingData.ScheduledDate > maxAdvanceTime)
            throw new ArgumentException($"Booking cannot be made more than {service.MaxAdvanceDays} days in advance");

        // Vérifier la durée
        if (bookingData.EstimatedDurationMinutes < service.MinDurationMinutes)
            throw new ArgumentException($"Duration must be at least {service.MinDurationMinutes} minutes");

        if (bookingData.EstimatedDurationMinutes > service.MaxDurationMinutes)
            throw new ArgumentException($"Duration cannot exceed {service.MaxDurationMinutes} minutes");

        // Vérifier la disponibilité urgente si nécessaire
        if (bookingData.IsUrgent && !service.IsUrgentAvailable)
            throw new ArgumentException("Urgent booking is not available for this service");

        // Vérifier les créneaux disponibles
        var isAvailable = await _serviceRepository.IsAvailableAsync(
            service.Id, 
            bookingData.ScheduledDate, 
            bookingData.EstimatedDurationMinutes, 
            cancellationToken);

        if (!isAvailable)
            throw new ArgumentException("The requested time slot is not available");
    }

    private async Task ValidateServiceArea(Service service, ServiceAddressDto clientAddress, CancellationToken cancellationToken)
    {
        // Si le service a une zone limitée, vérifier la distance
        if (service.ServiceRadiusKm > 0)
        {
            var providerAddress = await _serviceRepository.GetProviderAddressAsync(service.ProviderId, cancellationToken);
            if (providerAddress != null && clientAddress.Latitude.HasValue && clientAddress.Longitude.HasValue)
            {
                var clientServiceAddress = clientAddress.ToServiceAddress();
                var distance = providerAddress.CalculateDistanceKm(clientServiceAddress);
                
                if (distance.HasValue && distance.Value > service.ServiceRadiusKm)
                    throw new ArgumentException($"Service area is limited to {service.ServiceRadiusKm}km radius");
            }
        }
    }

    private async Task<BookingPriceResponse> CalculatePricing(Service service, CreateBookingRequest bookingData, CancellationToken cancellationToken)
    {
        var basePrice = service.CalculatePrice(bookingData.EstimatedDurationMinutes, bookingData.IsUrgent);
        var urgentSurcharge = bookingData.IsUrgent ? (long)(basePrice * service.UrgentSurcharge) : 0;
        var totalPrice = basePrice + urgentSurcharge;

        return new BookingPriceResponse
        {
            BasePrice = basePrice - urgentSurcharge,
            UrgentSurcharge = urgentSurcharge,
            TotalPrice = totalPrice,
            Currency = service.Currency,
            TotalPriceFormatted = FormatPrice(totalPrice, service.Currency)
        };
    }

    private Task SendNotifications(Booking booking, User client, User provider, Service service, CancellationToken cancellationToken)
    {
        // TODO: Implémenter les notifications une fois les méthodes ajoutées à INotificationService
        // Pour le moment, on skip les notifications
        return Task.CompletedTask;
    }

    private async Task<BookingResponse> MapToBookingResponse(Booking booking, User client, User provider, Service service)
    {
        return new BookingResponse
        {
            Id = booking.Id,
            ClientId = booking.ClientId,
            Client = MapToUserSummary(client),
            ProviderId = booking.ProviderId,
            Provider = MapToUserSummary(provider),
            ServiceId = booking.ServiceId,
            Service = MapToServiceSummary(service),
            ScheduledDate = booking.ScheduledDate,
            ScheduledEndDate = booking.ScheduledEndDate,
            Status = booking.Status,
            StatusDescription = booking.Status.ToString(),
            TotalAmount = booking.TotalAmount,
            TotalAmountFormatted = FormatPrice(booking.TotalAmount, booking.Currency),
            CommissionAmount = booking.CommissionAmount,
            Currency = booking.Currency,
            ClientNotes = booking.ClientNotes,
            ProviderNotes = booking.ProviderNotes,
            ServiceAddress = MapToServiceAddressDto(booking.ServiceAddress),
            EstimatedDurationMinutes = booking.EstimatedDurationMinutes,
            ActualDurationMinutes = booking.ActualDurationMinutes,
            ConfirmedAt = booking.ConfirmedAt,
            StartedAt = booking.StartedAt,
            CompletedAt = booking.CompletedAt,
            CancelledAt = booking.CancelledAt,
            CancellationReason = booking.CancellationReason,
            ExpiresAt = booking.ExpiresAt,
            TimeUntilExpiration = booking.GetTimeUntilExpiration(),
            IsUrgent = booking.IsUrgent,
            IsRecurring = booking.IsRecurring,
            CanBeCancelled = booking.CanBeCancelled(),
            CanBeModified = booking.CanBeModified(),
            CreatedAt = booking.CreatedAt,
            UpdatedAt = booking.UpdatedAt
        };
    }

    private UserSummaryDto MapToUserSummary(User user)
    {
        return new UserSummaryDto
        {
            Id = user.Id,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.GetFullName(),
            Email = user.Email.Value,
            PhoneNumber = user.PhoneNumber?.Value,
            AvatarUrl = user.AvatarUrl
        };
    }

    private ServiceSummaryDto MapToServiceSummary(Service service)
    {
        return new ServiceSummaryDto
        {
            Id = service.Id,
            Name = service.Name,
            ShortDescription = service.ShortDescription,
            BasePrice = service.BasePrice,
            BasePriceFormatted = FormatPrice(service.BasePrice, service.Currency),
            PricingUnit = service.PricingUnit,
            Currency = service.Currency,
            AverageRating = service.AverageRating,
            TotalReviews = service.TotalReviews
        };
    }

    private ServiceAddressDto MapToServiceAddressDto(ServiceAddress address)
    {
        return new ServiceAddressDto
        {
            AddressLine1 = address.AddressLine1,
            AddressLine2 = address.AddressLine2,
            City = address.City,
            PostalCode = address.PostalCode,
            Region = address.Region,
            Country = address.Country,
            Latitude = address.Latitude,
            Longitude = address.Longitude,
            AccessInstructions = address.AccessInstructions,
            Floor = address.Floor,
            AccessCode = address.AccessCode
        };
    }

    private string FormatPrice(long priceInCents, string currency)
    {
        var price = priceInCents / 100.0m;
        return currency switch
        {
            "EUR" => $"{price:F2} €",
            "USD" => $"${price:F2}",
            "GBP" => $"£{price:F2}",
            _ => $"{price:F2} {currency}"
        };
    }
}
