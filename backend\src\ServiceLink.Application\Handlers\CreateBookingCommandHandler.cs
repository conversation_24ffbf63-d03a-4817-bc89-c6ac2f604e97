using MediatR;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;

namespace ServiceLink.Application.Handlers;

/// <summary>
/// Handler pour la création d'une nouvelle réservation
/// </summary>
public class CreateBookingCommandHandler : IRequestHandler<CreateBookingCommand, BookingResponse>
{
    private readonly IRepository<Booking> _bookingRepository;
    private readonly IRepository<Service> _serviceRepository;
    private readonly IRepository<User> _userRepository;
    private readonly IBookingRealtimeService _bookingRealtimeService;
    private readonly IExternalNotificationService _notificationService;
    private readonly ILogger<CreateBookingCommandHandler> _logger;

    public CreateBookingCommandHandler(
        IRepository<Booking> bookingRepository,
        IRepository<Service> serviceRepository,
        IRepository<User> userRepository,
        IBookingRealtimeService bookingRealtimeService,
        IExternalNotificationService notificationService,
        ILogger<CreateBookingCommandHandler> logger)
    {
        _bookingRepository = bookingRepository;
        _serviceRepository = serviceRepository;
        _userRepository = userRepository;
        _bookingRealtimeService = bookingRealtimeService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public async Task<BookingResponse> Handle(CreateBookingCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Création d'une nouvelle réservation pour le client {ClientId} et le prestataire {ProviderId}",
            request.ClientId, request.ProviderId);

        try
        {
            // 1. Valider l'existence du service
            var service = await _serviceRepository.GetByIdAsync(request.ServiceId, cancellationToken);
            if (service == null)
            {
                throw new ArgumentException($"Service avec l'ID {request.ServiceId} introuvable");
            }

            if (!service.IsActive)
            {
                throw new InvalidOperationException("Le service n'est pas disponible");
            }

            // 2. Valider l'existence du client et du prestataire
            var client = await _userRepository.GetByIdAsync(request.ClientId, cancellationToken);
            if (client == null)
            {
                throw new ArgumentException($"Client avec l'ID {request.ClientId} introuvable");
            }

            var provider = await _userRepository.GetByIdAsync(request.ProviderId, cancellationToken);
            if (provider == null)
            {
                throw new ArgumentException($"Prestataire avec l'ID {request.ProviderId} introuvable");
            }

            if (provider.Role != UserRole.Provider)
            {
                throw new InvalidOperationException("L'utilisateur spécifié n'est pas un prestataire");
            }

            // 3. Valider la date de réservation
            if (request.ScheduledDate <= DateTime.UtcNow)
            {
                throw new ArgumentException("La date de réservation doit être dans le futur");
            }

            if (!service.IsAvailableAt(request.ScheduledDate))
            {
                throw new InvalidOperationException("Le service n'est pas disponible à cette date");
            }

            // 4. Vérifier la disponibilité du prestataire
            var isAvailable = await CheckProviderAvailabilityAsync(
                request.ProviderId, 
                request.ScheduledDate, 
                request.EstimatedDurationMinutes,
                cancellationToken);

            if (!isAvailable)
            {
                throw new InvalidOperationException("Le prestataire n'est pas disponible à cette date et heure");
            }

            // 5. Calculer le prix
            var totalPrice = service.CalculatePrice(request.EstimatedDurationMinutes, request.IsUrgent);

            // 6. Créer la réservation
            var booking = new Booking(
                request.ClientId,
                request.ProviderId,
                request.ServiceId,
                request.ScheduledDate,
                totalPrice,
                request.ServiceAddress,
                request.EstimatedDurationMinutes,
                request.ClientNotes,
                request.IsUrgent,
                service.Currency);

            // 7. Calculer la commission
            var commissionRate = await GetCommissionRateAsync(service.CategoryId, provider.Id, cancellationToken);
            booking.CalculateCommission(commissionRate);

            // 8. Sauvegarder la réservation
            await _bookingRepository.AddAsync(booking, cancellationToken);
            await _bookingRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Réservation {BookingId} créée avec succès", booking.Id);

            // 9. Envoyer les notifications
            await SendBookingNotificationsAsync(booking, service, client, provider, cancellationToken);

            // 10. Notification temps réel
            await _bookingRealtimeService.NotifyNewBookingAsync(new BookingNotificationDto
            {
                BookingId = booking.Id,
                ClientId = booking.ClientId,
                ProviderId = booking.ProviderId,
                ServiceName = service.Name,
                BookingDate = booking.ScheduledDate,
                Status = booking.Status.ToString(),
                Price = (decimal)booking.TotalAmount / 100
            }, cancellationToken);

            // 11. Retourner la réponse
            return await MapToBookingResponseAsync(booking, service, client, provider, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la réservation pour le client {ClientId}", request.ClientId);
            throw;
        }
    }

    /// <summary>
    /// Vérifie la disponibilité du prestataire
    /// </summary>
    private async Task<bool> CheckProviderAvailabilityAsync(
        Guid providerId, 
        DateTime scheduledDate, 
        int durationMinutes,
        CancellationToken cancellationToken)
    {
        var endDate = scheduledDate.AddMinutes(durationMinutes);

        // Vérifier les conflits avec les réservations existantes
        var conflictingBookings = await _bookingRepository.FindAsync(
            b => b.ProviderId == providerId &&
                 b.Status != BookingStatus.Cancelled &&
                 b.Status != BookingStatus.Rejected &&
                 b.Status != BookingStatus.Expired &&
                 ((b.ScheduledDate <= scheduledDate && b.ScheduledEndDate > scheduledDate) ||
                  (b.ScheduledDate < endDate && b.ScheduledEndDate >= endDate) ||
                  (b.ScheduledDate >= scheduledDate && b.ScheduledEndDate <= endDate)),
            cancellationToken);

        return !conflictingBookings.Any();
    }

    /// <summary>
    /// Obtient le taux de commission applicable
    /// </summary>
    private async Task<decimal> GetCommissionRateAsync(Guid categoryId, Guid providerId, CancellationToken cancellationToken)
    {
        // TODO: Implémenter la logique de calcul du taux de commission
        // basée sur la catégorie, le niveau du prestataire, etc.
        return 5.0m; // 5% par défaut
    }

    /// <summary>
    /// Envoie les notifications de réservation
    /// </summary>
    private async Task SendBookingNotificationsAsync(
        Booking booking,
        Service service,
        User client,
        User provider,
        CancellationToken cancellationToken)
    {
        try
        {
            // Notification au prestataire
            var providerNotification = new ExternalNotificationRequest
            {
                Type = ExternalNotificationType.Email,
                Recipients = new List<string> { provider.Email.Value },
                TemplateName = "booking_request",
                TemplateData = new Dictionary<string, object>
                {
                    ["providerName"] = provider.FullName,
                    ["clientName"] = client.FullName,
                    ["serviceName"] = service.Name,
                    ["bookingDate"] = booking.ScheduledDate.ToString("dd/MM/yyyy HH:mm"),
                    ["bookingAmount"] = $"{booking.TotalAmount / 100:F2} {booking.Currency}",
                    ["clientNotes"] = booking.ClientNotes,
                    ["bookingId"] = booking.Id.ToString()
                },
                Priority = booking.IsUrgent ? ExternalNotificationPriority.High : ExternalNotificationPriority.Normal
            };

            await _notificationService.SendNotificationAsync(providerNotification, cancellationToken);

            // Notification de confirmation au client
            var clientNotification = new ExternalNotificationRequest
            {
                Type = ExternalNotificationType.Email,
                Recipients = new List<string> { client.Email.Value },
                TemplateName = "booking_confirmation",
                TemplateData = new Dictionary<string, object>
                {
                    ["clientName"] = client.FullName,
                    ["providerName"] = provider.FullName,
                    ["serviceName"] = service.Name,
                    ["bookingDate"] = booking.ScheduledDate.ToString("dd/MM/yyyy HH:mm"),
                    ["bookingAmount"] = $"{booking.TotalAmount / 100:F2} {booking.Currency}",
                    ["bookingId"] = booking.Id.ToString()
                }
            };

            await _notificationService.SendNotificationAsync(clientNotification, cancellationToken);

            _logger.LogInformation("Notifications envoyées pour la réservation {BookingId}", booking.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi des notifications pour la réservation {BookingId}", booking.Id);
            // Ne pas faire échouer la création de réservation pour un problème de notification
        }
    }

    /// <summary>
    /// Mappe une entité Booking vers BookingResponse
    /// </summary>
    private async Task<BookingResponse> MapToBookingResponseAsync(
        Booking booking,
        Service service,
        User client,
        User provider,
        CancellationToken cancellationToken)
    {
        return new BookingResponse
        {
            Id = booking.Id,
            ClientId = booking.ClientId,
            Client = new UserSummaryDto
            {
                Id = client.Id,
                FirstName = client.FirstName,
                LastName = client.LastName,
                FullName = client.FullName,
                Email = client.Email.Value,
                PhoneNumber = client.PhoneNumber?.Value
            },
            ProviderId = booking.ProviderId,
            Provider = new UserSummaryDto
            {
                Id = provider.Id,
                FirstName = provider.FirstName,
                LastName = provider.LastName,
                FullName = provider.FullName,
                Email = provider.Email.Value,
                PhoneNumber = provider.PhoneNumber?.Value
            },
            ServiceId = booking.ServiceId,
            Service = new ServiceSummaryDto
            {
                Id = service.Id,
                Name = service.Name,
                ShortDescription = service.ShortDescription,
                BasePrice = service.BasePrice,
                BasePriceFormatted = $"{service.BasePrice / 100:F2} {service.Currency}",
                PricingUnit = service.PricingUnit,
                Currency = service.Currency
            },
            ScheduledDate = booking.ScheduledDate,
            ScheduledEndDate = booking.ScheduledEndDate,
            Status = booking.Status,
            StatusDescription = booking.Status.GetDescription(),
            TotalAmount = booking.TotalAmount,
            TotalAmountFormatted = $"{booking.TotalAmount / 100:F2} {booking.Currency}",
            CommissionAmount = booking.CommissionAmount,
            Currency = booking.Currency,
            ClientNotes = booking.ClientNotes,
            ProviderNotes = booking.ProviderNotes,
            ServiceAddress = MapServiceAddress(booking.ServiceAddress),
            EstimatedDurationMinutes = booking.EstimatedDurationMinutes,
            ActualDurationMinutes = booking.ActualDurationMinutes,
            ConfirmedAt = booking.ConfirmedAt,
            StartedAt = booking.StartedAt,
            CompletedAt = booking.CompletedAt,
            CancelledAt = booking.CancelledAt,
            CancellationReason = booking.CancellationReason,
            ExpiresAt = booking.ExpiresAt,
            TimeUntilExpiration = booking.GetTimeUntilExpiration(),
            IsUrgent = booking.IsUrgent,
            IsRecurring = booking.IsRecurring,
            CanBeCancelled = booking.CanBeCancelled(),
            CanBeModified = booking.CanBeModified(),
            CreatedAt = booking.CreatedAt,
            UpdatedAt = booking.UpdatedAt,
            Payments = new List<PaymentSummaryDto>(), // TODO: Charger les paiements
            Review = null // TODO: Charger l'avis si disponible
        };
    }

    /// <summary>
    /// Mappe ServiceAddress vers ServiceAddressDto
    /// </summary>
    private static ServiceAddressDto MapServiceAddress(ServiceAddress address)
    {
        return new ServiceAddressDto
        {
            AddressLine1 = address.AddressLine1,
            AddressLine2 = address.AddressLine2,
            City = address.City,
            PostalCode = address.PostalCode,
            Region = address.Region,
            Country = address.Country,
            Latitude = address.Latitude,
            Longitude = address.Longitude,
            AccessInstructions = address.AccessInstructions,
            Floor = address.Floor,
            AccessCode = address.AccessCode
        };
    }
}
