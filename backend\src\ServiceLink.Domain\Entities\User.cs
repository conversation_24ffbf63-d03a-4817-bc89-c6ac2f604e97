using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Events;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Domain.Entities;

/// <summary>
/// Entité représentant un utilisateur dans ServiceLink
/// Peut être un Client, Prestataire ou Administrateur
/// </summary>
public class User : BaseEntity
{
    /// <summary>
    /// Constructeur privé pour Entity Framework
    /// </summary>
    private User() { }

    /// <summary>
    /// Constructeur pour créer un nouvel utilisateur
    /// </summary>
    /// <param name="email">Adresse email</param>
    /// <param name="firstName">Prénom</param>
    /// <param name="lastName">Nom de famille</param>
    /// <param name="role">Rôle de l'utilisateur</param>
    /// <param name="phoneNumber">Numéro de téléphone (optionnel)</param>
    /// <param name="createdBy">Utilisateur créateur</param>
    public User(Email email, string firstName, string lastName, UserRole role, 
                PhoneNumber? phoneNumber = null, Guid? createdBy = null)
    {
        Email = email ?? throw new ArgumentNullException(nameof(email));
        FirstName = ValidateName(firstName, nameof(firstName));
        LastName = ValidateName(lastName, nameof(lastName));
        Role = role;
        PhoneNumber = phoneNumber;
        
        // Propriétés par défaut
        IsEmailConfirmed = false;
        IsPhoneConfirmed = false;
        IsActive = true;
        LastLoginAt = null;
        ProfileCompletionPercentage = CalculateProfileCompletion();
        
        SetCreatedBy(createdBy);
        
        // Événement de domaine
        AddDomainEvent(new UserCreatedEvent(Id, email.Value, role, createdBy));
    }

    /// <summary>
    /// Adresse email de l'utilisateur (unique)
    /// </summary>
    public Email Email { get; private set; } = null!;

    /// <summary>
    /// Prénom de l'utilisateur
    /// </summary>
    public string FirstName { get; private set; } = string.Empty;

    /// <summary>
    /// Nom de famille de l'utilisateur
    /// </summary>
    public string LastName { get; private set; } = string.Empty;

    /// <summary>
    /// Nom complet (prénom + nom)
    /// </summary>
    public string FullName => $"{FirstName} {LastName}".Trim();

    /// <summary>
    /// Numéro de téléphone (optionnel)
    /// </summary>
    public PhoneNumber? PhoneNumber { get; private set; }

    /// <summary>
    /// Rôle de l'utilisateur dans le système
    /// </summary>
    public UserRole Role { get; private set; }

    /// <summary>
    /// Hash du mot de passe
    /// </summary>
    public string PasswordHash { get; private set; } = string.Empty;

    /// <summary>
    /// Salt pour le hachage du mot de passe
    /// </summary>
    public string PasswordSalt { get; private set; } = string.Empty;

    /// <summary>
    /// Indique si l'email est confirmé
    /// </summary>
    public bool IsEmailConfirmed { get; private set; }

    /// <summary>
    /// Indique si le numéro de téléphone est confirmé
    /// </summary>
    public bool IsPhoneConfirmed { get; private set; }

    /// <summary>
    /// Indique si l'utilisateur est actif
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// Date de dernière connexion
    /// </summary>
    public DateTime? LastLoginAt { get; private set; }

    /// <summary>
    /// Nombre de tentatives de connexion échouées
    /// </summary>
    public int FailedLoginAttempts { get; private set; }

    /// <summary>
    /// Date de verrouillage du compte (si applicable)
    /// </summary>
    public DateTime? LockedUntil { get; private set; }

    /// <summary>
    /// Token de confirmation d'email
    /// </summary>
    public string? EmailConfirmationToken { get; private set; }

    /// <summary>
    /// Date d'expiration du token de confirmation d'email
    /// </summary>
    public DateTime? EmailConfirmationTokenExpiry { get; private set; }

    /// <summary>
    /// Token de réinitialisation de mot de passe
    /// </summary>
    public string? PasswordResetToken { get; private set; }

    /// <summary>
    /// Date d'expiration du token de réinitialisation
    /// </summary>
    public DateTime? PasswordResetTokenExpiry { get; private set; }

    /// <summary>
    /// Secret pour l'authentification à deux facteurs
    /// </summary>
    public string? TwoFactorSecret { get; private set; }

    /// <summary>
    /// Indique si l'authentification à deux facteurs est activée
    /// </summary>
    public bool IsTwoFactorEnabled { get; private set; }

    /// <summary>
    /// Codes de récupération pour l'authentification à deux facteurs
    /// </summary>
    public string? TwoFactorRecoveryCodes { get; private set; }

    /// <summary>
    /// URL de l'avatar de l'utilisateur
    /// </summary>
    public string? AvatarUrl { get; private set; }

    /// <summary>
    /// Pourcentage de complétion du profil
    /// </summary>
    public int ProfileCompletionPercentage { get; private set; }

    /// <summary>
    /// Préférences de l'utilisateur (JSON)
    /// </summary>
    public string? Preferences { get; private set; }

    /// <summary>
    /// Langue préférée de l'utilisateur
    /// </summary>
    public string Language { get; private set; } = "fr-FR";

    /// <summary>
    /// Fuseau horaire de l'utilisateur
    /// </summary>
    public string TimeZone { get; private set; } = "Europe/Paris";

    /// <summary>
    /// Date de dernière modification du mot de passe
    /// </summary>
    public DateTime? PasswordChangedAt { get; private set; }

    /// <summary>
    /// Valide un nom (prénom ou nom de famille)
    /// </summary>
    private static string ValidateName(string name, string paramName)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException($"Le {paramName} ne peut pas être vide.", paramName);

        name = name.Trim();

        if (name.Length < 2)
            throw new ArgumentException($"Le {paramName} doit contenir au moins 2 caractères.", paramName);

        if (name.Length > 50)
            throw new ArgumentException($"Le {paramName} ne peut pas dépasser 50 caractères.", paramName);

        return name;
    }

    /// <summary>
    /// Calcule le pourcentage de complétion du profil
    /// </summary>
    private int CalculateProfileCompletion()
    {
        var completedFields = 0;
        var totalFields = 8;

        if (!string.IsNullOrEmpty(FirstName)) completedFields++;
        if (!string.IsNullOrEmpty(LastName)) completedFields++;
        if (Email != null) completedFields++;
        if (PhoneNumber != null) completedFields++;
        if (IsEmailConfirmed) completedFields++;
        if (IsPhoneConfirmed) completedFields++;
        if (!string.IsNullOrEmpty(AvatarUrl)) completedFields++;
        if (IsTwoFactorEnabled) completedFields++;

        return (int)Math.Round((double)completedFields / totalFields * 100);
    }

    /// <summary>
    /// Met à jour les informations de base de l'utilisateur
    /// </summary>
    public void UpdateBasicInfo(string firstName, string lastName, PhoneNumber? phoneNumber = null, Guid? updatedBy = null)
    {
        FirstName = ValidateName(firstName, nameof(firstName));
        LastName = ValidateName(lastName, nameof(lastName));
        PhoneNumber = phoneNumber;
        
        ProfileCompletionPercentage = CalculateProfileCompletion();
        UpdateMetadata(updatedBy);
        
        AddDomainEvent(new UserUpdatedEvent(Id, updatedBy));
    }

    /// <summary>
    /// Définit le mot de passe de l'utilisateur
    /// </summary>
    public void SetPassword(string passwordHash, string passwordSalt, Guid? updatedBy = null)
    {
        if (string.IsNullOrWhiteSpace(passwordHash))
            throw new ArgumentException("Le hash du mot de passe ne peut pas être vide.", nameof(passwordHash));

        if (string.IsNullOrWhiteSpace(passwordSalt))
            throw new ArgumentException("Le salt du mot de passe ne peut pas être vide.", nameof(passwordSalt));

        PasswordHash = passwordHash;
        PasswordSalt = passwordSalt;
        PasswordChangedAt = DateTime.UtcNow;
        
        // Réinitialiser les tentatives de connexion échouées
        FailedLoginAttempts = 0;
        LockedUntil = null;
        
        UpdateMetadata(updatedBy);
        
        AddDomainEvent(new UserPasswordChangedEvent(Id, updatedBy));
    }

    /// <summary>
    /// Confirme l'adresse email de l'utilisateur
    /// </summary>
    public void ConfirmEmail(Guid? confirmedBy = null)
    {
        IsEmailConfirmed = true;
        EmailConfirmationToken = null;
        EmailConfirmationTokenExpiry = null;
        
        ProfileCompletionPercentage = CalculateProfileCompletion();
        UpdateMetadata(confirmedBy);
        
        AddDomainEvent(new UserEmailConfirmedEvent(Id, Email.Value, confirmedBy));
    }

    /// <summary>
    /// Confirme le numéro de téléphone de l'utilisateur
    /// </summary>
    public void ConfirmPhone(Guid? confirmedBy = null)
    {
        if (PhoneNumber == null)
            throw new InvalidOperationException("Aucun numéro de téléphone à confirmer.");

        IsPhoneConfirmed = true;
        ProfileCompletionPercentage = CalculateProfileCompletion();
        UpdateMetadata(confirmedBy);
        
        AddDomainEvent(new UserPhoneConfirmedEvent(Id, PhoneNumber.Value, confirmedBy));
    }

    /// <summary>
    /// Enregistre une connexion réussie
    /// </summary>
    public void RecordSuccessfulLogin()
    {
        LastLoginAt = DateTime.UtcNow;
        FailedLoginAttempts = 0;
        LockedUntil = null;
        
        UpdateMetadata();
        
        AddDomainEvent(new UserLoggedInEvent(Id));
    }

    /// <summary>
    /// Enregistre une tentative de connexion échouée
    /// </summary>
    public void RecordFailedLogin()
    {
        FailedLoginAttempts++;
        
        // Verrouiller le compte après 5 tentatives échouées
        if (FailedLoginAttempts >= 5)
        {
            LockedUntil = DateTime.UtcNow.AddMinutes(30);
            AddDomainEvent(new UserAccountLockedEvent(Id, LockedUntil.Value));
        }
        
        UpdateMetadata();
    }

    /// <summary>
    /// Vérifie si le compte est verrouillé
    /// </summary>
    public bool IsLocked()
    {
        return LockedUntil.HasValue && LockedUntil.Value > DateTime.UtcNow;
    }

    /// <summary>
    /// Déverrouille le compte
    /// </summary>
    public void UnlockAccount(Guid? unlockedBy = null)
    {
        FailedLoginAttempts = 0;
        LockedUntil = null;
        
        UpdateMetadata(unlockedBy);
        
        AddDomainEvent(new UserAccountUnlockedEvent(Id, unlockedBy));
    }

    /// <summary>
    /// Active l'authentification à deux facteurs
    /// </summary>
    public void EnableTwoFactor(string secret, string recoveryCodes, Guid? enabledBy = null)
    {
        if (string.IsNullOrWhiteSpace(secret))
            throw new ArgumentException("Le secret 2FA ne peut pas être vide.", nameof(secret));

        TwoFactorSecret = secret;
        TwoFactorRecoveryCodes = recoveryCodes;
        IsTwoFactorEnabled = true;
        
        ProfileCompletionPercentage = CalculateProfileCompletion();
        UpdateMetadata(enabledBy);
        
        AddDomainEvent(new UserTwoFactorEnabledEvent(Id, enabledBy));
    }

    /// <summary>
    /// Désactive l'authentification à deux facteurs
    /// </summary>
    public void DisableTwoFactor(Guid? disabledBy = null)
    {
        TwoFactorSecret = null;
        TwoFactorRecoveryCodes = null;
        IsTwoFactorEnabled = false;
        
        ProfileCompletionPercentage = CalculateProfileCompletion();
        UpdateMetadata(disabledBy);
        
        AddDomainEvent(new UserTwoFactorDisabledEvent(Id, disabledBy));
    }

    /// <summary>
    /// Désactive l'utilisateur
    /// </summary>
    public void Deactivate(Guid? deactivatedBy = null)
    {
        IsActive = false;
        UpdateMetadata(deactivatedBy);
        
        AddDomainEvent(new UserDeactivatedEvent(Id, deactivatedBy));
    }

    /// <summary>
    /// Réactive l'utilisateur
    /// </summary>
    public void Activate(Guid? activatedBy = null)
    {
        IsActive = true;
        UpdateMetadata(activatedBy);
        
        AddDomainEvent(new UserActivatedEvent(Id, activatedBy));
    }

    /// <summary>
    /// Change le rôle de l'utilisateur
    /// </summary>
    public void ChangeRole(UserRole newRole, Guid? changedBy = null)
    {
        var oldRole = Role;
        Role = newRole;
        
        UpdateMetadata(changedBy);
        
        AddDomainEvent(new UserRoleChangedEvent(Id, oldRole, newRole, changedBy));
    }

    /// <summary>
    /// Met à jour l'avatar de l'utilisateur
    /// </summary>
    public void UpdateAvatar(string avatarUrl, Guid? updatedBy = null)
    {
        AvatarUrl = avatarUrl;
        ProfileCompletionPercentage = CalculateProfileCompletion();
        
        UpdateMetadata(updatedBy);
    }

    /// <summary>
    /// Vérifie si l'utilisateur a une permission spécifique
    /// </summary>
    public bool HasPermission(string permission)
    {
        return Role.HasPermission(permission);
    }

    /// <summary>
    /// Vérifie si l'utilisateur peut accéder à une ressource
    /// </summary>
    public bool CanAccess(Guid resourceOwnerId)
    {
        // L'utilisateur peut accéder à ses propres ressources
        if (Id == resourceOwnerId)
            return true;

        // Les admins peuvent accéder à tout
        if (Role == UserRole.Admin)
            return true;

        return false;
    }

    /// <summary>
    /// Nettoie le token de confirmation d'email (pour l'infrastructure)
    /// </summary>
    public void ClearEmailConfirmationToken()
    {
        EmailConfirmationToken = null;
        EmailConfirmationTokenExpiry = null;
    }

    /// <summary>
    /// Nettoie le token de réinitialisation de mot de passe (pour l'infrastructure)
    /// </summary>
    public void ClearPasswordResetToken()
    {
        PasswordResetToken = null;
        PasswordResetTokenExpiry = null;
    }

    /// <summary>
    /// Obtient le nom complet de l'utilisateur
    /// </summary>
    public string GetFullName()
    {
        return $"{FirstName} {LastName}".Trim();
    }
}
