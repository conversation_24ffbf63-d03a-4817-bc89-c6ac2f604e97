using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using ServiceLink.Infrastructure.Services;
using System.Diagnostics;
using System.Text;

namespace ServiceLink.Infrastructure.Middleware;

/// <summary>
/// Middleware de sécurité pour les opérations de paiement
/// </summary>
public class PaymentSecurityMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PaymentSecurityMiddleware> _logger;
    private readonly PaymentSecurityOptions _options;

    public PaymentSecurityMiddleware(
        RequestDelegate next,
        ILogger<PaymentSecurityMiddleware> logger,
        PaymentSecurityOptions options)
    {
        _next = next;
        _logger = logger;
        _options = options;
    }

    /// <summary>
    /// Traite la requête avec les contrôles de sécurité
    /// </summary>
    /// <param name="context">Contexte HTTP</param>
    /// <param name="auditService">Service d'audit</param>
    /// <returns>Tâche asynchrone</returns>
    public async Task InvokeAsync(HttpContext context, IPaymentAuditService auditService)
    {
        // Vérifier si c'est une route de paiement
        if (!IsPaymentRoute(context.Request.Path))
        {
            await _next(context);
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        var clientIp = GetClientIpAddress(context);
        var userAgent = context.Request.Headers["User-Agent"].ToString();

        _logger.LogInformation("Requête de paiement reçue de {ClientIp}: {Method} {Path}", 
            clientIp, context.Request.Method, context.Request.Path);

        try
        {
            // Vérifications de sécurité
            await PerformSecurityChecks(context, clientIp);

            // Traiter la requête
            await _next(context);

            stopwatch.Stop();

            // Audit de succès
            if (context.User.Identity?.IsAuthenticated == true)
            {
                await LogSuccessfulOperation(context, auditService, clientIp, userAgent, stopwatch.ElapsedMilliseconds);
            }
        }
        catch (SecurityException ex)
        {
            stopwatch.Stop();
            _logger.LogWarning(ex, "Violation de sécurité détectée pour {ClientIp}: {Message}", clientIp, ex.Message);
            
            await LogSecurityViolation(context, auditService, clientIp, userAgent, ex.Message, stopwatch.ElapsedMilliseconds);
            
            context.Response.StatusCode = 403;
            await context.Response.WriteAsync("Accès refusé");
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Erreur lors du traitement de la requête de paiement pour {ClientIp}", clientIp);
            
            if (context.User.Identity?.IsAuthenticated == true)
            {
                await LogFailedOperation(context, auditService, clientIp, userAgent, ex.Message, stopwatch.ElapsedMilliseconds);
            }
            
            throw;
        }
    }

    /// <summary>
    /// Vérifie si la route concerne les paiements
    /// </summary>
    /// <param name="path">Chemin de la requête</param>
    /// <returns>True si c'est une route de paiement</returns>
    private static bool IsPaymentRoute(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant() ?? string.Empty;
        return pathValue.Contains("/payment") || pathValue.Contains("/webhook");
    }

    /// <summary>
    /// Effectue les vérifications de sécurité
    /// </summary>
    /// <param name="context">Contexte HTTP</param>
    /// <param name="clientIp">Adresse IP du client</param>
    /// <returns>Tâche asynchrone</returns>
    private async Task PerformSecurityChecks(HttpContext context, string clientIp)
    {
        // Vérification de la liste noire d'IP
        if (_options.BlockedIpAddresses.Contains(clientIp))
        {
            throw new SecurityException($"Adresse IP bloquée: {clientIp}");
        }

        // Vérification du rate limiting (simplifié)
        if (await IsRateLimited(clientIp))
        {
            throw new SecurityException($"Limite de taux dépassée pour: {clientIp}");
        }

        // Vérification des headers de sécurité
        ValidateSecurityHeaders(context);

        // Vérification de la taille du payload
        if (context.Request.ContentLength > _options.MaxPayloadSize)
        {
            throw new SecurityException("Payload trop volumineux");
        }
    }

    /// <summary>
    /// Valide les headers de sécurité
    /// </summary>
    /// <param name="context">Contexte HTTP</param>
    private static void ValidateSecurityHeaders(HttpContext context)
    {
        // Vérifier la présence de headers de sécurité requis
        var headers = context.Request.Headers;

        // Exemple: vérifier l'origine pour les webhooks
        if (context.Request.Path.StartsWithSegments("/api/webhooks"))
        {
            var origin = headers["Origin"].ToString();
            var userAgent = headers["User-Agent"].ToString();

            // Validation basique - en production, implémenter des règles plus strictes
            if (string.IsNullOrEmpty(userAgent))
            {
                throw new SecurityException("User-Agent manquant");
            }
        }
    }

    /// <summary>
    /// Vérifie si l'IP est soumise au rate limiting
    /// </summary>
    /// <param name="clientIp">Adresse IP du client</param>
    /// <returns>True si la limite est dépassée</returns>
    private async Task<bool> IsRateLimited(string clientIp)
    {
        // Implémentation simplifiée - en production, utiliser Redis ou un cache distribué
        // pour le rate limiting
        await Task.Delay(1); // Simuler une vérification async
        return false;
    }

    /// <summary>
    /// Obtient l'adresse IP du client
    /// </summary>
    /// <param name="context">Contexte HTTP</param>
    /// <returns>Adresse IP du client</returns>
    private static string GetClientIpAddress(HttpContext context)
    {
        // Vérifier les headers de proxy
        var xForwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(xForwardedFor))
        {
            return xForwardedFor.Split(',')[0].Trim();
        }

        var xRealIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(xRealIp))
        {
            return xRealIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }

    /// <summary>
    /// Enregistre une opération réussie
    /// </summary>
    private async Task LogSuccessfulOperation(
        HttpContext context, 
        IPaymentAuditService auditService, 
        string clientIp, 
        string userAgent, 
        long durationMs)
    {
        try
        {
            var userId = GetUserId(context);
            if (userId.HasValue)
            {
                var auditEntry = new PaymentAuditEntry
                {
                    UserId = userId.Value,
                    Operation = $"{context.Request.Method} {context.Request.Path}",
                    Status = "Success",
                    IpAddress = clientIp,
                    UserAgent = userAgent,
                    DurationMs = durationMs,
                    AdditionalData = new Dictionary<string, object>
                    {
                        ["StatusCode"] = context.Response.StatusCode,
                        ["ContentLength"] = context.Request.ContentLength ?? 0
                    }
                };

                await auditService.LogPaymentAttemptAsync(auditEntry);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'audit de l'opération réussie");
        }
    }

    /// <summary>
    /// Enregistre une violation de sécurité
    /// </summary>
    private async Task LogSecurityViolation(
        HttpContext context, 
        IPaymentAuditService auditService, 
        string clientIp, 
        string userAgent, 
        string errorMessage, 
        long durationMs)
    {
        try
        {
            var userId = GetUserId(context);
            var auditEntry = new PaymentAuditEntry
            {
                UserId = userId ?? Guid.Empty,
                Operation = $"{context.Request.Method} {context.Request.Path}",
                Status = "SecurityViolation",
                IpAddress = clientIp,
                UserAgent = userAgent,
                ErrorMessage = errorMessage,
                DurationMs = durationMs,
                AdditionalData = new Dictionary<string, object>
                {
                    ["ViolationType"] = "Security",
                    ["Path"] = context.Request.Path.ToString()
                }
            };

            await auditService.LogPaymentAttemptAsync(auditEntry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'audit de la violation de sécurité");
        }
    }

    /// <summary>
    /// Enregistre une opération échouée
    /// </summary>
    private async Task LogFailedOperation(
        HttpContext context, 
        IPaymentAuditService auditService, 
        string clientIp, 
        string userAgent, 
        string errorMessage, 
        long durationMs)
    {
        try
        {
            var userId = GetUserId(context);
            if (userId.HasValue)
            {
                var auditEntry = new PaymentAuditEntry
                {
                    UserId = userId.Value,
                    Operation = $"{context.Request.Method} {context.Request.Path}",
                    Status = "Failed",
                    IpAddress = clientIp,
                    UserAgent = userAgent,
                    ErrorMessage = errorMessage,
                    DurationMs = durationMs,
                    AdditionalData = new Dictionary<string, object>
                    {
                        ["ErrorType"] = "Exception"
                    }
                };

                await auditService.LogPaymentAttemptAsync(auditEntry);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'audit de l'opération échouée");
        }
    }

    /// <summary>
    /// Obtient l'ID de l'utilisateur depuis le contexte
    /// </summary>
    /// <param name="context">Contexte HTTP</param>
    /// <returns>ID de l'utilisateur ou null</returns>
    private static Guid? GetUserId(HttpContext context)
    {
        var userIdClaim = context.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        return null;
    }
}

/// <summary>
/// Options de configuration pour le middleware de sécurité des paiements
/// </summary>
public class PaymentSecurityOptions
{
    /// <summary>
    /// Adresses IP bloquées
    /// </summary>
    public HashSet<string> BlockedIpAddresses { get; set; } = new();

    /// <summary>
    /// Taille maximale du payload en octets
    /// </summary>
    public long MaxPayloadSize { get; set; } = 1024 * 1024; // 1 MB

    /// <summary>
    /// Nombre maximum de requêtes par minute par IP
    /// </summary>
    public int MaxRequestsPerMinute { get; set; } = 60;

    /// <summary>
    /// Activer l'audit détaillé
    /// </summary>
    public bool EnableDetailedAudit { get; set; } = true;

    /// <summary>
    /// Headers de sécurité requis
    /// </summary>
    public List<string> RequiredSecurityHeaders { get; set; } = new();
}

/// <summary>
/// Exception de sécurité pour les paiements
/// </summary>
public class SecurityException : Exception
{
    public SecurityException(string message) : base(message) { }
    public SecurityException(string message, Exception innerException) : base(message, innerException) { }
}
