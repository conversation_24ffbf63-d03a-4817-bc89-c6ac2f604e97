import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Star, MapPin, Clock, DollarSign, Calendar, ArrowLeft, Heart, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { BookingWizard } from '@/components/booking/BookingWizard';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useServiceStore, useUIStore } from '@/stores';

export const ServiceDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { selectedService, getServiceById, isLoading } = useServiceStore();
  const { showToast } = useUIStore();
  const [isBookingOpen, setIsBookingOpen] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  useEffect(() => {
    if (id) {
      getServiceById(id);
    }
  }, [id, getServiceById]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!selectedService) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-4">Service non trouvé</h2>
        <Button onClick={() => navigate('/services')}>
          Retour aux services
        </Button>
      </div>
    );
  }

  const formatPrice = (price: number, unit: string) => {
    const formatted = new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(price);
    
    switch (unit) {
      case 'Hourly':
        return `${formatted}/h`;
      case 'Fixed':
        return formatted;
      case 'PerUnit':
        return `${formatted}/unité`;
      default:
        return formatted;
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h${remainingMinutes}min` : `${hours}h`;
  };

  const handleBookingComplete = (bookingData: any) => {
    setIsBookingOpen(false);
    showToast({
      type: 'success',
      title: 'Réservation confirmée !',
      message: 'Votre demande a été envoyée au prestataire.',
    });
    navigate('/bookings');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: selectedService.name,
        text: selectedService.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      showToast({
        type: 'success',
        title: 'Lien copié',
        message: 'Le lien du service a été copié dans le presse-papiers.',
      });
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour
        </Button>
        
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setIsFavorite(!isFavorite)}
          >
            <Heart className={`h-4 w-4 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
          </Button>
          <Button variant="outline" size="icon" onClick={handleShare}>
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Service Images */}
          <div className="aspect-video bg-muted rounded-lg overflow-hidden">
            {selectedService.images && selectedService.images.length > 0 ? (
              <img 
                src={selectedService.images[0]} 
                alt={selectedService.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-muted-foreground">Image du service</span>
              </div>
            )}
          </div>

          {/* Service Info */}
          <div>
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold mb-2">{selectedService.name}</h1>
                {selectedService.category && (
                  <Badge variant="secondary" className="mb-2">
                    {selectedService.category.name}
                  </Badge>
                )}
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-primary">
                  {formatPrice(selectedService.basePrice, selectedService.pricingUnit)}
                </div>
                <div className="text-sm text-muted-foreground">
                  À partir de
                </div>
              </div>
            </div>

            <p className="text-muted-foreground text-lg leading-relaxed">
              {selectedService.description}
            </p>
          </div>

          {/* Service Details */}
          <Card>
            <CardHeader>
              <CardTitle>Détails du service</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Durée</div>
                    <div className="text-sm text-muted-foreground">
                      {formatDuration(selectedService.minDurationMinutes)}
                      {selectedService.maxDurationMinutes !== selectedService.minDurationMinutes && 
                        ` - ${formatDuration(selectedService.maxDurationMinutes)}`
                      }
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Zone d'intervention</div>
                    <div className="text-sm text-muted-foreground">
                      Rayon de {selectedService.serviceRadiusKm} km
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Délai de réservation</div>
                    <div className="text-sm text-muted-foreground">
                      Minimum {selectedService.minAdvanceHours}h à l'avance
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Star className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Note moyenne</div>
                    <div className="text-sm text-muted-foreground">
                      {selectedService.averageRating.toFixed(1)}/5 ({selectedService.totalBookings} avis)
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Provider Info */}
          {selectedService.provider && (
            <Card>
              <CardHeader>
                <CardTitle>À propos du prestataire</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={selectedService.provider.avatar} alt={selectedService.provider.firstName} />
                    <AvatarFallback className="text-lg">
                      {selectedService.provider.firstName.charAt(0)}{selectedService.provider.lastName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold mb-2">
                      {selectedService.provider.firstName} {selectedService.provider.lastName}
                    </h3>
                    
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                        <span className="font-medium">{selectedService.provider.rating.toFixed(1)}</span>
                        <span className="text-muted-foreground ml-1">
                          ({selectedService.provider.reviewCount} avis)
                        </span>
                      </div>
                      
                      <Badge variant="outline">
                        Prestataire vérifié
                      </Badge>
                    </div>
                    
                    <p className="text-muted-foreground">
                      Prestataire expérimenté avec de nombreuses réalisations réussies. 
                      Professionnel et ponctuel, je m'engage à vous fournir un service de qualité.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Booking Card */}
          <Card className="sticky top-4">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Réserver ce service</span>
                <div className="text-right">
                  <div className="text-2xl font-bold text-primary">
                    {formatPrice(selectedService.basePrice, selectedService.pricingUnit)}
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                className="w-full" 
                size="lg"
                onClick={() => setIsBookingOpen(true)}
                disabled={!selectedService.isActive}
              >
                {selectedService.isActive ? 'Réserver maintenant' : 'Service indisponible'}
              </Button>
              
              <div className="text-center text-sm text-muted-foreground">
                Réservation gratuite • Annulation facile
              </div>
              
              {/* Quick Stats */}
              <div className="pt-4 border-t space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Réponse moyenne</span>
                  <span className="font-medium">&lt; 2h</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Taux d'acceptation</span>
                  <span className="font-medium">95%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Dernière connexion</span>
                  <span className="font-medium">Il y a 2h</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Similar Services */}
          <Card>
            <CardHeader>
              <CardTitle>Services similaires</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex space-x-3 p-3 rounded-lg hover:bg-muted cursor-pointer">
                    <div className="w-16 h-16 bg-muted rounded-lg"></div>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">Service similaire {i}</h4>
                      <p className="text-xs text-muted-foreground">À partir de 25€/h</p>
                      <div className="flex items-center mt-1">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs ml-1">4.8</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Booking Dialog */}
      <Dialog open={isBookingOpen} onOpenChange={setIsBookingOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto p-0">
          <BookingWizard
            service={selectedService}
            onComplete={handleBookingComplete}
            onCancel={() => setIsBookingOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};
