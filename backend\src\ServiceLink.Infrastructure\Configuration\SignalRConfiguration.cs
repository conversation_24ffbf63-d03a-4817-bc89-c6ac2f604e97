using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ServiceLink.Application.Interfaces;
using ServiceLink.Infrastructure.Hubs;
using ServiceLink.Infrastructure.Services;

namespace ServiceLink.Infrastructure.Configuration;

/// <summary>
/// Configuration pour SignalR et les services temps réel
/// </summary>
public static class SignalRConfiguration
{
    /// <summary>
    /// Ajoute les services SignalR à la collection de services
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration de l'application</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddSignalRServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configuration SignalR
        var signalRBuilder = services.AddSignalR(options =>
        {
            // Configuration générale
            options.EnableDetailedErrors = configuration.GetValue<bool>("SignalR:EnableDetailedErrors", false);
            options.KeepAliveInterval = TimeSpan.FromSeconds(configuration.GetValue<int>("SignalR:KeepAliveIntervalSeconds", 15));
            options.ClientTimeoutInterval = TimeSpan.FromSeconds(configuration.GetValue<int>("SignalR:ClientTimeoutIntervalSeconds", 30));
            options.HandshakeTimeout = TimeSpan.FromSeconds(configuration.GetValue<int>("SignalR:HandshakeTimeoutSeconds", 15));
            options.MaximumReceiveMessageSize = configuration.GetValue<long>("SignalR:MaximumReceiveMessageSize", 32 * 1024); // 32KB
            options.StreamBufferCapacity = configuration.GetValue<int>("SignalR:StreamBufferCapacity", 10);
            options.MaximumParallelInvocationsPerClient = configuration.GetValue<int>("SignalR:MaximumParallelInvocationsPerClient", 1);
        });

        // Configuration du backplane Redis si activé
        var useRedisBackplane = configuration.GetValue<bool>("SignalR:UseRedisBackplane", false);
        if (useRedisBackplane)
        {
            var redisConnectionString = configuration.GetConnectionString("Redis")
                ?? configuration.GetValue<string>("Redis:ConnectionString");

            if (!string.IsNullOrEmpty(redisConnectionString))
            {
                // Fix: Use the correct extension method for Redis backplane
                signalRBuilder.Services.AddSignalR().AddStackExchangeRedis(redisConnectionString, options =>
                {
                    options.Configuration.ChannelPrefix = "ServiceLink.SignalR";
                });
            }
        }

        // Configuration de l'authentification JWT pour SignalR
        services.Configure<AuthorizationOptions>(options =>
        {
            options.AddPolicy("SignalRPolicy", policy =>
            {
                policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                policy.RequireAuthenticatedUser();
            });
        });

        // Enregistrement des services SignalR personnalisés
        services.AddScoped<INotificationService, SignalRNotificationService>();
        services.AddScoped<IChatService, SignalRChatService>();
        services.AddScoped<IBookingRealtimeService, SignalRBookingRealtimeService>();

        return services;
    }

    /// <summary>
    /// Ajoute les health checks pour SignalR
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddSignalRHealthChecks(this IServiceCollection services)
    {
        services.AddHealthChecks()
            .AddCheck<SignalRHealthCheck>("signalr", tags: new[] { "signalr", "realtime" });

        return services;
    }
}

/// <summary>
/// Paramètres de configuration pour SignalR
/// </summary>
public class SignalRSettings
{
    /// <summary>
    /// Activer les erreurs détaillées (développement uniquement)
    /// </summary>
    public bool EnableDetailedErrors { get; set; } = false;

    /// <summary>
    /// Intervalle de keep-alive en secondes
    /// </summary>
    public int KeepAliveIntervalSeconds { get; set; } = 15;

    /// <summary>
    /// Timeout client en secondes
    /// </summary>
    public int ClientTimeoutIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// Timeout handshake en secondes
    /// </summary>
    public int HandshakeTimeoutSeconds { get; set; } = 15;

    /// <summary>
    /// Taille maximale des messages reçus
    /// </summary>
    public long MaximumReceiveMessageSize { get; set; } = 32 * 1024; // 32KB

    /// <summary>
    /// Capacité du buffer de stream
    /// </summary>
    public int StreamBufferCapacity { get; set; } = 10;

    /// <summary>
    /// Nombre maximum d'invocations parallèles par client
    /// </summary>
    public int MaximumParallelInvocationsPerClient { get; set; } = 1;

    /// <summary>
    /// Utiliser Redis comme backplane
    /// </summary>
    public bool UseRedisBackplane { get; set; } = false;

    /// <summary>
    /// Chaîne de connexion Redis pour le backplane
    /// </summary>
    public string? RedisConnectionString { get; set; }

    /// <summary>
    /// Préfixe des canaux Redis
    /// </summary>
    public string RedisChannelPrefix { get; set; } = "ServiceLink.SignalR";

    /// <summary>
    /// Activer la compression des messages
    /// </summary>
    public bool EnableMessageCompression { get; set; } = true;

    /// <summary>
    /// Activer les logs détaillés
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// Valide la configuration SignalR
    /// </summary>
    /// <returns>True si la configuration est valide</returns>
    public bool IsValid()
    {
        return KeepAliveIntervalSeconds > 0 &&
               ClientTimeoutIntervalSeconds > KeepAliveIntervalSeconds &&
               HandshakeTimeoutSeconds > 0 &&
               MaximumReceiveMessageSize > 0 &&
               StreamBufferCapacity > 0 &&
               MaximumParallelInvocationsPerClient > 0;
    }
}

/// <summary>
/// Health check pour SignalR
/// </summary>
public class SignalRHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IServiceProvider _serviceProvider;

    public SignalRHealthCheck(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Vérifier que les services SignalR sont enregistrés
            var notificationService = _serviceProvider.GetService<INotificationService>();
            var chatService = _serviceProvider.GetService<IChatService>();
            var bookingService = _serviceProvider.GetService<IBookingRealtimeService>();

            if (notificationService == null || chatService == null || bookingService == null)
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                    "Un ou plusieurs services SignalR ne sont pas enregistrés");
            }

            // Test basique de connectivité
            var healthData = new Dictionary<string, object>
            {
                ["notification_service"] = "registered",
                ["chat_service"] = "registered",
                ["booking_service"] = "registered",
                ["timestamp"] = DateTime.UtcNow
            };

            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                "Tous les services SignalR sont opérationnels", healthData);
        }
        catch (Exception ex)
        {
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                "Erreur lors de la vérification des services SignalR", ex);
        }
    }
}




