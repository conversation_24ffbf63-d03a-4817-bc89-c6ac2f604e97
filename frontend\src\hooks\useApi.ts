import { useState, useEffect, useCallback } from 'react'
import type { ApiError } from '../types/api'

// État générique pour les appels API
export interface ApiState<T> {
  data: T | null
  loading: boolean
  error: ApiError | null
  success: boolean
}

// Hook générique pour les appels API
export function useApi<T>(
  apiCall: () => Promise<T>,
  dependencies: any[] = [],
  immediate: boolean = true
) {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: immediate,
    error: null,
    success: false,
  })

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null, success: false }))
    
    try {
      const data = await apiCall()
      setState({
        data,
        loading: false,
        error: null,
        success: true,
      })
      return data
    } catch (error) {
      const apiError = error as ApiError
      setState({
        data: null,
        loading: false,
        error: apiError,
        success: false,
      })
      throw error
    }
  }, dependencies)

  useEffect(() => {
    if (immediate) {
      execute()
    }
  }, [execute, immediate])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    })
  }, [])

  return {
    ...state,
    execute,
    reset,
    refetch: execute,
  }
}

// Hook pour les mutations (POST, PUT, DELETE)
export function useMutation<T, P = any>(
  mutationFn: (params: P) => Promise<T>
) {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  })

  const mutate = useCallback(async (params: P) => {
    setState(prev => ({ ...prev, loading: true, error: null, success: false }))
    
    try {
      const data = await mutationFn(params)
      setState({
        data,
        loading: false,
        error: null,
        success: true,
      })
      return data
    } catch (error) {
      const apiError = error as ApiError
      setState({
        data: null,
        loading: false,
        error: apiError,
        success: false,
      })
      throw error
    }
  }, [mutationFn])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    })
  }, [])

  return {
    ...state,
    mutate,
    reset,
  }
}

// Hook pour la pagination
export interface PaginationState {
  pageNumber: number
  pageSize: number
  totalCount: number
  totalPages: number
  hasPreviousPage: boolean
  hasNextPage: boolean
}

export function usePagination(initialPageSize: number = 20) {
  const [pagination, setPagination] = useState<PaginationState>({
    pageNumber: 1,
    pageSize: initialPageSize,
    totalCount: 0,
    totalPages: 0,
    hasPreviousPage: false,
    hasNextPage: false,
  })

  const setPage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, pageNumber: page }))
  }, [])

  const setPageSize = useCallback((size: number) => {
    setPagination(prev => ({ ...prev, pageSize: size, pageNumber: 1 }))
  }, [])

  const nextPage = useCallback(() => {
    setPagination(prev => 
      prev.hasNextPage 
        ? { ...prev, pageNumber: prev.pageNumber + 1 }
        : prev
    )
  }, [])

  const previousPage = useCallback(() => {
    setPagination(prev => 
      prev.hasPreviousPage 
        ? { ...prev, pageNumber: prev.pageNumber - 1 }
        : prev
    )
  }, [])

  const updatePagination = useCallback((newPagination: Partial<PaginationState>) => {
    setPagination(prev => ({ ...prev, ...newPagination }))
  }, [])

  return {
    pagination,
    setPage,
    setPageSize,
    nextPage,
    previousPage,
    updatePagination,
  }
}

// Hook pour la recherche avec debounce
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Hook pour la recherche avec API
export function useSearch<T>(
  searchFn: (query: string) => Promise<T>,
  debounceDelay: number = 300
) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<ApiError | null>(null)

  const debouncedQuery = useDebounce(query, debounceDelay)

  useEffect(() => {
    if (!debouncedQuery.trim()) {
      setResults(null)
      setError(null)
      return
    }

    const search = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const data = await searchFn(debouncedQuery)
        setResults(data)
      } catch (err) {
        setError(err as ApiError)
        setResults(null)
      } finally {
        setLoading(false)
      }
    }

    search()
  }, [debouncedQuery, searchFn])

  const clearSearch = useCallback(() => {
    setQuery('')
    setResults(null)
    setError(null)
  }, [])

  return {
    query,
    setQuery,
    results,
    loading,
    error,
    clearSearch,
  }
}

// Hook pour le cache local
export function useLocalCache<T>(key: string, ttl: number = 5 * 60 * 1000) {
  const getCachedData = useCallback((): T | null => {
    try {
      const cached = localStorage.getItem(key)
      if (!cached) return null

      const { data, timestamp } = JSON.parse(cached)
      const now = Date.now()
      
      if (now - timestamp > ttl) {
        localStorage.removeItem(key)
        return null
      }
      
      return data
    } catch {
      return null
    }
  }, [key, ttl])

  const setCachedData = useCallback((data: T) => {
    try {
      const cacheItem = {
        data,
        timestamp: Date.now(),
      }
      localStorage.setItem(key, JSON.stringify(cacheItem))
    } catch {
      // Ignore cache errors
    }
  }, [key])

  const clearCache = useCallback(() => {
    localStorage.removeItem(key)
  }, [key])

  return {
    getCachedData,
    setCachedData,
    clearCache,
  }
}
