namespace ServiceLink.API.Configuration;

/// <summary>
/// Configuration pour JWT
/// </summary>
public class JwtSettings
{
    /// <summary>
    /// Clé secrète pour signer les tokens
    /// </summary>
    public string SecretKey { get; set; } = string.Empty;

    /// <summary>
    /// Émetteur du token
    /// </summary>
    public string Issuer { get; set; } = string.Empty;

    /// <summary>
    /// Audience du token
    /// </summary>
    public string Audience { get; set; } = string.Empty;

    /// <summary>
    /// Durée d'expiration en minutes
    /// </summary>
    public int ExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// Durée d'expiration du refresh token en jours
    /// </summary>
    public int RefreshTokenExpirationDays { get; set; } = 7;

    /// <summary>
    /// Tolérance d'horloge en minutes
    /// </summary>
    public int ClockSkewMinutes { get; set; } = 5;

    /// <summary>
    /// Valide la configuration JWT
    /// </summary>
    /// <returns>True si la configuration est valide</returns>
    public bool IsValid()
    {
        return !string.IsNullOrEmpty(SecretKey) &&
               SecretKey.Length >= 32 &&
               !string.IsNullOrEmpty(Issuer) &&
               !string.IsNullOrEmpty(Audience) &&
               ExpirationMinutes > 0 &&
               RefreshTokenExpirationDays > 0;
    }
}

/// <summary>
/// Configuration pour CORS
/// </summary>
public class CorsSettings
{
    /// <summary>
    /// Origines autorisées
    /// </summary>
    public string[] AllowedOrigins { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Méthodes HTTP autorisées
    /// </summary>
    public string[] AllowedMethods { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Headers autorisés
    /// </summary>
    public string[] AllowedHeaders { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Autoriser les credentials
    /// </summary>
    public bool AllowCredentials { get; set; } = true;

    /// <summary>
    /// Durée de cache preflight en secondes
    /// </summary>
    public int MaxAge { get; set; } = 86400;
}

/// <summary>
/// Configuration pour la sécurité
/// </summary>
public class SecuritySettings
{
    /// <summary>
    /// Exiger HTTPS
    /// </summary>
    public bool RequireHttps { get; set; } = true;

    /// <summary>
    /// Exiger la confirmation d'email
    /// </summary>
    public bool RequireEmailConfirmation { get; set; } = true;

    /// <summary>
    /// Exiger la confirmation de téléphone
    /// </summary>
    public bool RequirePhoneConfirmation { get; set; } = false;

    /// <summary>
    /// Expiration du token de reset password en heures
    /// </summary>
    public int PasswordResetTokenExpirationHours { get; set; } = 1;

    /// <summary>
    /// Expiration du token de confirmation email en heures
    /// </summary>
    public int EmailConfirmationTokenExpirationHours { get; set; } = 24;

    /// <summary>
    /// Nombre maximum de tentatives de connexion échouées
    /// </summary>
    public int MaxFailedLoginAttempts { get; set; } = 5;

    /// <summary>
    /// Durée de verrouillage en minutes
    /// </summary>
    public int LockoutDurationMinutes { get; set; } = 30;

    /// <summary>
    /// 2FA obligatoire
    /// </summary>
    public bool TwoFactorRequired { get; set; } = false;
}

/// <summary>
/// Configuration pour Swagger
/// </summary>
public class SwaggerSettings
{
    /// <summary>
    /// Titre de l'API
    /// </summary>
    public string Title { get; set; } = "ServiceLink API";

    /// <summary>
    /// Description de l'API
    /// </summary>
    public string Description { get; set; } = "API pour ServiceLink";

    /// <summary>
    /// Version de l'API
    /// </summary>
    public string Version { get; set; } = "v1";

    /// <summary>
    /// Nom du contact
    /// </summary>
    public string ContactName { get; set; } = string.Empty;

    /// <summary>
    /// Email du contact
    /// </summary>
    public string ContactEmail { get; set; } = string.Empty;

    /// <summary>
    /// Nom de la licence
    /// </summary>
    public string LicenseName { get; set; } = string.Empty;

    /// <summary>
    /// Activer les commentaires XML
    /// </summary>
    public bool EnableXmlComments { get; set; } = true;

    /// <summary>
    /// Activer l'authentification JWT dans Swagger
    /// </summary>
    public bool EnableJwtAuthentication { get; set; } = true;
}

/// <summary>
/// Configuration pour la base de données
/// </summary>
public class DatabaseSettings
{
    /// <summary>
    /// Taille du pool de connexions
    /// </summary>
    public int PoolSize { get; set; } = 128;

    /// <summary>
    /// Timeout des commandes en secondes
    /// </summary>
    public int CommandTimeout { get; set; } = 30;

    /// <summary>
    /// Activer le retry automatique
    /// </summary>
    public bool EnableRetryOnFailure { get; set; } = true;

    /// <summary>
    /// Nombre maximum de tentatives
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// Délai maximum entre les tentatives
    /// </summary>
    public TimeSpan MaxRetryDelay { get; set; } = TimeSpan.FromSeconds(30);
}

/// <summary>
/// Configuration pour le développement
/// </summary>
public class DevelopmentSettings
{
    /// <summary>
    /// Activer le logging des données sensibles
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;

    /// <summary>
    /// Activer les erreurs détaillées
    /// </summary>
    public bool EnableDetailedErrors { get; set; } = true;

    /// <summary>
    /// Initialiser les données de test
    /// </summary>
    public bool SeedData { get; set; } = false;
}
