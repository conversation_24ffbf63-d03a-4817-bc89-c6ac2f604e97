using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Queries;
using System.Security.Claims;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour l'authentification
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AuthController> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="mediator">Médiateur MediatR</param>
    /// <param name="logger">Logger</param>
    public AuthController(IMediator mediator, ILogger<AuthController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Connexion utilisateur
    /// </summary>
    /// <param name="request">Données de connexion</param>
    /// <returns>Token JWT et informations utilisateur</returns>
    [HttpPost("login")]
    [AllowAnonymous]
    public async Task<ActionResult<LoginResponse>> Login([FromBody] LoginRequest request)
    {
        var command = new LoginCommand
        {
            Email = request.Email,
            Password = request.Password,
            RememberMe = request.RememberMe
        };

        var result = await _mediator.Send(command);
        
        if (result == null)
        {
            return Unauthorized(new { message = "Email ou mot de passe incorrect." });
        }

        return Ok(result);
    }

    /// <summary>
    /// Inscription utilisateur
    /// </summary>
    /// <param name="request">Données d'inscription</param>
    /// <returns>Utilisateur créé</returns>
    [HttpPost("register")]
    [AllowAnonymous]
    public async Task<ActionResult<UserResponse>> Register([FromBody] RegisterRequest request)
    {
        var command = new RegisterCommand
        {
            Email = request.Email,
            Password = request.Password,
            ConfirmPassword = request.ConfirmPassword,
            FirstName = request.FirstName,
            LastName = request.LastName,
            Role = request.Role,
            CompanyName = request.CompanyName,
            Siret = request.Siret,
            Description = request.Description,
            PhoneNumber = request.PhoneNumber,
            Address = request.Address,
            City = request.City,
            PostalCode = request.PostalCode,
            Country = request.Country,
            AcceptTerms = request.AcceptTerms
        };

        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetProfile), result);
    }

    /// <summary>
    /// Rafraîchissement du token
    /// </summary>
    /// <param name="request">Token de rafraîchissement</param>
    /// <returns>Nouveaux tokens</returns>
    [HttpPost("refresh")]
    [AllowAnonymous]
    public async Task<ActionResult<RefreshTokenResponse>> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        var command = new RefreshTokenCommand
        {
            RefreshToken = request.RefreshToken
        };

        var result = await _mediator.Send(command);
        
        if (result == null)
        {
            return Unauthorized(new { message = "Token de rafraîchissement invalide." });
        }

        return Ok(result);
    }

    /// <summary>
    /// Déconnexion utilisateur
    /// </summary>
    /// <returns>Résultat de la déconnexion</returns>
    [HttpPost("logout")]
    [Authorize]
    public async Task<ActionResult> Logout()
    {
        var currentUserId = GetCurrentUserId();
        
        var command = new LogoutCommand
        {
            UserId = currentUserId
        };

        await _mediator.Send(command);
        return Ok(new { message = "Déconnexion réussie." });
    }

    /// <summary>
    /// Demande de réinitialisation de mot de passe
    /// </summary>
    /// <param name="request">Email pour la réinitialisation</param>
    /// <returns>Résultat de la demande</returns>
    [HttpPost("forgot-password")]
    [AllowAnonymous]
    public async Task<ActionResult> ForgotPassword([FromBody] ForgotPasswordRequest request)
    {
        var command = new ForgotPasswordCommand
        {
            Email = request.Email
        };

        await _mediator.Send(command);
        return Ok(new { message = "Si un compte existe avec cette adresse email, un lien de réinitialisation a été envoyé." });
    }

    /// <summary>
    /// Réinitialisation de mot de passe
    /// </summary>
    /// <param name="request">Données de réinitialisation</param>
    /// <returns>Résultat de la réinitialisation</returns>
    [HttpPost("reset-password")]
    [AllowAnonymous]
    public async Task<ActionResult> ResetPassword([FromBody] ResetPasswordRequest request)
    {
        var command = new ResetPasswordCommand
        {
            Token = request.Token,
            Email = request.Email,
            NewPassword = request.NewPassword,
            ConfirmNewPassword = request.ConfirmNewPassword
        };

        var result = await _mediator.Send(command);
        
        if (result)
        {
            return Ok(new { message = "Mot de passe réinitialisé avec succès." });
        }

        return BadRequest(new { message = "Le lien de réinitialisation est invalide ou expiré." });
    }

    /// <summary>
    /// Confirmation d'email
    /// </summary>
    /// <param name="request">Token de confirmation</param>
    /// <returns>Résultat de la confirmation</returns>
    [HttpPost("confirm-email")]
    [AllowAnonymous]
    public async Task<ActionResult> ConfirmEmail([FromBody] ConfirmEmailRequest request)
    {
        var command = new ConfirmEmailCommand
        {
            Token = request.Token
        };

        var result = await _mediator.Send(command);
        
        if (result)
        {
            return Ok(new { message = "Email confirmé avec succès." });
        }

        return BadRequest(new { message = "Le lien de confirmation est invalide ou expiré." });
    }

    /// <summary>
    /// Renvoi de l'email de confirmation
    /// </summary>
    /// <returns>Résultat du renvoi</returns>
    [HttpPost("resend-confirmation")]
    [Authorize]
    public async Task<ActionResult> ResendConfirmationEmail()
    {
        var currentUserId = GetCurrentUserId();
        
        var command = new ResendConfirmationEmailCommand
        {
            UserId = currentUserId
        };

        await _mediator.Send(command);
        return Ok(new { message = "Email de confirmation renvoyé." });
    }

    /// <summary>
    /// Obtient le profil de l'utilisateur connecté
    /// </summary>
    /// <returns>Profil utilisateur</returns>
    [HttpGet("profile")]
    [Authorize]
    public async Task<ActionResult<UserResponse>> GetProfile()
    {
        var currentUserId = GetCurrentUserId();
        var query = new GetUserByIdQuery(currentUserId);
        var result = await _mediator.Send(query);

        if (result == null)
        {
            return NotFound("Profil utilisateur non trouvé.");
        }

        return Ok(result);
    }

    /// <summary>
    /// Change le mot de passe de l'utilisateur connecté
    /// </summary>
    /// <param name="request">Données de changement de mot de passe</param>
    /// <returns>Résultat de l'opération</returns>
    [HttpPost("change-password")]
    [Authorize]
    public async Task<ActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        var currentUserId = GetCurrentUserId();

        var command = new ChangePasswordCommand
        {
            UserId = currentUserId,
            CurrentPassword = request.CurrentPassword,
            NewPassword = request.NewPassword,
            UpdatedBy = currentUserId
        };

        var result = await _mediator.Send(command);
        
        if (result)
        {
            return Ok(new { message = "Mot de passe modifié avec succès." });
        }

        return BadRequest("Mot de passe actuel incorrect.");
    }

    /// <summary>
    /// Obtient l'ID de l'utilisateur connecté
    /// </summary>
    /// <returns>ID de l'utilisateur</returns>
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}
