import React from 'react'
import { User, Lock, ArrowLeft } from 'lucide-react'
import { ProfileForm } from '../components/profile/ProfileForm'
import { ChangePasswordForm } from '../components/profile/ChangePasswordForm'
import { But<PERSON> } from '../components/ui/Button'

type TabType = 'profile' | 'password'

interface ProfilePageProps {
  onBack?: () => void
}

export const ProfilePage: React.FC<ProfilePageProps> = ({ onBack }) => {
  const [activeTab, setActiveTab] = React.useState<TabType>('profile')

  const tabs = [
    {
      id: 'profile' as TabType,
      label: 'Informations personnelles',
      icon: User,
    },
    {
      id: 'password' as TabType,
      label: 'Mot de passe',
      icon: Lock,
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Mon Profil</h1>
              <p className="mt-2 text-gray-600">
                Gérez vos informations personnelles et vos paramètres de sécurité
              </p>
            </div>
            {onBack && (
              <Button
                variant="outline"
                onClick={onBack}
                leftIcon={<ArrowLeft className="h-4 w-4" />}
              >
                Retour
              </Button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon
                const isActive = activeTab === tab.id
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                      ${isActive
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon
                      className={`
                        -ml-0.5 mr-2 h-5 w-5
                        ${isActive
                          ? 'text-blue-500'
                          : 'text-gray-400 group-hover:text-gray-500'
                        }
                      `}
                    />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mb-8">
          {activeTab === 'profile' && (
            <ProfileForm
              onSuccess={() => {
                // Optionnel : actions après mise à jour du profil
              }}
            />
          )}
          
          {activeTab === 'password' && (
            <ChangePasswordForm
              onSuccess={() => {
                // Optionnel : actions après changement de mot de passe
              }}
            />
          )}
        </div>
      </div>
    </div>
  )
}
