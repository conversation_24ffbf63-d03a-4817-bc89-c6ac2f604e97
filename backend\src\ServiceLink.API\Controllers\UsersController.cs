using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Queries;
using ServiceLink.Domain.Enums;
using System.Security.Claims;

namespace ServiceLink.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des utilisateurs
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UsersController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<UsersController> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="mediator">Médiateur MediatR</param>
    /// <param name="logger">Logger</param>
    public UsersController(IMediator mediator, ILogger<UsersController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Obtient la liste des utilisateurs avec pagination et filtres
    /// </summary>
    /// <param name="pageNumber">Numéro de page (défaut: 1)</param>
    /// <param name="pageSize">Taille de page (défaut: 20, max: 100)</param>
    /// <param name="role">Filtre par rôle</param>
    /// <param name="isActive">Filtre par statut actif</param>
    /// <param name="isEmailConfirmed">Filtre par email confirmé</param>
    /// <param name="searchTerm">Terme de recherche</param>
    /// <param name="sortBy">Champ de tri</param>
    /// <param name="sortOrder">Ordre de tri (asc/desc)</param>
    /// <returns>Liste paginée des utilisateurs</returns>
    [HttpGet]
    [Authorize(Roles = "Admin,Support,Manager,Supervisor")]
    public async Task<ActionResult<UserListResponse>> GetUsers(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] UserRole? role = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool? isEmailConfirmed = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? sortBy = null,
        [FromQuery] string? sortOrder = "asc")
    {
        if (pageSize > 100) pageSize = 100;
        if (pageNumber < 1) pageNumber = 1;

        var query = new GetUsersQuery
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            Role = role,
            IsActive = isActive,
            IsEmailConfirmed = isEmailConfirmed,
            SearchTerm = searchTerm,
            SortBy = sortBy,
            SortOrder = sortOrder
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Obtient un utilisateur par son ID
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur</param>
    /// <returns>Informations de l'utilisateur</returns>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<UserResponse>> GetUser(Guid id)
    {
        var currentUserId = GetCurrentUserId();
        var currentUserRole = GetCurrentUserRole();

        // Vérifier les permissions
        if (currentUserId != id && !IsAdministrativeRole(currentUserRole))
        {
            return Forbid("Vous ne pouvez consulter que votre propre profil.");
        }

        var query = new GetUserByIdQuery(id);
        var result = await _mediator.Send(query);

        if (result == null)
        {
            return NotFound("Utilisateur non trouvé.");
        }

        return Ok(result);
    }

    /// <summary>
    /// Obtient le profil de l'utilisateur connecté
    /// </summary>
    /// <returns>Profil de l'utilisateur connecté</returns>
    [HttpGet("me")]
    public async Task<ActionResult<UserResponse>> GetCurrentUser()
    {
        var currentUserId = GetCurrentUserId();
        var query = new GetUserByIdQuery(currentUserId);
        var result = await _mediator.Send(query);

        if (result == null)
        {
            return NotFound("Profil utilisateur non trouvé.");
        }

        return Ok(result);
    }

    /// <summary>
    /// Crée un nouvel utilisateur
    /// </summary>
    /// <param name="request">Données de création</param>
    /// <returns>Utilisateur créé</returns>
    [HttpPost("register")]
    [Authorize(Roles = "Admin,Manager,Supervisor")]
    public async Task<ActionResult<UserResponse>> CreateUser([FromBody] RegisterRequest request)
    {
        var currentUserId = GetCurrentUserId();

        var command = new RegisterCommand
        {
            Email = request.Email,
            FirstName = request.FirstName,
            LastName = request.LastName,
            Password = request.Password,
            PhoneNumber = request.PhoneNumber,
            Role = request.Role,
            Language = request.Language,
            TimeZone = request.TimeZone,
            CreatedBy = currentUserId
        };

        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetUser), new { id = result.Id }, result);
    }

    /// <summary>
    /// Met à jour un utilisateur || Met à jour le profil de l'utilisateur connecté
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur</param>
    /// <param name="request">Données de mise à jour</param>
    /// <returns>Utilisateur mis à jour</returns>
    [HttpPut("{id:guid}")]
    [Authorize]
    public async Task<ActionResult<UserResponse>> UpdateUser(Guid id, [FromBody] UpdateUserRequest request)
    {
        var currentUserId = GetCurrentUserId();
        var currentUserRole = GetCurrentUserRole();

        // Vérifier les permissions
        if (currentUserId != id && !IsAdministrativeRole(currentUserRole))
        {
            return Forbid("Vous ne pouvez modifier que votre propre profil.");
        }

        var command = new UpdateUserCommand
        {
            UserId = id,
            FirstName = request.FirstName,
            LastName = request.LastName,
            PhoneNumber = request.PhoneNumber,
            Language = request.Language,
            TimeZone = request.TimeZone,
            AvatarUrl = request.AvatarUrl,
            UpdatedBy = currentUserId
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Change le mot de passe d'un utilisateur
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur</param>
    /// <param name="request">Données de changement de mot de passe</param>
    /// <returns>Résultat de l'opération</returns>
    [HttpPost("{id:guid}/change-password")]
    public async Task<ActionResult> ChangePassword(Guid id, [FromBody] ChangePasswordRequest request)
    {
        var currentUserId = GetCurrentUserId();

        // Seul l'utilisateur peut changer son propre mot de passe
        if (currentUserId != id)
        {
            return Forbid("Vous ne pouvez changer que votre propre mot de passe.");
        }

        var command = new ChangePasswordCommand
        {
            UserId = id,
            CurrentPassword = request.CurrentPassword,
            NewPassword = request.NewPassword,
            UpdatedBy = currentUserId
        };

        var result = await _mediator.Send(command);
        
        if (result)
        {
            return Ok(new { message = "Mot de passe modifié avec succès." });
        }

        return BadRequest("Erreur lors du changement de mot de passe.");
    }

    /// <summary>
    /// Active l'authentification à deux facteurs
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur</param>
    /// <returns>Codes de récupération</returns>
    [HttpPost("{id:guid}/enable-2fa")]
    public async Task<ActionResult<string[]>> EnableTwoFactor(Guid id)
    {
        var currentUserId = GetCurrentUserId();

        // Seul l'utilisateur peut activer sa propre 2FA
        if (currentUserId != id)
        {
            return Forbid("Vous ne pouvez activer la 2FA que pour votre propre compte.");
        }

        var command = new EnableTwoFactorCommand
        {
            UserId = id,
            EnabledBy = currentUserId
        };

        var recoveryCodes = await _mediator.Send(command);
        return Ok(new { recoveryCodes, message = "Authentification à deux facteurs activée avec succès." });
    }

    /// <summary>
    /// Désactive l'authentification à deux facteurs
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur</param>
    /// <param name="request">Code 2FA pour confirmation</param>
    /// <returns>Résultat de l'opération</returns>
    [HttpPost("{id:guid}/disable-2fa")]
    public async Task<ActionResult> DisableTwoFactor(Guid id, [FromBody] DisableTwoFactorRequest request)
    {
        var currentUserId = GetCurrentUserId();

        // Seul l'utilisateur peut désactiver sa propre 2FA
        if (currentUserId != id)
        {
            return Forbid("Vous ne pouvez désactiver la 2FA que pour votre propre compte.");
        }

        var command = new DisableTwoFactorCommand
        {
            UserId = id,
            TwoFactorCode = request.TwoFactorCode,
            DisabledBy = currentUserId
        };

        var result = await _mediator.Send(command);
        
        if (result)
        {
            return Ok(new { message = "Authentification à deux facteurs désactivée avec succès." });
        }

        return BadRequest("Code 2FA invalide.");
    }

    /// <summary>
    /// Désactive un utilisateur
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur</param>
    /// <param name="request">Raison de la désactivation</param>
    /// <returns>Résultat de l'opération</returns>
    [HttpPost("{id:guid}/deactivate")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult> DeactivateUser(Guid id, [FromBody] DeactivateUserRequest request)
    {
        var currentUserId = GetCurrentUserId();

        var command = new DeactivateUserCommand
        {
            UserId = id,
            Reason = request.Reason,
            DeactivatedBy = currentUserId
        };

        var result = await _mediator.Send(command);
        
        if (result)
        {
            return Ok(new { message = "Utilisateur désactivé avec succès." });
        }

        return BadRequest("Erreur lors de la désactivation de l'utilisateur.");
    }

    /// <summary>
    /// Réactive un utilisateur
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur</param>
    /// <returns>Résultat de l'opération</returns>
    [HttpPost("{id:guid}/activate")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult> ActivateUser(Guid id)
    {
        var currentUserId = GetCurrentUserId();

        var command = new ActivateUserCommand
        {
            UserId = id,
            ActivatedBy = currentUserId
        };

        var result = await _mediator.Send(command);
        
        if (result)
        {
            return Ok(new { message = "Utilisateur réactivé avec succès." });
        }

        return BadRequest("Erreur lors de la réactivation de l'utilisateur.");
    }

    /// <summary>
    /// Obtient les statistiques des utilisateurs
    /// </summary>
    /// <returns>Statistiques des utilisateurs</returns>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,Manager,Support")]
    public async Task<ActionResult<UserStatisticsResponse>> GetUserStatistics()
    {
        var query = new GetUserStatisticsQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Obtient l'ID de l'utilisateur connecté
    /// </summary>
    /// <returns>ID de l'utilisateur</returns>
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    /// <summary>
    /// Obtient le rôle de l'utilisateur connecté
    /// </summary>
    /// <returns>Rôle de l'utilisateur</returns>
    private string GetCurrentUserRole()
    {
        return User.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty;
    }

    /// <summary>
    /// Vérifie si un rôle est administratif
    /// </summary>
    /// <param name="role">Rôle à vérifier</param>
    /// <returns>True si le rôle est administratif</returns>
    private static bool IsAdministrativeRole(string role)
    {
        var adminRoles = new[] { "Admin", "Manager", "Support", "Supervisor" };
        return adminRoles.Contains(role);
    }
}

/// <summary>
/// DTO pour la désactivation d'un utilisateur
/// </summary>
public class DeactivateUserRequest
{
    /// <summary>
    /// Raison de la désactivation
    /// </summary>
    public string? Reason { get; set; }
}

/// <summary>
/// DTO pour la désactivation de la 2FA
/// </summary>
public class DisableTwoFactorRequest
{
    /// <summary>
    /// Code 2FA pour confirmation
    /// </summary>
    public string TwoFactorCode { get; set; } = string.Empty;
}
