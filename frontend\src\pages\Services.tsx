import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { Filter, Grid, List, MapPin, Star, Clock, Euro, TrendingUp, SlidersHorizontal, Search, X } from 'lucide-react';
import { ServiceList } from '@/components/services/ServiceList';
import { ServiceFilters } from '@/components/services/ServiceFilters';
import { BookingForm } from '@/components/booking/BookingForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useServiceStore, useUIStore } from '@/stores';
import type { Service } from '@/stores/serviceStore';

export const Services: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [isBookingDialogOpen, setIsBookingDialogOpen] = useState(false);
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('relevance');
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const [selectedLocation, setSelectedLocation] = useState(searchParams.get('location') || '');

  const {
    services,
    categories,
    isLoading,
    searchFilters,
    updateSearchFilters,
    searchServices,
    loadCategories
  } = useServiceStore();
  const { showToast } = useUIStore();

  useEffect(() => {
    loadCategories();
    // Load services based on URL params
    const categoryId = searchParams.get('category');
    const searchTerm = searchParams.get('search');
    const location = searchParams.get('location');

    if (categoryId || searchTerm || location) {
      updateSearchFilters({
        categoryId: categoryId || undefined,
        searchTerm: searchTerm || undefined,
        location: location || undefined,
      });
      searchServices();
    }
  }, [searchParams, loadCategories, updateSearchFilters, searchServices]);

  const handleSearch = () => {
    const params = new URLSearchParams();
    if (searchQuery) params.set('search', searchQuery);
    if (selectedLocation) params.set('location', selectedLocation);
    setSearchParams(params);

    updateSearchFilters({
      searchTerm: searchQuery || undefined,
      location: selectedLocation || undefined,
    });
    searchServices();
  };

  const handleBookService = (service: Service) => {
    setSelectedService(service);
    setIsBookingDialogOpen(true);
  };

  const handleBookingSuccess = () => {
    setIsBookingDialogOpen(false);
    setSelectedService(null);
    showToast({
      type: 'success',
      title: 'Réservation créée',
      message: 'Votre demande de réservation a été envoyée au prestataire.',
    });
  };

  const handleBookingCancel = () => {
    setIsBookingDialogOpen(false);
    setSelectedService(null);
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setSelectedLocation('');
    setSearchParams(new URLSearchParams());
    updateSearchFilters({});
    searchServices();
  };

  const activeFiltersCount = Object.values(searchFilters).filter(Boolean).length;

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Search */}
      <div className="bg-gradient-to-r from-primary/5 to-secondary/5 -mx-6 px-6 py-8 rounded-lg">
        <div className="max-w-4xl">
          <h1 className="text-3xl font-bold mb-2">
            Trouvez le service parfait
          </h1>
          <p className="text-muted-foreground mb-6">
            Plus de 12,000 prestataires vérifiés • Commission réduite • Paiement sécurisé
          </p>

          {/* Advanced Search Bar */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Que recherchez-vous ? (ménage, jardinage, bricolage...)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>

            <div className="relative min-w-[200px]">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Où ?"
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="pl-10"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>

            <Button onClick={handleSearch} size="lg" className="px-8">
              Rechercher
            </Button>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar Filters */}
        <div className="lg:w-80 space-y-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold flex items-center">
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  Filtres
                  {activeFiltersCount > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {activeFiltersCount}
                    </Badge>
                  )}
                </h3>
                {activeFiltersCount > 0 && (
                  <Button variant="ghost" size="sm" onClick={handleClearFilters}>
                    <X className="h-4 w-4 mr-1" />
                    Effacer
                  </Button>
                )}
              </div>

              <ServiceFilters />
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-4">Pourquoi ServiceLink ?</h3>
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Euro className="h-4 w-4 text-green-500 mr-2" />
                  <span>Commission 8% seulement</span>
                </div>
                <div className="flex items-center text-sm">
                  <Star className="h-4 w-4 text-yellow-500 mr-2" />
                  <span>Note moyenne 4.9/5</span>
                </div>
                <div className="flex items-center text-sm">
                  <Clock className="h-4 w-4 text-blue-500 mr-2" />
                  <span>Réponse sous 2h</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="flex-1 space-y-4">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-semibold">
                Services disponibles
              </h2>
              <Badge variant="outline">
                {services.length} résultats
              </Badge>
            </div>

            <div className="flex items-center space-x-2">
              {/* Sort Options */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Trier par" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="relevance">Pertinence</SelectItem>
                  <SelectItem value="price_asc">Prix croissant</SelectItem>
                  <SelectItem value="price_desc">Prix décroissant</SelectItem>
                  <SelectItem value="rating">Mieux notés</SelectItem>
                  <SelectItem value="distance">Distance</SelectItem>
                </SelectContent>
              </Select>

              <Separator orientation="vertical" className="h-6" />

              {/* View Mode Toggle */}
              <div className="flex border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'secondary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'secondary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Service List */}
          <ServiceList
            onBookService={handleBookService}
          />
        </div>
      </div>

      {/* Booking Dialog */}
      <Dialog open={isBookingDialogOpen} onOpenChange={setIsBookingDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Réserver ce service</DialogTitle>
          </DialogHeader>
          {selectedService && (
            <BookingForm
              service={selectedService}
              onSuccess={handleBookingSuccess}
              onCancel={handleBookingCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
