import React, { useState } from 'react';
import { ServiceList } from '@/components/services/ServiceList';
import { BookingForm } from '@/components/booking/BookingForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useUIStore } from '@/stores';
import type { Service } from '@/stores/serviceStore';

export const Services: React.FC = () => {
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [isBookingDialogOpen, setIsBookingDialogOpen] = useState(false);
  const { showToast } = useUIStore();

  const handleBookService = (service: Service) => {
    setSelectedService(service);
    setIsBookingDialogOpen(true);
  };

  const handleBookingSuccess = () => {
    setIsBookingDialogOpen(false);
    setSelectedService(null);
    showToast({
      type: 'success',
      title: 'Booking Created',
      message: 'Your booking request has been sent to the service provider.',
    });
  };

  const handleBookingCancel = () => {
    setIsBookingDialogOpen(false);
    setSelectedService(null);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Browse Services
        </h1>
        <p className="text-muted-foreground">
          Discover and book amazing services from trusted providers
        </p>
      </div>

      {/* Service List */}
      <ServiceList onBookService={handleBookService} />

      {/* Booking Dialog */}
      <Dialog open={isBookingDialogOpen} onOpenChange={setIsBookingDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Book Service</DialogTitle>
          </DialogHeader>
          {selectedService && (
            <BookingForm
              service={selectedService}
              onSuccess={handleBookingSuccess}
              onCancel={handleBookingCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
