version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: servicelink-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-servicelink}
      POSTGRES_USER: ${POSTGRES_USER:-servicelink}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-ServiceLink2024!}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - servicelink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-servicelink} -d ${POSTGRES_DB:-servicelink}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Caching and Sessions
  redis:
    image: redis:7-alpine
    container_name: servicelink-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - servicelink-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL Test Database
  postgres-test:
    image: postgres:16-alpine
    container_name: servicelink-postgres-test
    environment:
      POSTGRES_DB: servicelink_test
      POSTGRES_USER: servicelink_test_user
      POSTGRES_PASSWORD: servicelink_test_password
    ports:
      - "5437:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    networks:
      - servicelink-network
    restart: unless-stopped
    profiles:
      - testing

  # ServiceLink API
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: servicelink-api
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Development}
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=${POSTGRES_DB:-servicelink};Username=${POSTGRES_USER:-servicelink};Password=${POSTGRES_PASSWORD:-ServiceLink2024!}
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD:-ServiceLink2024!}
      - JWT__SecretKey=${JWT_SECRET_KEY:-your-super-secret-jwt-key-that-should-be-at-least-32-characters-long}
      - JWT__Issuer=ServiceLink
      - JWT__Audience=ServiceLink
      - JWT__ExpirationInMinutes=60
      - CORS__AllowedOrigins=${CORS_ORIGINS:-http://localhost:3000,http://localhost:5173}
      - Email__SmtpHost=${SMTP_HOST:-smtp.gmail.com}
      - Email__SmtpPort=${SMTP_PORT:-587}
      - Email__SmtpUsername=${SMTP_USERNAME}
      - Email__SmtpPassword=${SMTP_PASSWORD}
      - Email__FromEmail=${FROM_EMAIL:-<EMAIL>}
      - Email__FromName=${FROM_NAME:-ServiceLink}
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - servicelink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs

  # ServiceLink Frontend (Development)
  frontend:
    image: node:20-alpine
    container_name: servicelink-frontend
    working_dir: /app
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8080
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - servicelink-network
    command: sh -c "npm install && npm run dev"
    profiles:
      - frontend

  # Nginx (pour la production)
  nginx:
    image: nginx:alpine
    container_name: servicelink-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - api
    networks:
      - servicelink-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  postgres_test_data:
    driver: local
  redis_data:
    driver: local

networks:
  servicelink-network:
    driver: bridge
    name: servicelink-network
