# Configuration de développement pour ServiceLink Frontend

# API Backend
VITE_API_URL=https://localhost:7276/api
VITE_API_TIMEOUT=10000

# SignalR Hub
VITE_SIGNALR_URL=https://localhost:7276/hubs

# Configuration de l'application
VITE_APP_NAME=ServiceLink
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# URLs publiques
VITE_PUBLIC_URL=http://localhost:5173
VITE_BACKEND_URL=https://localhost:7276

# Configuration des cartes (Google Maps)
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Configuration des paiements (clés publiques uniquement)
VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id_here

# Configuration Firebase (pour les notifications push)
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef123456

# Configuration de debug
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=debug

# Configuration des uploads
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# Configuration de la géolocalisation
VITE_DEFAULT_LATITUDE=48.8566
VITE_DEFAULT_LONGITUDE=2.3522
VITE_DEFAULT_ZOOM=12
VITE_MAX_SEARCH_RADIUS=50

# Configuration des notifications
VITE_ENABLE_PUSH_NOTIFICATIONS=true
VITE_NOTIFICATION_TIMEOUT=5000

# Configuration du cache
VITE_ENABLE_CACHE=true
VITE_CACHE_TTL=300000

# Configuration des analytics (si utilisé)
VITE_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
VITE_ENABLE_ANALYTICS=false
