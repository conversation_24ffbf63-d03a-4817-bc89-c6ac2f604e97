namespace ServiceLink.Application.Interfaces;

/// <summary>
/// Interface unifiée pour tous les services de notification externes
/// </summary>
public interface IExternalNotificationService
{
    /// <summary>
    /// Type de service de notification
    /// </summary>
    ExternalNotificationType ServiceType { get; }

    /// <summary>
    /// Envoie une notification
    /// </summary>
    /// <param name="request">Détails de la notification</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de l'envoi</returns>
    Task<ExternalNotificationResult> SendNotificationAsync(ExternalNotificationRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie une notification en lot
    /// </summary>
    /// <param name="requests">Liste des notifications à envoyer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultats des envois</returns>
    Task<IEnumerable<ExternalNotificationResult>> SendBulkNotificationsAsync(IEnumerable<ExternalNotificationRequest> requests, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie le statut d'une notification
    /// </summary>
    /// <param name="notificationId">ID de la notification</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Statut de la notification</returns>
    Task<ExternalNotificationStatus> GetNotificationStatusAsync(string notificationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Valide une adresse de destination
    /// </summary>
    /// <param name="destination">Adresse à valider</param>
    /// <returns>True si l'adresse est valide</returns>
    bool ValidateDestination(string destination);
}

/// <summary>
/// Factory pour créer des services de notification externes
/// </summary>
public interface IExternalNotificationServiceFactory
{
    /// <summary>
    /// Crée un service de notification pour le type spécifié
    /// </summary>
    /// <param name="type">Type de notification</param>
    /// <returns>Service de notification</returns>
    IExternalNotificationService CreateNotificationService(ExternalNotificationType type);

    /// <summary>
    /// Obtient tous les services de notification disponibles
    /// </summary>
    /// <returns>Liste des services disponibles</returns>
    IEnumerable<IExternalNotificationService> GetAvailableServices();

    /// <summary>
    /// Envoie une notification via le service approprié
    /// </summary>
    /// <param name="request">Demande de notification</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de l'envoi</returns>
    Task<ExternalNotificationResult> SendNotificationAsync(ExternalNotificationRequest request, CancellationToken cancellationToken = default);
}

/// <summary>
/// Service de gestion des templates de notification
/// </summary>
public interface INotificationTemplateService
{
    /// <summary>
    /// Obtient un template par son nom
    /// </summary>
    /// <param name="templateName">Nom du template</param>
    /// <param name="language">Langue du template</param>
    /// <returns>Template de notification</returns>
    Task<NotificationTemplate?> GetTemplateAsync(string templateName, string language = "fr");

    /// <summary>
    /// Rend un template avec les données fournies
    /// </summary>
    /// <param name="template">Template à rendre</param>
    /// <param name="data">Données pour le rendu</param>
    /// <returns>Contenu rendu</returns>
    string RenderTemplate(NotificationTemplate template, Dictionary<string, object> data);

    /// <summary>
    /// Crée ou met à jour un template
    /// </summary>
    /// <param name="template">Template à sauvegarder</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Template sauvegardé</returns>
    Task<NotificationTemplate> SaveTemplateAsync(NotificationTemplate template, CancellationToken cancellationToken = default);
}

/// <summary>
/// Types de notification externes
/// </summary>
public enum ExternalNotificationType
{
    /// <summary>
    /// Email via SendGrid
    /// </summary>
    Email = 1,

    /// <summary>
    /// SMS via Twilio
    /// </summary>
    SMS = 2,

    /// <summary>
    /// Push notification via Firebase
    /// </summary>
    Push = 3
}

/// <summary>
/// Demande de notification externe
/// </summary>
public class ExternalNotificationRequest
{
    /// <summary>
    /// Type de notification
    /// </summary>
    public ExternalNotificationType Type { get; set; }

    /// <summary>
    /// Destinataire(s)
    /// </summary>
    public List<string> Recipients { get; set; } = new();

    /// <summary>
    /// Sujet/Titre de la notification
    /// </summary>
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// Contenu de la notification
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Template à utiliser (optionnel)
    /// </summary>
    public string? TemplateName { get; set; }

    /// <summary>
    /// Données pour le template
    /// </summary>
    public Dictionary<string, object> TemplateData { get; set; } = new();

    /// <summary>
    /// Priorité de la notification
    /// </summary>
    public ExternalNotificationPriority Priority { get; set; } = ExternalNotificationPriority.Normal;

    /// <summary>
    /// Date d'envoi programmée (optionnelle)
    /// </summary>
    public DateTime? ScheduledAt { get; set; }

    /// <summary>
    /// Métadonnées additionnelles
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Langue de la notification
    /// </summary>
    public string Language { get; set; } = "fr";

    /// <summary>
    /// Pièces jointes (pour emails)
    /// </summary>
    public List<NotificationAttachment> Attachments { get; set; } = new();

    /// <summary>
    /// Tags pour catégoriser la notification
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Résultat d'envoi de notification externe
/// </summary>
public class ExternalNotificationResult
{
    /// <summary>
    /// Succès de l'envoi
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// ID de la notification
    /// </summary>
    public string NotificationId { get; set; } = string.Empty;

    /// <summary>
    /// Type de notification
    /// </summary>
    public ExternalNotificationType Type { get; set; }

    /// <summary>
    /// Statut de la notification
    /// </summary>
    public ExternalNotificationStatus Status { get; set; }

    /// <summary>
    /// Message d'erreur si échec
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Code d'erreur si échec
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Date d'envoi
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Destinataires traités avec succès
    /// </summary>
    public List<string> SuccessfulRecipients { get; set; } = new();

    /// <summary>
    /// Destinataires en échec
    /// </summary>
    public List<string> FailedRecipients { get; set; } = new();

    /// <summary>
    /// Coût de l'envoi (si applicable)
    /// </summary>
    public decimal? Cost { get; set; }

    /// <summary>
    /// Données spécifiques au provider
    /// </summary>
    public Dictionary<string, object> ProviderData { get; set; } = new();
}

/// <summary>
/// Statuts de notification externe
/// </summary>
public enum ExternalNotificationStatus
{
    /// <summary>
    /// En attente d'envoi
    /// </summary>
    Pending = 1,

    /// <summary>
    /// En cours d'envoi
    /// </summary>
    Sending = 2,

    /// <summary>
    /// Envoyée avec succès
    /// </summary>
    Sent = 3,

    /// <summary>
    /// Délivrée
    /// </summary>
    Delivered = 4,

    /// <summary>
    /// Lue/Ouverte
    /// </summary>
    Read = 5,

    /// <summary>
    /// Échec d'envoi
    /// </summary>
    Failed = 6,

    /// <summary>
    /// Rejetée
    /// </summary>
    Rejected = 7,

    /// <summary>
    /// Rebond (bounce)
    /// </summary>
    Bounced = 8,

    /// <summary>
    /// Désabonnement
    /// </summary>
    Unsubscribed = 9
}

/// <summary>
/// Priorités de notification externe
/// </summary>
public enum ExternalNotificationPriority
{
    /// <summary>
    /// Priorité basse
    /// </summary>
    Low = 1,

    /// <summary>
    /// Priorité normale
    /// </summary>
    Normal = 2,

    /// <summary>
    /// Priorité haute
    /// </summary>
    High = 3,

    /// <summary>
    /// Priorité critique/urgente
    /// </summary>
    Critical = 4
}

/// <summary>
/// Template de notification
/// </summary>
public class NotificationTemplate
{
    /// <summary>
    /// ID du template
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Nom du template
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Type de notification
    /// </summary>
    public ExternalNotificationType Type { get; set; }

    /// <summary>
    /// Langue du template
    /// </summary>
    public string Language { get; set; } = "fr";

    /// <summary>
    /// Sujet du template
    /// </summary>
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// Contenu du template
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Variables disponibles dans le template
    /// </summary>
    public List<string> Variables { get; set; } = new();

    /// <summary>
    /// Actif ou non
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Date de création
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date de dernière modification
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Version du template
    /// </summary>
    public int Version { get; set; } = 1;
}

/// <summary>
/// Pièce jointe pour notification
/// </summary>
public class NotificationAttachment
{
    /// <summary>
    /// Nom du fichier
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// Type MIME
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// Contenu du fichier en base64
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Taille du fichier en octets
    /// </summary>
    public long Size { get; set; }
}
