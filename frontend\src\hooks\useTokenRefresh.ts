import { useEffect, useRef } from 'react';
import { useAuthStore } from '../stores/authStore';

/**
 * Hook pour gérer le refresh automatique du token JWT
 * Refresh le token 5 minutes avant son expiration
 */
export function useTokenRefresh() {
  const { token, refreshToken, refreshAuthToken, logout } = useAuthStore();
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Nettoyer le timeout précédent
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
      refreshTimeoutRef.current = null;
    }

    // Si pas de token, pas besoin de refresh
    if (!token || !refreshToken) {
      return;
    }

    try {
      // Décoder le token pour obtenir l'expiration
      const tokenPayload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = tokenPayload.exp * 1000; // Convertir en millisecondes
      const currentTime = Date.now();
      
      // Calculer le temps avant expiration
      const timeUntilExpiration = expirationTime - currentTime;
      
      // Si le token expire dans moins de 5 minutes, refresh immédiatement
      if (timeUntilExpiration <= 5 * 60 * 1000) {
        console.log('🔄 Token expire bientôt, refresh immédiat');
        refreshAuthToken().catch((error) => {
          console.error('❌ Erreur refresh token immédiat:', error);
          logout();
        });
        return;
      }

      // Programmer le refresh 5 minutes avant l'expiration
      const refreshTime = timeUntilExpiration - (5 * 60 * 1000);
      
      console.log(`⏰ Refresh programmé dans ${Math.round(refreshTime / 1000 / 60)} minutes`);
      
      refreshTimeoutRef.current = setTimeout(async () => {
        try {
          console.log('🔄 Refresh automatique du token');
          await refreshAuthToken();
        } catch (error) {
          console.error('❌ Erreur refresh automatique:', error);
          logout();
        }
      }, refreshTime);

    } catch (error) {
      console.error('❌ Erreur décodage token:', error);
      logout();
    }

    // Cleanup
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
    };
  }, [token, refreshToken, refreshAuthToken, logout]);

  // Cleanup au démontage
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);
}
