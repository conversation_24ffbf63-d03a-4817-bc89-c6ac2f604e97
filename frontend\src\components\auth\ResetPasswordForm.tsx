import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Lock, Eye, EyeOff, Save } from 'lucide-react'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { authService } from '../../services/authService'
import { toast } from 'sonner'

// Schéma de validation pour la réinitialisation
const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(8, 'Le mot de passe doit contenir au moins 8 caractères')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre'
    ),
  confirmPassword: z
    .string()
    .min(1, 'La confirmation du mot de passe est requise'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword'],
})

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>

interface ResetPasswordFormProps {
  token: string
  email: string
  onSuccess?: () => void
}

export const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({
  token,
  email,
  onSuccess,
}) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const [showPassword, setShowPassword] = React.useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const onSubmit = async (data: ResetPasswordFormData) => {
    try {
      setIsLoading(true)
      await authService.resetPassword({
        token,
        email,
        password: data.password,
        confirmPassword: data.confirmPassword,
      })
      
      toast.success('Mot de passe réinitialisé avec succès!')
      onSuccess?.()
    } catch (error: any) {
      if (error.statusCode === 400) {
        setError('root', {
          message: 'Le lien de réinitialisation est invalide ou expiré',
        })
      } else if (error.errors) {
        Object.entries(error.errors).forEach(([field, messages]) => {
          if (Array.isArray(messages) && messages.length > 0) {
            setError(field as keyof ResetPasswordFormData, {
              message: messages[0],
            })
          }
        })
      } else {
        setError('root', {
          message: error.message || 'Une erreur est survenue',
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Nouveau mot de passe
          </h2>
          <p className="text-gray-600">
            Choisissez un nouveau mot de passe pour votre compte
          </p>
          <p className="text-sm text-gray-500 mt-1">
            Compte : {email}
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <Input
            label="Nouveau mot de passe"
            type={showPassword ? 'text' : 'password'}
            placeholder="Votre nouveau mot de passe"
            leftIcon={<Lock className="h-4 w-4" />}
            rightIcon={
              <div className="cursor-pointer">
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none pointer-events-auto"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
            }
            error={errors.password?.message}
            helperText="Au moins 8 caractères avec une minuscule, une majuscule et un chiffre"
            {...register('password')}
          />

          <Input
            label="Confirmer le mot de passe"
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder="Confirmez votre nouveau mot de passe"
            leftIcon={<Lock className="h-4 w-4" />}
            rightIcon={
              <div className="cursor-pointer">
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none pointer-events-auto"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
            }
            error={errors.confirmPassword?.message}
            {...register('confirmPassword')}
          />

          {errors.root && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{errors.root.message}</p>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              Conseils pour un mot de passe sécurisé :
            </h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Au moins 8 caractères</li>
              <li>• Mélange de lettres majuscules et minuscules</li>
              <li>• Au moins un chiffre</li>
              <li>• Évitez les mots du dictionnaire</li>
            </ul>
          </div>

          <Button
            type="submit"
            className="w-full"
            isLoading={isLoading}
            disabled={isLoading}
            leftIcon={<Save className="h-4 w-4" />}
          >
            {isLoading ? 'Réinitialisation...' : 'Réinitialiser le mot de passe'}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            <a
              href="/login"
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Retour à la connexion
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
