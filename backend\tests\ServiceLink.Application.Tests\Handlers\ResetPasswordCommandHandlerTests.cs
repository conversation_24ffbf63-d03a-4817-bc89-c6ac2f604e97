using System.Threading;
using System.Threading.Tasks;
using Moq;
using Xunit;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Handlers;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;

public class ResetPasswordCommandHandlerTests
{
    [Fact]
    public async Task Handle_ValidToken_ResetsPasswordAndReturnsTrue()
    {
        // Arrange
        var userRepository = new Mock<IUserRepository>();
        var passwordService = new Mock<IPasswordService>();
        var passwordResetTokenRepository = new Mock<IPasswordResetTokenRepository>();
        var user = new User(
            ServiceLink.Domain.ValueObjects.Email.Create("<EMAIL>"),
            "Test",
            "User",
            ServiceLink.Domain.Enums.UserRole.Client,
            null,
            null
        );
        user.Activate();
        userRepository.Setup(r => r.GetByEmailAsync(It.IsAny<string>(), It.IsAny<CancellationToken>())).Returns((string email, CancellationToken ct) => Task.FromResult(user));
        passwordResetTokenRepository.Setup(r => r.ValidateTokenAsync(user.Id, "token")).Returns((Guid id, string token) => Task.FromResult(true));
        passwordService.Setup(p => p.HashPassword(It.IsAny<string>())).Returns(("hash", "salt"));
        userRepository.Setup(r => r.UpdateAsync(It.IsAny<User>(), It.IsAny<CancellationToken>())).Returns((User u, CancellationToken ct) => Task.CompletedTask);
        passwordResetTokenRepository.Setup(r => r.InvalidateTokenAsync(user.Id, "token")).Returns((Guid id, string token) => Task.CompletedTask);
        var handler = new ResetPasswordCommandHandler(userRepository.Object, passwordService.Object, passwordResetTokenRepository.Object);
        var command = new ResetPasswordCommand { Email = "<EMAIL>", Token = "token", NewPassword = "newpass" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.True(result);
    }
    [Fact]
    public async Task Handle_InvalidTokenOrUser_ReturnsFalse()
    {
        // Arrange
        var userRepository = new Mock<IUserRepository>();
        var passwordService = new Mock<IPasswordService>();
        var passwordResetTokenRepository = new Mock<IPasswordResetTokenRepository>();
        userRepository.Setup(r => r.GetByEmailAsync(It.IsAny<string>(), It.IsAny<CancellationToken>())).Returns((string email, CancellationToken ct) => Task.FromResult<User?>(null));
        var handler = new ResetPasswordCommandHandler(userRepository.Object, passwordService.Object, passwordResetTokenRepository.Object);
        var command = new ResetPasswordCommand { Email = "<EMAIL>", Token = "token", NewPassword = "newpass" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.False(result);
    }
}
