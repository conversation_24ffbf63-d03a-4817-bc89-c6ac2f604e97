using System.Threading;
using System.Threading.Tasks;
using Moq;
using Xunit;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Handlers;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;

public class RefreshTokenCommandHandlerTests
{
    [Fact]
    public async Task Handle_ValidRefreshToken_ReturnsNewTokens()
    {
        // Arrange
        var userRepository = new Mock<IUserRepository>();
        var jwtService = new Mock<IJwtService>();
        var refreshTokenRepository = new Mock<IRefreshTokenRepository>();
        var user = new User(
            ServiceLink.Domain.ValueObjects.Email.Create("<EMAIL>"),
            "Test",
            "User",
            ServiceLink.Domain.Enums.UserRole.Client,
            null,
            null
        );
        user.Activate();
        var refreshToken = new ServiceLink.Domain.Entities.RefreshToken { Token = "refresh-token", UserId = user.Id, IsRevoked = false, ExpiresAt = System.DateTime.UtcNow.AddMinutes(10) };
        refreshTokenRepository.Setup(r => r.GetByTokenAsync(It.IsAny<string>())).Returns((string token) => Task.FromResult(refreshToken));
        userRepository.Setup(r => r.GetByIdAsync(user.Id, It.IsAny<CancellationToken>())).Returns((Guid id, CancellationToken ct) => Task.FromResult(user));
        jwtService.Setup(j => j.GenerateJwtToken(user)).Returns("jwt-token");
        jwtService.Setup(j => j.GenerateRefreshToken(user)).Returns("new-refresh-token");
        var handler = new RefreshTokenCommandHandler(userRepository.Object, jwtService.Object, refreshTokenRepository.Object);
        var command = new RefreshTokenCommand { RefreshToken = "refresh-token" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.NotNull(result);
        Assert.Equal("jwt-token", result.Token);
        Assert.Equal("new-refresh-token", result.RefreshToken);
    }
    [Fact]
    public async Task Handle_InvalidRefreshToken_ReturnsNull()
    {
        // Arrange
        var userRepository = new Mock<IUserRepository>();
        var jwtService = new Mock<IJwtService>();
        var refreshTokenRepository = new Mock<IRefreshTokenRepository>();
        refreshTokenRepository.Setup(r => r.GetByTokenAsync(It.IsAny<string>())).Returns((string token) => Task.FromResult<ServiceLink.Domain.Entities.RefreshToken>(null));
        var handler = new RefreshTokenCommandHandler(userRepository.Object, jwtService.Object, refreshTokenRepository.Object);
        var command = new RefreshTokenCommand { RefreshToken = "invalid" };
        // Act
        var result = await handler.Handle(command, CancellationToken.None);
        // Assert
        Assert.Null(result);
    }
}
