import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Calendar, MapPin, Clock, CreditCard, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Progress } from '@/components/ui/progress';
import type { Service } from '@/stores/serviceStore';

interface BookingWizardProps {
  service: Service;
  onComplete: (bookingData: any) => void;
  onCancel: () => void;
}

interface BookingData {
  date?: Date;
  time?: string;
  duration?: number;
  address?: string;
  notes?: string;
  isUrgent?: boolean;
}

const steps = [
  { id: 1, title: 'Quand ?', icon: Calendar },
  { id: 2, title: 'Où ?', icon: MapPin },
  { id: 3, title: 'Détails', icon: Clock },
  { id: 4, title: 'Paiement', icon: CreditCard },
  { id: 5, title: 'Confirmation', icon: Check },
];

export const BookingWizard: React.FC<BookingWizardProps> = ({
  service,
  onComplete,
  onCancel
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [bookingData, setBookingData] = useState<BookingData>({});

  const progress = (currentStep / steps.length) * 100;

  const updateBookingData = (data: Partial<BookingData>) => {
    setBookingData(prev => ({ ...prev, ...data }));
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return bookingData.date && bookingData.time;
      case 2:
        return bookingData.address;
      case 3:
        return bookingData.duration;
      case 4:
        return true; // Payment validation would go here
      default:
        return true;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <DateTimeStep service={service} data={bookingData} onUpdate={updateBookingData} />;
      case 2:
        return <AddressStep service={service} data={bookingData} onUpdate={updateBookingData} />;
      case 3:
        return <DetailsStep service={service} data={bookingData} onUpdate={updateBookingData} />;
      case 4:
        return <PaymentStep service={service} data={bookingData} onUpdate={updateBookingData} />;
      case 5:
        return <ConfirmationStep service={service} data={bookingData} onComplete={onComplete} />;
      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Réserver : {service.name}</h1>
          <Button variant="ghost" onClick={onCancel}>
            Annuler
          </Button>
        </div>
        
        {/* Progress Bar */}
        <div className="space-y-4">
          <Progress value={progress} className="h-2" />
          
          {/* Step Indicators */}
          <div className="flex justify-between">
            {steps.map((step) => {
              const Icon = step.icon;
              const isActive = step.id === currentStep;
              const isCompleted = step.id < currentStep;
              
              return (
                <div key={step.id} className="flex flex-col items-center space-y-2">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center border-2 transition-colors
                    ${isActive ? 'bg-primary border-primary text-primary-foreground' : 
                      isCompleted ? 'bg-green-500 border-green-500 text-white' : 
                      'border-muted-foreground text-muted-foreground'}
                  `}>
                    {isCompleted ? (
                      <Check className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <span className={`text-sm ${isActive ? 'font-medium' : 'text-muted-foreground'}`}>
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Step Content */}
      <Card className="mb-8">
        <CardContent className="p-8">
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation */}
      {currentStep < steps.length && (
        <div className="flex justify-between">
          <Button 
            variant="outline" 
            onClick={prevStep}
            disabled={currentStep === 1}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Précédent
          </Button>
          
          <Button 
            onClick={nextStep}
            disabled={!canProceed()}
          >
            {currentStep === steps.length - 1 ? 'Confirmer' : 'Suivant'}
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      )}
    </div>
  );
};

// Step Components
const DateTimeStep: React.FC<{
  service: Service;
  data: BookingData;
  onUpdate: (data: Partial<BookingData>) => void;
}> = ({ service, data, onUpdate }) => {
  const today = new Date();
  const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
  
  const timeSlots = [
    '08:00', '09:00', '10:00', '11:00', '12:00', '13:00',
    '14:00', '15:00', '16:00', '17:00', '18:00', '19:00'
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-4">Quand souhaitez-vous ce service ?</h2>
        
        {/* Quick Date Options */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
          {[0, 1, 2, 3].map((days) => {
            const date = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
            const isSelected = data.date?.toDateString() === date.toDateString();
            
            return (
              <Button
                key={days}
                variant={isSelected ? "default" : "outline"}
                className="h-auto p-4 flex-col"
                onClick={() => onUpdate({ date })}
              >
                <span className="text-sm font-medium">
                  {days === 0 ? "Aujourd'hui" : 
                   days === 1 ? "Demain" : 
                   date.toLocaleDateString('fr-FR', { weekday: 'short' })}
                </span>
                <span className="text-xs">
                  {date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })}
                </span>
              </Button>
            );
          })}
        </div>

        {/* Time Slots */}
        {data.date && (
          <div>
            <h3 className="font-medium mb-3">Choisissez un créneau horaire</h3>
            <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
              {timeSlots.map((time) => (
                <Button
                  key={time}
                  variant={data.time === time ? "default" : "outline"}
                  size="sm"
                  onClick={() => onUpdate({ time })}
                >
                  {time}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Urgent Option */}
        <div className="mt-6 p-4 border border-orange-200 rounded-lg bg-orange-50 dark:bg-orange-950">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-orange-800 dark:text-orange-200">
                Service urgent (dans les 2h)
              </h4>
              <p className="text-sm text-orange-600 dark:text-orange-300">
                Supplément de 20% appliqué
              </p>
            </div>
            <Button
              variant={data.isUrgent ? "default" : "outline"}
              onClick={() => onUpdate({ isUrgent: !data.isUrgent })}
            >
              {data.isUrgent ? 'Sélectionné' : 'Choisir'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

const AddressStep: React.FC<{
  service: Service;
  data: BookingData;
  onUpdate: (data: Partial<BookingData>) => void;
}> = ({ service, data, onUpdate }) => {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Où doit avoir lieu le service ?</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Adresse complète</label>
          <textarea
            className="w-full p-3 border border-border rounded-lg resize-none"
            rows={3}
            placeholder="Entrez votre adresse complète..."
            value={data.address || ''}
            onChange={(e) => onUpdate({ address: e.target.value })}
          />
        </div>
        
        <div className="p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">Zone de service</h4>
          <p className="text-sm text-muted-foreground">
            Ce prestataire intervient dans un rayon de {service.serviceRadiusKm} km.
          </p>
        </div>
      </div>
    </div>
  );
};

const DetailsStep: React.FC<{
  service: Service;
  data: BookingData;
  onUpdate: (data: Partial<BookingData>) => void;
}> = ({ service, data, onUpdate }) => {
  const durationOptions = [
    { value: 60, label: '1 heure' },
    { value: 120, label: '2 heures' },
    { value: 180, label: '3 heures' },
    { value: 240, label: '4 heures' },
  ];

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Précisez vos besoins</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Durée estimée</label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {durationOptions.map((option) => (
              <Button
                key={option.value}
                variant={data.duration === option.value ? "default" : "outline"}
                onClick={() => onUpdate({ duration: option.value })}
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">
            Informations complémentaires (optionnel)
          </label>
          <textarea
            className="w-full p-3 border border-border rounded-lg resize-none"
            rows={4}
            placeholder="Décrivez vos besoins spécifiques, contraintes d'accès, etc."
            value={data.notes || ''}
            onChange={(e) => onUpdate({ notes: e.target.value })}
          />
        </div>
      </div>
    </div>
  );
};

const PaymentStep: React.FC<{
  service: Service;
  data: BookingData;
  onUpdate: (data: Partial<BookingData>) => void;
}> = ({ service, data, onUpdate }) => {
  const basePrice = service.basePrice * (data.duration || 60) / 60;
  const urgentSurcharge = data.isUrgent ? basePrice * 0.2 : 0;
  const total = basePrice + urgentSurcharge;

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Récapitulatif et paiement</h2>
      
      {/* Price Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Détail du prix</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between">
            <span>Service de base ({data.duration || 60} min)</span>
            <span>{basePrice.toFixed(2)}€</span>
          </div>
          {data.isUrgent && (
            <div className="flex justify-between text-orange-600">
              <span>Supplément urgent (20%)</span>
              <span>+{urgentSurcharge.toFixed(2)}€</span>
            </div>
          )}
          <div className="border-t pt-3 flex justify-between font-semibold text-lg">
            <span>Total</span>
            <span>{total.toFixed(2)}€</span>
          </div>
        </CardContent>
      </Card>
      
      {/* Payment Method */}
      <div>
        <h3 className="font-medium mb-3">Mode de paiement</h3>
        <div className="space-y-2">
          <Button variant="outline" className="w-full justify-start h-auto p-4">
            <CreditCard className="h-5 w-5 mr-3" />
            <div className="text-left">
              <div className="font-medium">Carte bancaire</div>
              <div className="text-sm text-muted-foreground">Paiement sécurisé</div>
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
};

const ConfirmationStep: React.FC<{
  service: Service;
  data: BookingData;
  onComplete: (bookingData: any) => void;
}> = ({ service, data, onComplete }) => {
  return (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-8 w-8 text-green-600" />
      </div>
      
      <div>
        <h2 className="text-2xl font-semibold mb-2">Réservation confirmée !</h2>
        <p className="text-muted-foreground">
          Votre demande a été envoyée au prestataire. Vous recevrez une confirmation sous peu.
        </p>
      </div>
      
      <Button onClick={() => onComplete(data)} size="lg">
        Retour à l'accueil
      </Button>
    </div>
  );
};
