using OtpNet;
using QRCoder;
using ServiceLink.Application.Interfaces;
using System.Security.Cryptography;
using System.Text;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Service pour l'authentification à deux facteurs (2FA)
/// </summary>
public class TwoFactorService : ITwoFactorService
{
    private const int SecretLength = 32; // 160 bits en base32
    private const int CodeLength = 6;
    private const int TimeStep = 30; // 30 secondes
    private const int RecoveryCodeLength = 8;

    /// <summary>
    /// Génère un secret 2FA pour un utilisateur
    /// </summary>
    /// <param name="userEmail">Email de l'utilisateur</param>
    /// <param name="issuer">Nom de l'application (défaut: ServiceLink)</param>
    /// <returns>Secret 2FA encodé en base32</returns>
    public string GenerateSecret(string userEmail, string issuer = "ServiceLink")
    {
        if (string.IsNullOrWhiteSpace(userEmail))
            throw new ArgumentException("L'email utilisateur est requis.", nameof(userEmail));

        if (string.IsNullOrWhiteSpace(issuer))
            throw new ArgumentException("L'émetteur est requis.", nameof(issuer));

        var secretBytes = new byte[SecretLength];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(secretBytes);

        return Base32Encoding.ToString(secretBytes);
    }

    /// <summary>
    /// Génère une URL QR Code pour configurer l'authentificateur
    /// </summary>
    /// <param name="userEmail">Email de l'utilisateur</param>
    /// <param name="secret">Secret 2FA</param>
    /// <param name="issuer">Nom de l'application</param>
    /// <returns>URL pour le QR Code</returns>
    public string GenerateQrCodeUrl(string userEmail, string secret, string issuer = "ServiceLink")
    {
        if (string.IsNullOrWhiteSpace(userEmail))
            throw new ArgumentException("L'email utilisateur est requis.", nameof(userEmail));

        if (string.IsNullOrWhiteSpace(secret))
            throw new ArgumentException("Le secret est requis.", nameof(secret));

        if (string.IsNullOrWhiteSpace(issuer))
            throw new ArgumentException("L'émetteur est requis.", nameof(issuer));

        // Format: otpauth://totp/Issuer:AccountName?secret=SECRET&issuer=ISSUER
        var accountName = Uri.EscapeDataString(userEmail);
        var issuerEncoded = Uri.EscapeDataString(issuer);
        var secretEncoded = Uri.EscapeDataString(secret);

        return $"otpauth://totp/{issuerEncoded}:{accountName}?secret={secretEncoded}&issuer={issuerEncoded}&digits={CodeLength}&period={TimeStep}";
    }

    /// <summary>
    /// Génère une image QR Code en base64
    /// </summary>
    /// <param name="qrCodeUrl">URL du QR Code</param>
    /// <param name="size">Taille de l'image (défaut: 200px)</param>
    /// <returns>Image QR Code en base64</returns>
    public async Task<string> GenerateQrCodeImageAsync(string qrCodeUrl, int size = 200)
    {
        if (string.IsNullOrWhiteSpace(qrCodeUrl))
            throw new ArgumentException("L'URL du QR Code est requise.", nameof(qrCodeUrl));

        if (size < 50 || size > 1000)
            throw new ArgumentException("La taille doit être entre 50 et 1000 pixels.", nameof(size));

        return await Task.Run(() =>
        {
            using var qrGenerator = new QRCodeGenerator();
            var qrCodeData = qrGenerator.CreateQrCode(qrCodeUrl, QRCodeGenerator.ECCLevel.M);
            
            using var qrCode = new PngByteQRCode(qrCodeData);
            var qrCodeBytes = qrCode.GetGraphic(size / 25); // Ajuster la taille des pixels
            
            return Convert.ToBase64String(qrCodeBytes);
        });
    }

    /// <summary>
    /// Valide un code TOTP
    /// </summary>
    /// <param name="secret">Secret 2FA de l'utilisateur</param>
    /// <param name="code">Code à valider</param>
    /// <param name="window">Fenêtre de tolérance en périodes (défaut: 1)</param>
    /// <returns>True si le code est valide</returns>
    public bool ValidateCode(string secret, string code, int window = 1)
    {
        if (string.IsNullOrWhiteSpace(secret) || string.IsNullOrWhiteSpace(code))
            return false;

        if (!IsValidSecret(secret))
            return false;

        if (code.Length != CodeLength || !code.All(char.IsDigit))
            return false;

        try
        {
            var secretBytes = Base32Encoding.ToBytes(secret);
            var totp = new Totp(secretBytes, step: TimeStep);

            // Vérifier le code actuel et les codes dans la fenêtre de tolérance
            var currentTime = DateTime.UtcNow;
            
            for (int i = -window; i <= window; i++)
            {
                var timeStep = currentTime.AddSeconds(i * TimeStep);
                var expectedCode = totp.ComputeTotp(timeStep);
                
                if (expectedCode == code)
                    return true;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Génère des codes de récupération
    /// </summary>
    /// <param name="count">Nombre de codes à générer (défaut: 10)</param>
    /// <returns>Liste des codes de récupération</returns>
    public string[] GenerateRecoveryCodes(int count = 10)
    {
        if (count < 1 || count > 50)
            throw new ArgumentException("Le nombre de codes doit être entre 1 et 50.", nameof(count));

        var codes = new string[count];
        using var rng = RandomNumberGenerator.Create();

        for (int i = 0; i < count; i++)
        {
            codes[i] = GenerateRecoveryCode(rng);
        }

        return codes;
    }

    /// <summary>
    /// Valide un code de récupération
    /// </summary>
    /// <param name="recoveryCodes">Codes de récupération stockés (séparés par des virgules)</param>
    /// <param name="code">Code à valider</param>
    /// <returns>Tuple (isValid, updatedRecoveryCodes) - les codes mis à jour sans le code utilisé</returns>
    public (bool isValid, string updatedRecoveryCodes) ValidateRecoveryCode(string recoveryCodes, string code)
    {
        if (string.IsNullOrWhiteSpace(recoveryCodes) || string.IsNullOrWhiteSpace(code))
            return (false, recoveryCodes);

        var codes = recoveryCodes.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                .Select(c => c.Trim())
                                .ToList();

        var normalizedCode = code.Replace("-", "").Replace(" ", "").ToUpperInvariant();

        var matchingCode = codes.FirstOrDefault(c => 
            c.Replace("-", "").Replace(" ", "").ToUpperInvariant() == normalizedCode);

        if (matchingCode == null)
            return (false, recoveryCodes);

        // Retirer le code utilisé
        codes.Remove(matchingCode);
        var updatedCodes = string.Join(",", codes);

        return (true, updatedCodes);
    }

    /// <summary>
    /// Génère un code de sauvegarde temporaire pour la désactivation d'urgence
    /// </summary>
    /// <param name="userEmail">Email de l'utilisateur</param>
    /// <param name="validityMinutes">Durée de validité en minutes (défaut: 30)</param>
    /// <returns>Code de sauvegarde temporaire</returns>
    public string GenerateBackupCode(string userEmail, int validityMinutes = 30)
    {
        if (string.IsNullOrWhiteSpace(userEmail))
            throw new ArgumentException("L'email utilisateur est requis.", nameof(userEmail));

        if (validityMinutes < 1 || validityMinutes > 1440) // Max 24h
            throw new ArgumentException("La validité doit être entre 1 minute et 24 heures.", nameof(validityMinutes));

        var expiryTime = DateTime.UtcNow.AddMinutes(validityMinutes);
        var data = $"{userEmail}:{expiryTime:yyyy-MM-ddTHH:mm:ssZ}";
        
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes("ServiceLink-Backup-Key"));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
        var code = Convert.ToBase64String(hash).Replace("+", "").Replace("/", "").Replace("=", "")[..12];
        
        return $"{code}:{expiryTime:yyyy-MM-ddTHH:mm:ssZ}";
    }

    /// <summary>
    /// Valide un code de sauvegarde temporaire
    /// </summary>
    /// <param name="userEmail">Email de l'utilisateur</param>
    /// <param name="backupCode">Code de sauvegarde</param>
    /// <returns>True si le code est valide et non expiré</returns>
    public bool ValidateBackupCode(string userEmail, string backupCode)
    {
        if (string.IsNullOrWhiteSpace(userEmail) || string.IsNullOrWhiteSpace(backupCode))
            return false;

        try
        {
            var parts = backupCode.Split(':');
            if (parts.Length != 2)
                return false;

            var code = parts[0];
            var expiryString = parts[1];

            if (!DateTime.TryParse(expiryString, out var expiry))
                return false;

            if (DateTime.UtcNow > expiry)
                return false;

            // Régénérer le code attendu
            var data = $"{userEmail}:{expiryString}";
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes("ServiceLink-Backup-Key"));
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            var expectedCode = Convert.ToBase64String(hash).Replace("+", "").Replace("/", "").Replace("=", "")[..12];

            return code == expectedCode;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Vérifie si un secret 2FA est valide
    /// </summary>
    /// <param name="secret">Secret à valider</param>
    /// <returns>True si le secret est valide</returns>
    public bool IsValidSecret(string secret)
    {
        if (string.IsNullOrWhiteSpace(secret))
            return false;

        try
        {
            var bytes = Base32Encoding.ToBytes(secret);
            return bytes.Length >= 10; // Au moins 80 bits
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Génère un code TOTP pour un secret donné (utile pour les tests)
    /// </summary>
    /// <param name="secret">Secret 2FA</param>
    /// <param name="timestamp">Timestamp Unix (optionnel, utilise l'heure actuelle par défaut)</param>
    /// <returns>Code TOTP à 6 chiffres</returns>
    public string GenerateCode(string secret, long? timestamp = null)
    {
        if (string.IsNullOrWhiteSpace(secret))
            throw new ArgumentException("Le secret est requis.", nameof(secret));

        if (!IsValidSecret(secret))
            throw new ArgumentException("Le secret n'est pas valide.", nameof(secret));

        var secretBytes = Base32Encoding.ToBytes(secret);
        var totp = new Totp(secretBytes, step: TimeStep);

        if (timestamp.HasValue)
        {
            var dateTime = DateTimeOffset.FromUnixTimeSeconds(timestamp.Value).DateTime;
            return totp.ComputeTotp(dateTime);
        }

        return totp.ComputeTotp();
    }

    /// <summary>
    /// Obtient le temps restant avant expiration du code actuel
    /// </summary>
    /// <returns>Secondes restantes avant le prochain code</returns>
    public int GetRemainingSeconds()
    {
        var now = DateTime.UtcNow;
        var secondsSinceEpoch = (long)(now - new DateTime(1970, 1, 1)).TotalSeconds;
        var currentStep = secondsSinceEpoch / TimeStep;
        var nextStepTime = (currentStep + 1) * TimeStep;
        
        return (int)(nextStepTime - secondsSinceEpoch);
    }

    /// <summary>
    /// Formate les codes de récupération pour l'affichage
    /// </summary>
    /// <param name="recoveryCodes">Codes de récupération (séparés par des virgules)</param>
    /// <returns>Codes formatés pour l'affichage</returns>
    public string[] FormatRecoveryCodesForDisplay(string recoveryCodes)
    {
        if (string.IsNullOrWhiteSpace(recoveryCodes))
            return Array.Empty<string>();

        return recoveryCodes.Split(',', StringSplitOptions.RemoveEmptyEntries)
                           .Select(code => code.Trim())
                           .Where(code => !string.IsNullOrEmpty(code))
                           .Select(FormatSingleRecoveryCode)
                           .ToArray();
    }

    /// <summary>
    /// Génère un code de récupération individuel
    /// </summary>
    /// <param name="rng">Générateur de nombres aléatoires</param>
    /// <returns>Code de récupération formaté</returns>
    private static string GenerateRecoveryCode(RandomNumberGenerator rng)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var code = new StringBuilder();

        for (int i = 0; i < RecoveryCodeLength; i++)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var index = BitConverter.ToUInt32(bytes, 0) % chars.Length;
            code.Append(chars[(int)index]);
        }

        return FormatSingleRecoveryCode(code.ToString());
    }

    /// <summary>
    /// Formate un code de récupération individuel
    /// </summary>
    /// <param name="code">Code brut</param>
    /// <returns>Code formaté (ex: ABCD-EFGH)</returns>
    private static string FormatSingleRecoveryCode(string code)
    {
        if (code.Length != RecoveryCodeLength)
            return code;

        return $"{code[..4]}-{code[4..]}";
    }
}
