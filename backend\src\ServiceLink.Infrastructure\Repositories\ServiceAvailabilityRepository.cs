using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Infrastructure.Data;

namespace ServiceLink.Infrastructure.Repositories;

/// <summary>
/// Implémentation du repository pour les disponibilités de service
/// </summary>
public class ServiceAvailabilityRepository : Repository<ServiceAvailability>, IServiceAvailabilityRepository
{
    public ServiceAvailabilityRepository(ServiceLinkDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient les disponibilités d'un service
    /// </summary>
    public async Task<IEnumerable<ServiceAvailability>> GetByServiceIdAsync(Guid serviceId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ServiceAvailability>()
            .Where(a => a.ServiceId == serviceId && a.IsActive)
            .Include(a => a.Service)
            .Include(a => a.Provider)
            .OrderBy(a => a.StartDate)
            .ThenBy(a => a.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les disponibilités d'un prestataire
    /// </summary>
    public async Task<IEnumerable<ServiceAvailability>> GetByProviderIdAsync(Guid providerId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ServiceAvailability>()
            .Where(a => a.ProviderId == providerId && a.IsActive)
            .Include(a => a.Service)
            .Include(a => a.Provider)
            .OrderBy(a => a.StartDate)
            .ThenBy(a => a.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les disponibilités pour une période
    /// </summary>
    public async Task<IEnumerable<ServiceAvailability>> GetForPeriodAsync(
        Guid serviceId, 
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        return await Context.Set<ServiceAvailability>()
            .Where(a => a.ServiceId == serviceId && 
                       a.IsActive &&
                       a.StartDate <= endDate.Date &&
                       a.EndDate >= startDate.Date)
            .Include(a => a.Service)
            .Include(a => a.Provider)
            .OrderBy(a => a.StartDate)
            .ThenBy(a => a.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Vérifie la disponibilité pour une date/heure
    /// </summary>
    public async Task<bool> IsAvailableAsync(
        Guid serviceId, 
        DateTime dateTime, 
        int durationMinutes, 
        CancellationToken cancellationToken = default)
    {
        var date = dateTime.Date;
        var time = TimeOnly.FromDateTime(dateTime);
        var endTime = time.AddMinutes(durationMinutes);

        // Vérifier s'il y a une disponibilité qui couvre cette période
        var availability = await Context.Set<ServiceAvailability>()
            .FirstOrDefaultAsync(a => 
                a.ServiceId == serviceId &&
                a.IsActive &&
                a.StartDate <= date &&
                a.EndDate >= date &&
                a.StartTime <= time &&
                a.EndTime >= endTime &&
                a.CurrentCapacity < a.MaxCapacity, cancellationToken);

        if (availability == null)
            return false;

        // Si c'est récurrent, vérifier le jour de la semaine
        if (availability.IsRecurring)
        {
            var dayOfWeek = dateTime.DayOfWeek;
            var validDays = availability.GetDaysOfWeek();
            if (!validDays.Contains(dayOfWeek))
                return false;
        }

        // Vérifier qu'il n'y a pas de conflit avec d'autres réservations
        var endDateTime = dateTime.AddMinutes(durationMinutes);
        var hasConflict = await Context.Set<Booking>()
            .AnyAsync(b => b.ServiceId == serviceId &&
                          b.Status != Domain.Enums.BookingStatus.Cancelled &&
                          b.Status != Domain.Enums.BookingStatus.Rejected &&
                          b.Status != Domain.Enums.BookingStatus.Expired &&
                          b.ScheduledDate < endDateTime &&
                          b.ScheduledEndDate > dateTime, cancellationToken);

        return !hasConflict;
    }

    /// <summary>
    /// Obtient une disponibilité avec toutes ses relations
    /// </summary>
    public override async Task<ServiceAvailability?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ServiceAvailability>()
            .Include(a => a.Service)
            .Include(a => a.Provider)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    /// <summary>
    /// Met à jour la capacité d'une disponibilité
    /// </summary>
    public async Task UpdateCapacityAsync(Guid availabilityId, int newCurrentCapacity, CancellationToken cancellationToken = default)
    {
        var availability = await GetByIdAsync(availabilityId, cancellationToken);
        if (availability != null)
        {
            // Note: En production, vous pourriez vouloir une méthode plus directe pour éviter de charger l'entité complète
            await Context.Database.ExecuteSqlRawAsync(
                "UPDATE ServiceAvailabilities SET CurrentCapacity = {0} WHERE Id = {1}",
                newCurrentCapacity, availabilityId);
        }
    }

    /// <summary>
    /// Obtient les disponibilités qui se chevauchent
    /// </summary>
    public async Task<IEnumerable<ServiceAvailability>> GetOverlappingAvailabilitiesAsync(
        Guid serviceId,
        DateTime startDate,
        DateTime endDate,
        TimeOnly startTime,
        TimeOnly endTime,
        Guid? excludeId = null,
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<ServiceAvailability>()
            .Where(a => a.ServiceId == serviceId &&
                       a.IsActive &&
                       a.StartDate <= endDate.Date &&
                       a.EndDate >= startDate.Date &&
                       a.StartTime < endTime &&
                       a.EndTime > startTime);

        if (excludeId.HasValue)
        {
            query = query.Where(a => a.Id != excludeId.Value);
        }

        return await query
            .Include(a => a.Service)
            .Include(a => a.Provider)
            .ToListAsync(cancellationToken);
    }
}


