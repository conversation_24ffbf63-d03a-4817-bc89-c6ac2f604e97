using System.ComponentModel.DataAnnotations;

namespace ServiceLink.Application.DTOs;

public class ServiceCategoryDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? Icon { get; set; }
    public Guid? ParentId { get; set; }
    public List<ServiceCategoryDto>? Children { get; set; }
    public int ServiceCount { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ServiceProviderDto
{
    public Guid Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public string? Avatar { get; set; }
    public string? Bio { get; set; }
    public decimal Rating { get; set; }
    public int ReviewCount { get; set; }
    public int CompletedBookings { get; set; }
    public string ResponseTime { get; set; } = string.Empty;
    public bool IsVerified { get; set; }
    public bool IsOnline { get; set; }
    public DateTime JoinedAt { get; set; }
    public ServiceLocationDto? Location { get; set; }
    public List<string> Specialties { get; set; } = new();
    public List<string> Languages { get; set; } = new();
    public List<string> Certifications { get; set; } = new();
}

public class ServiceLocationDto
{
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public int ServiceRadius { get; set; }
}

public class ServiceAvailabilityDto
{
    public bool Monday { get; set; }
    public bool Tuesday { get; set; }
    public bool Wednesday { get; set; }
    public bool Thursday { get; set; }
    public bool Friday { get; set; }
    public bool Saturday { get; set; }
    public bool Sunday { get; set; }
    public string StartTime { get; set; } = string.Empty;
    public string EndTime { get; set; } = string.Empty;
    public string TimeZone { get; set; } = string.Empty;
}

public class ServiceDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ShortDescription { get; set; }
    public Guid CategoryId { get; set; }
    public ServiceCategoryDto? Category { get; set; }
    public Guid ProviderId { get; set; }
    public ServiceProviderDto? Provider { get; set; }
    public decimal BasePrice { get; set; }
    public string PricingUnit { get; set; } = string.Empty;
    public string Currency { get; set; } = string.Empty;
    public int? Duration { get; set; }
    public bool IsActive { get; set; }
    public bool IsUrgent { get; set; }
    public bool IsFeatured { get; set; }
    public decimal Rating { get; set; }
    public int ReviewCount { get; set; }
    public int BookingCount { get; set; }
    public List<string> Images { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public List<string>? Requirements { get; set; }
    public List<string>? Included { get; set; }
    public List<string>? Excluded { get; set; }
    public ServiceLocationDto Location { get; set; } = new();
    public ServiceAvailabilityDto Availability { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ServiceStatsDto
{
    public int TotalServices { get; set; }
    public int TotalProviders { get; set; }
    public int TotalBookings { get; set; }
    public decimal AverageRating { get; set; }
    public List<TopCategoryDto> TopCategories { get; set; } = new();
}

public class TopCategoryDto
{
    public string Name { get; set; } = string.Empty;
    public int Count { get; set; }
}

public class CreateServiceDto
{
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public string Description { get; set; } = string.Empty;
    
    public string? ShortDescription { get; set; }
    
    [Required]
    public Guid CategoryId { get; set; }
    
    [Required]
    [Range(0.01, double.MaxValue)]
    public decimal BasePrice { get; set; }
    
    [Required]
    public string PricingUnit { get; set; } = string.Empty;
    
    [Required]
    public string Currency { get; set; } = "EUR";
    
    public int? Duration { get; set; }
    public bool IsUrgent { get; set; }
    public List<string> Images { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public List<string>? Requirements { get; set; }
    public List<string>? Included { get; set; }
    public List<string>? Excluded { get; set; }
    
    [Required]
    public ServiceLocationDto Location { get; set; } = new();
    
    [Required]
    public ServiceAvailabilityDto Availability { get; set; } = new();
}

public class UpdateServiceDto
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? ShortDescription { get; set; }
    public Guid? CategoryId { get; set; }
    public decimal? BasePrice { get; set; }
    public string? PricingUnit { get; set; }
    public string? Currency { get; set; }
    public int? Duration { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsUrgent { get; set; }
    public bool? IsFeatured { get; set; }
    public List<string>? Images { get; set; }
    public List<string>? Tags { get; set; }
    public List<string>? Requirements { get; set; }
    public List<string>? Included { get; set; }
    public List<string>? Excluded { get; set; }
    public ServiceLocationDto? Location { get; set; }
    public ServiceAvailabilityDto? Availability { get; set; }
}
