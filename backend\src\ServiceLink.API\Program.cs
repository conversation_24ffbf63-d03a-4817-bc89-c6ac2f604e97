using AspNetCoreRateLimit;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using ServiceLink.API.Configuration;
using ServiceLink.API.Middleware;
using ServiceLink.API.Services;
using ServiceLink.Application;
using ServiceLink.Infrastructure;
using ServiceLink.Infrastructure.Configuration;
using ServiceLink.API.Extensions;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Configuration
var jwtSettings = new JwtSettings();
builder.Configuration.GetSection("JWT").Bind(jwtSettings);

var corsSettings = new CorsSettings();
builder.Configuration.GetSection("CORS").Bind(corsSettings);

var swaggerSettings = new SwaggerSettings();
builder.Configuration.GetSection("Swagger").Bind(swaggerSettings);

var securitySettings = new SecuritySettings();
builder.Configuration.GetSection("Security").Bind(securitySettings);

// Validation de la configuration JWT
if (!jwtSettings.IsValid())
{
    throw new InvalidOperationException("Configuration JWT invalide. Vérifiez la clé secrète et les autres paramètres.");
}

// Services de base
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Configuration des services par couches
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);

// Configuration des services de notification externes
builder.Services.AddExternalNotificationServices(builder.Configuration);

// Configuration des settings
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JWT"));
builder.Services.Configure<CorsSettings>(builder.Configuration.GetSection("CORS"));
builder.Services.Configure<SecuritySettings>(builder.Configuration.GetSection("Security"));
builder.Services.Configure<SwaggerSettings>(builder.Configuration.GetSection("Swagger"));

// Services API
builder.Services.AddScoped<IJwtService, JwtService>();

// Authentification JWT
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.SecretKey)),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = jwtSettings.Audience,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.FromMinutes(jwtSettings.ClockSkewMinutes),
        RequireExpirationTime = true
    };

    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogWarning("Échec de l'authentification JWT: {Error}", context.Exception.Message);
            return Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            var userId = context.Principal?.FindFirst("sub")?.Value ?? "Unknown";
            logger.LogDebug("Token JWT validé pour l'utilisateur {UserId}", userId);
            return Task.CompletedTask;
        }
    };
});

// Autorisation
builder.Services.AddAuthorization();

// CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        if (corsSettings.AllowedOrigins.Any())
        {
            policy.WithOrigins(corsSettings.AllowedOrigins);
        }
        else
        {
            policy.AllowAnyOrigin();
        }

        if (corsSettings.AllowedMethods.Any())
        {
            policy.WithMethods(corsSettings.AllowedMethods);
        }
        else
        {
            policy.AllowAnyMethod();
        }

        if (corsSettings.AllowedHeaders.Any())
        {
            policy.WithHeaders(corsSettings.AllowedHeaders);
        }
        else
        {
            policy.AllowAnyHeader();
        }

        if (corsSettings.AllowCredentials)
        {
            policy.AllowCredentials();
        }

        policy.SetPreflightMaxAge(TimeSpan.FromSeconds(corsSettings.MaxAge));
    });
});

// Rate Limiting
builder.Services.AddMemoryCache();
builder.Services.Configure<IpRateLimitOptions>(builder.Configuration.GetSection("RateLimit"));
builder.Services.AddInMemoryRateLimiting();
builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();

// Swagger
builder.Services.AddSwaggerConfiguration(swaggerSettings);

// Health Checks
builder.Services.AddHealthChecks();

var app = builder.Build();

// Pipeline de middleware
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseErrorHandling();
}

// Logging des requêtes
app.UseRequestLogging();

// HTTPS Redirection
if (securitySettings.RequireHttps)
{
    app.UseHttpsRedirection();
}

// CORS
app.UseCors();

// Rate Limiting
app.UseIpRateLimiting();

// Authentification et autorisation
app.UseAuthentication();
app.UseAuthorization();

// Swagger
if (app.Environment.IsDevelopment() || builder.Configuration.GetValue<bool>("Swagger:EnableInProduction"))
{
    app.UseSwaggerConfiguration(swaggerSettings);
}

// Health Checks
app.MapHealthChecks("/health");

// SignalR Hubs
app.MapSignalRHubs();

// Controllers
app.MapControllers();

app.Run();

/// <summary>
/// Classe Program publique pour les tests d'intégration
/// </summary>
public partial class Program { }
