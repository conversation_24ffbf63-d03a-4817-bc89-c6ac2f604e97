import { useEffect } from 'react';
import { useAuthStore } from '@/stores/authStore';

/**
 * Hook pour initialiser l'authentification au démarrage de l'application
 * Corrige le problème de désynchronisation entre localStorage et Zustand
 */
export function useAuthInitialization() {
  const { initializeAuth, isAuthenticated, user } = useAuthStore();

  useEffect(() => {
    // Initialiser l'authentification au montage du composant
    console.log('🚀 Initialisation de l\'authentification...');
    initializeAuth();
    
    // Debug: afficher l'état après initialisation
    setTimeout(() => {
      const currentState = useAuthStore.getState();
      console.log('📊 État auth après initialisation:', {
        isAuthenticated: currentState.isAuthenticated,
        hasUser: !!currentState.user,
        hasToken: !!currentState.token,
        userRole: currentState.user?.role
      });
    }, 100);
  }, [initializeAuth]);

  // Surveiller les changements d'état pour debug
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('✅ Utilisateur connecté:', {
        email: user.email,
        role: user.role,
        firstName: user.firstName
      });
    } else {
      console.log('❌ Utilisateur non connecté');
    }
  }, [isAuthenticated, user]);

  return {
    isAuthenticated,
    user,
    isInitialized: true // Toujours true car Zustand persist est synchrone
  };
}
