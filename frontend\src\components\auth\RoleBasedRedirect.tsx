import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '@/stores/authStore';

export const RoleBasedRedirect: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />;
  }

  // Redirection selon le rôle
  switch (user.role) {
    case 'Client':
      return <Navigate to="/dashboard/client" replace />;
    case 'Provider':
    case 'ServiceProvider':
    case 'Prestataire':
      return <Navigate to="/dashboard/provider" replace />;
    case 'Admin':
    case 'Admin Global':
      return <Navigate to="/dashboard/admin" replace />;
    case 'Manager':
      return <Navigate to="/dashboard/manager" replace />;
    case 'Support':
      return <Navigate to="/dashboard/support" replace />;
    case 'Supervisor':
    case 'Superviseur':
      return <Navigate to="/dashboard/supervisor" replace />;
    default:
      // Fallback vers le dashboard général
      return <Navigate to="/dashboard/general" replace />;
  }
};
