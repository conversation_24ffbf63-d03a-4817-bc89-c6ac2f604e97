import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '@/stores/authStore';

export const RoleBasedRedirect: React.FC = () => {
  const { user, isAuthenticated, isInitialized } = useAuthStore();

  console.log('🔄 RoleBasedRedirect:', { isAuthenticated, userRole: user?.role, userEmail: user?.email, isInitialized });

  // Attendre l'initialisation
  if (!isInitialized) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>;
  }

  if (!isAuthenticated || !user) {
    console.log('❌ Redirection vers login - non authentifié');
    return <Navigate to="/login" replace />;
  }

  // Redirection selon le rôle
  switch (user.role) {
    case 'Client':
      console.log('✅ Redirection Client vers /dashboard/client');
      return <Navigate to="/dashboard/client" replace />;
    case 'Provider':
    case 'ServiceProvider':
    case 'Prestataire':
      console.log('✅ Redirection Provider vers /dashboard/provider');
      return <Navigate to="/dashboard/provider" replace />;
    case 'Admin':
    case 'Admin Global':
      console.log('✅ Redirection Admin vers /dashboard/admin');
      return <Navigate to="/dashboard/admin" replace />;
    case 'Manager':
      console.log('✅ Redirection Manager vers /dashboard/manager');
      return <Navigate to="/dashboard/manager" replace />;
    case 'Support':
      console.log('✅ Redirection Support vers /dashboard/support');
      return <Navigate to="/dashboard/support" replace />;
    case 'Supervisor':
    case 'Superviseur':
      console.log('✅ Redirection Supervisor vers /dashboard/supervisor');
      return <Navigate to="/dashboard/supervisor" replace />;
    default:
      console.log('⚠️ Rôle non reconnu, redirection vers /dashboard/general:', user.role);
      return <Navigate to="/dashboard/general" replace />;
  }
};
