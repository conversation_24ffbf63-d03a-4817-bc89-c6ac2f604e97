# ServiceLink - .gitignore
# Comprehensive gitignore for .NET, React, and Docker projects

# ===== .NET / C# =====
## Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

## Migration results
[Dd]ata/

## Visual Studio files
.vs/
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

## User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

## Mono auto generated files
mono_crash.*

## Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

## Benchmark Results
BenchmarkDotNet.Artifacts/

## .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

## StyleCop
StyleCopReport.xml

## Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.tlog
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

## Chutzpah Test files
_Chutzpah*

## Visual C++ cache files
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

## Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

## Visual Studio Trace Files
*.e2e

## TFS 2012 Local Workspace
$tf/

## Guidance Automation Toolkit
*.gpState

## ReSharper is a .NET coding add-in
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

## TeamCity is a build add-in
_TeamCity*

## DotCover is a Code Coverage Tool
*.dotCover

## AxoCover is a Code Coverage Tool
.axoCover/*
!.axoCover/settings.json

## Coverlet is a free, cross platform Code Coverage Tool
coverage*.json
coverage*.xml
coverage*.info

## Visual Studio code coverage results
*.coverage
*.coveragexml

## NCrunch
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

## MightyMoose
*.mm.*
AutoTest.Net/

## Web workbench (sass)
.sass-cache/

## Installshield output folder
[Ee]xpress/

## DocProject is a documentation generator add-in
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/Html2
DocProject/Help/html

## Click-Once directory
publish/

## Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
# Note: Comment the next line if you want to checkin your web deploy settings,
# but database connection strings (with potential passwords) will be unencrypted
*.pubxml
*.publishproj

## Microsoft Azure Web App publish settings. Comment the next line if you want to
# checkin your Azure Web App publish settings, but sensitive information contained
# in these scripts will be unencrypted
PublishScripts/

## NuGet Packages
*.nupkg
# NuGet Symbol Packages
*.snupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

## Microsoft Azure Build Output
csx/
*.build.csdef

## Microsoft Azure Emulator
ecf/
rcf/

## Windows Store app package directories and files
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx
*.appxbundle
*.appxupload

## Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!?*.[Cc]ache/

## Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs

## Including strong name files can present a security risk
# (https://github.com/github/gitignore/pull/2483#issue-259490424)
#*.snk

## Since there are multiple workflows, uncomment next line to ignore bower_components
# (https://github.com/github/gitignore/pull/1529#issuecomment-104372622)
#bower_components/

## RIA/Silverlight projects
Generated_Code/

## Backup & report files from converting an old project file
# to a newer Visual Studio version. Backup files are not needed,
# because we have git ;-)
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm
CordovaApp.projitems

## SQL Server files
*.mdf
*.ldf
*.ndf

## Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser
*- [Bb]ackup.rdl
*- [Bb]ackup ([0-9]).rdl
*- [Bb]ackup ([0-9][0-9]).rdl

## Microsoft Fakes
FakesAssemblies/

## GhostDoc plugin setting file
*.GhostDoc.xml

## Node.js Tools for Visual Studio
.ntvs_analysis.dat
node_modules/

## Visual Studio 6 build log
*.plg

## Visual Studio 6 workspace options file
*.opt

## Visual Studio 6 auto-generated workspace file (contains which files were open etc.)
*.vbw

## Visual Studio 6 auto-generated project file (contains which files were open etc.)
*.vbp

## Visual Studio 6 workspace and project file (working project files containing files to include in project)
*.dsw
*.dsp

## Visual Studio 6 technical files
*.ncb
*.aps

## Visual Studio LightSwitch build output
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts
**/*.Server/ModelManifest.xml
_Pvt_Extensions

## Paket dependency manager
.paket/paket.exe
paket-files/

## FAKE - F# Make
.fake/

## CodeRush personal settings
.cr/personal

## Python Tools for Visual Studio (PTVS)
__pycache__/
*.pyc

## Cake - Uncomment if you are using it
# tools/**
# !tools/packages.config

## Tabs Studio
*.tss

## Telerik's JustMock configuration file
*.jmconfig

## BizTalk build output
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs

## OpenCover UI analysis results
OpenCover/

## Azure Stream Analytics local run output
ASALocalRun/

## MSBuild Binary and Structured Log
*.binlog

## NVidia Nsight GPU debugger configuration file
*.nvuser

## MFractors (Xamarin productivity tool) working folder
.mfractor/

## Local History for Visual Studio
.localhistory/

## Visual Studio History (VSHistory) files
.vshistory/

## BeatPulse healthcheck temp database
healthchecksdb

## Backup folder for Package Reference Convert tool in Visual Studio 2017
MigrationBackup/

## Ionide (cross platform F# VS Code tools) working folder
.ionide/

## Fody - auto-generated XML schema
FodyWeavers.xsd

## VS Code files for those working on multiple tools
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Local History for Visual Studio Code
.history/

# Windows Installer files from build outputs
*.cab
*.msi
*.msix
*.msm
*.msp

# JetBrains Rider
*.sln.iml

# ===== Node.js / React =====
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Vite
dist-ssr
*.local

# ===== Docker =====
# Docker volumes
docker-volumes/

# ===== Database =====
# Database files
*.db
*.sqlite
*.sqlite3

# ===== IDE =====
# JetBrains IDEs
.idea/
*.swp
*.swo

# Sublime Text
*.sublime-project
*.sublime-workspace

# ===== OS =====
# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== ServiceLink Specific =====
# Configuration files with secrets
appsettings.*.json
*.secrets.json
[Rr]eferences/

# Logs
logs/
*.log

# Temporary files
temp/
tmp/

# Build artifacts
artifacts/
publish/

# Test results
TestResults/
coverage/
*.trx
*.coverage

# Documentation build
docs/_site/
docs/.jekyll-cache/

# Backup files
*.bak
*.backup
