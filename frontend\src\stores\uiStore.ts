import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type Theme = 'light' | 'dark' | 'system';
export type Language = 'en' | 'fr';

export interface Modal {
  id: string;
  component: string;
  props?: Record<string, any>;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closable?: boolean;
}

export interface Sidebar {
  isOpen: boolean;
  variant: 'default' | 'floating' | 'overlay';
}

export interface Breadcrumb {
  label: string;
  href?: string;
  isActive?: boolean;
}

export interface UIState {
  // Theme & Appearance
  theme: Theme;
  language: Language;
  
  // Layout
  sidebar: Sidebar;
  isHeaderVisible: boolean;
  isFooterVisible: boolean;
  
  // Navigation
  breadcrumbs: Breadcrumb[];
  currentPage: string;
  
  // Modals & Overlays
  modals: Modal[];
  isLoading: boolean;
  loadingMessage?: string;
  
  // Mobile
  isMobile: boolean;
  
  // Search
  isSearchOpen: boolean;
  searchQuery: string;
  
  // Notifications UI
  isNotificationPanelOpen: boolean;
  
  // Actions
  setTheme: (theme: Theme) => void;
  setLanguage: (language: Language) => void;
  toggleSidebar: () => void;
  setSidebarOpen: (isOpen: boolean) => void;
  setSidebarVariant: (variant: Sidebar['variant']) => void;
  setHeaderVisible: (visible: boolean) => void;
  setFooterVisible: (visible: boolean) => void;
  setBreadcrumbs: (breadcrumbs: Breadcrumb[]) => void;
  setCurrentPage: (page: string) => void;
  openModal: (modal: Omit<Modal, 'id'>) => string;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  setLoading: (isLoading: boolean, message?: string) => void;
  setMobile: (isMobile: boolean) => void;
  setSearchOpen: (isOpen: boolean) => void;
  setSearchQuery: (query: string) => void;
  setNotificationPanelOpen: (isOpen: boolean) => void;
}

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // Initial state
      theme: 'system',
      language: 'fr',
      
      sidebar: {
        isOpen: true,
        variant: 'default',
      },
      isHeaderVisible: true,
      isFooterVisible: true,
      
      breadcrumbs: [],
      currentPage: '',
      
      modals: [],
      isLoading: false,
      loadingMessage: undefined,
      
      isMobile: false,
      
      isSearchOpen: false,
      searchQuery: '',
      
      isNotificationPanelOpen: false,

      // Actions
      setTheme: (theme: Theme) => {
        set({ theme });
        
        // Apply theme to document
        const root = document.documentElement;
        if (theme === 'dark') {
          root.classList.add('dark');
        } else if (theme === 'light') {
          root.classList.remove('dark');
        } else {
          // System theme
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          if (prefersDark) {
            root.classList.add('dark');
          } else {
            root.classList.remove('dark');
          }
        }
      },

      setLanguage: (language: Language) => {
        set({ language });
        
        // Update document language
        document.documentElement.lang = language;
      },

      toggleSidebar: () => {
        set((state) => ({
          sidebar: {
            ...state.sidebar,
            isOpen: !state.sidebar.isOpen,
          },
        }));
      },

      setSidebarOpen: (isOpen: boolean) => {
        set((state) => ({
          sidebar: {
            ...state.sidebar,
            isOpen,
          },
        }));
      },

      setSidebarVariant: (variant: Sidebar['variant']) => {
        set((state) => ({
          sidebar: {
            ...state.sidebar,
            variant,
          },
        }));
      },

      setHeaderVisible: (visible: boolean) => {
        set({ isHeaderVisible: visible });
      },

      setFooterVisible: (visible: boolean) => {
        set({ isFooterVisible: visible });
      },

      setBreadcrumbs: (breadcrumbs: Breadcrumb[]) => {
        set({ breadcrumbs });
      },

      setCurrentPage: (page: string) => {
        set({ currentPage: page });
      },

      openModal: (modal: Omit<Modal, 'id'>) => {
        const id = crypto.randomUUID();
        const newModal: Modal = {
          ...modal,
          id,
          size: modal.size || 'md',
          closable: modal.closable !== false,
        };

        set((state) => ({
          modals: [...state.modals, newModal],
        }));

        return id;
      },

      closeModal: (id: string) => {
        set((state) => ({
          modals: state.modals.filter(modal => modal.id !== id),
        }));
      },

      closeAllModals: () => {
        set({ modals: [] });
      },

      setLoading: (isLoading: boolean, message?: string) => {
        set({
          isLoading,
          loadingMessage: isLoading ? message : undefined,
        });
      },

      setMobile: (isMobile: boolean) => {
        set({ isMobile });
        
        // Auto-close sidebar on mobile
        if (isMobile) {
          set((state) => ({
            sidebar: {
              ...state.sidebar,
              isOpen: false,
              variant: 'overlay',
            },
          }));
        }
      },

      setSearchOpen: (isOpen: boolean) => {
        set({ isSearchOpen: isOpen });
        
        // Clear search query when closing
        if (!isOpen) {
          set({ searchQuery: '' });
        }
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query });
      },

      setNotificationPanelOpen: (isOpen: boolean) => {
        set({ isNotificationPanelOpen: isOpen });
      },
    }),
    {
      name: 'ui-storage',
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        sidebar: state.sidebar,
      }),
    }
  )
);

// Initialize theme on store creation
const initializeTheme = () => {
  const { theme, setTheme } = useUIStore.getState();
  setTheme(theme);
};

// Initialize mobile detection
const initializeMobileDetection = () => {
  const { setMobile } = useUIStore.getState();
  
  const checkMobile = () => {
    setMobile(window.innerWidth < 768);
  };
  
  checkMobile();
  window.addEventListener('resize', checkMobile);
  
  return () => window.removeEventListener('resize', checkMobile);
};

// Initialize system theme detection
const initializeSystemThemeDetection = () => {
  const { theme, setTheme } = useUIStore.getState();
  
  if (theme === 'system') {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      if (useUIStore.getState().theme === 'system') {
        setTheme('system'); // This will trigger the theme application logic
      }
    };
    
    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }
};

// Auto-initialize when the store is created
if (typeof window !== 'undefined') {
  initializeTheme();
  initializeMobileDetection();
  initializeSystemThemeDetection();
}
