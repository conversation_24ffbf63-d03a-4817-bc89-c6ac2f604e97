import axios, { type AxiosError, type AxiosResponse } from 'axios'
import type { ApiError } from '../types/api'

// Configuration de base d'Axios
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5280/api'

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
})

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Intercepteur pour gérer les réponses et les erreurs
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as any

    // Si l'erreur est 401 et qu'on n'a pas déjà essayé de refresh
    if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem('refreshToken')
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken,
          })

          const { token, refreshToken: newRefreshToken } = response.data
          localStorage.setItem('token', token)
          localStorage.setItem('refreshToken', newRefreshToken)

          // Retry la requête originale avec le nouveau token
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${token}`
          }
          return api(originalRequest)
        }
      } catch (refreshError) {
        // Si le refresh échoue, déconnecter l'utilisateur
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }

    // Transformer l'erreur Axios en ApiError
    const apiError: ApiError = {
      message: (error.response?.data as any)?.message || error.message || 'Une erreur est survenue',
      statusCode: error.response?.status || 500,
      errors: (error.response?.data as any)?.errors,
      traceId: (error.response?.data as any)?.traceId,
    }

    return Promise.reject(apiError)
  }
)

// Fonction utilitaire pour gérer les erreurs
export const handleApiError = (error: unknown): ApiError => {
  if (error && typeof error === 'object' && 'message' in error) {
    return error as ApiError
  }

  return {
    message: 'Une erreur inattendue est survenue',
    statusCode: 500,
  }
}

// Types pour les méthodes HTTP
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// Fonction générique pour les appels API
export const apiCall = async <T = any>(
  method: HttpMethod,
  url: string,
  data?: any,
  config?: any
): Promise<T> => {
  try {
    const response = await api.request<T>({
      method,
      url,
      data,
      ...config,
    })
    return response.data
  } catch (error) {
    throw handleApiError(error)
  }
}

// Méthodes de convenance
export const get = <T = any>(url: string, config?: any): Promise<T> =>
  apiCall<T>('GET', url, undefined, config)

export const post = <T = any>(url: string, data?: any, config?: any): Promise<T> =>
  apiCall<T>('POST', url, data, config)

export const put = <T = any>(url: string, data?: any, config?: any): Promise<T> =>
  apiCall<T>('PUT', url, data, config)

export const del = <T = any>(url: string, config?: any): Promise<T> =>
  apiCall<T>('DELETE', url, undefined, config)

export const patch = <T = any>(url: string, data?: any, config?: any): Promise<T> =>
  apiCall<T>('PATCH', url, data, config)

export default api
