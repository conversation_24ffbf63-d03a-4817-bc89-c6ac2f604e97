using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Infrastructure.Data;

namespace ServiceLink.Infrastructure.Repositories;

/// <summary>
/// Implémentation du repository pour les catégories de service
/// </summary>
public class ServiceCategoryRepository : Repository<ServiceCategory>, IServiceCategoryRepository
{
    public ServiceCategoryRepository(ServiceLinkDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient les catégories racines
    /// </summary>
    public async Task<IEnumerable<ServiceCategory>> GetRootCategoriesAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<ServiceCategory>()
            .Where(c => c.ParentCategoryId == null && c.IsActive)
            .Include(c => c.SubCategories.Where(sc => sc.IsActive))
            .OrderBy(c => c.DisplayOrder)
            .ThenBy(c => c.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les sous-catégories d'une catégorie
    /// </summary>
    public async Task<IEnumerable<ServiceCategory>> GetSubCategoriesAsync(Guid parentCategoryId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ServiceCategory>()
            .Where(c => c.ParentCategoryId == parentCategoryId && c.IsActive)
            .Include(c => c.SubCategories.Where(sc => sc.IsActive))
            .OrderBy(c => c.DisplayOrder)
            .ThenBy(c => c.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient une catégorie par slug
    /// </summary>
    public async Task<ServiceCategory?> GetBySlugAsync(string slug, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ServiceCategory>()
            .Include(c => c.ParentCategory)
            .Include(c => c.SubCategories.Where(sc => sc.IsActive))
            .Include(c => c.Services.Where(s => s.IsActive))
            .FirstOrDefaultAsync(c => c.Slug == slug && c.IsActive, cancellationToken);
    }

    /// <summary>
    /// Obtient les catégories mises en avant
    /// </summary>
    public async Task<IEnumerable<ServiceCategory>> GetFeaturedCategoriesAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<ServiceCategory>()
            .Where(c => c.IsFeatured && c.IsActive)
            .Include(c => c.SubCategories.Where(sc => sc.IsActive))
            .OrderBy(c => c.DisplayOrder)
            .ThenBy(c => c.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient une catégorie avec toutes ses relations
    /// </summary>
    public override async Task<ServiceCategory?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ServiceCategory>()
            .Include(c => c.ParentCategory)
            .Include(c => c.SubCategories.Where(sc => sc.IsActive))
            .Include(c => c.Services.Where(s => s.IsActive))
            .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }
}


