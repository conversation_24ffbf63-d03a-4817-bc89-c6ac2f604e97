import React from 'react'
import { <PERSON>u, X, User, <PERSON>tings, Shield, LogOut, Bell } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { getInitials } from '../../lib/utils'

interface HeaderProps {
  onProfileClick?: () => void
  onAdminClick?: () => void
}

export const Header: React.FC<HeaderProps> = ({ onProfileClick, onAdminClick }) => {
  const { user, logout } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)
  const [isProfileMenuOpen, setIsProfileMenuOpen] = React.useState(false)

  const handleLogout = async () => {
    await logout()
    setIsProfileMenuOpen(false)
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo et titre */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <h1 className="text-xl font-bold text-blue-600">ServiceLink</h1>
            </div>
            
            {/* Navigation desktop */}
            <nav className="hidden md:ml-8 md:flex md:space-x-8">
              <a
                href="/dashboard"
                className="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                Dashboard
              </a>
              <a
                href="/services"
                className="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                Services
              </a>
              <a
                href="/reservations"
                className="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                Réservations
              </a>
            </nav>
          </div>

          {/* Actions utilisateur */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <Bell className="h-5 w-5" />
            </button>

            {/* Menu profil */}
            <div className="relative">
              <button
                onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {user ? getInitials(user.firstName, user.lastName) : 'U'}
                </div>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium text-gray-900">
                    {user ? `${user.firstName} ${user.lastName}` : 'Utilisateur'}
                  </div>
                  <div className="text-xs text-gray-500">{user?.role}</div>
                </div>
              </button>

              {/* Menu déroulant profil */}
              {isProfileMenuOpen && (
                <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                  <div className="px-4 py-3 border-b border-gray-200">
                    <div className="text-sm font-medium text-gray-900">
                      {user ? `${user.firstName} ${user.lastName}` : 'Utilisateur'}
                    </div>
                    <div className="text-sm text-gray-500">{user?.email}</div>
                  </div>
                  
                  <button
                    onClick={() => {
                      onProfileClick?.()
                      setIsProfileMenuOpen(false)
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <User className="h-4 w-4 mr-3" />
                    Mon profil
                  </button>
                  
                  <button
                    onClick={() => {
                      // TODO: Ouvrir paramètres
                      setIsProfileMenuOpen(false)
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <Settings className="h-4 w-4 mr-3" />
                    Paramètres
                  </button>
                  
                  {user?.role === 'Admin' && (
                    <button
                      onClick={() => {
                        onAdminClick?.()
                        setIsProfileMenuOpen(false)
                      }}
                      className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      <Shield className="h-4 w-4 mr-3" />
                      Administration
                    </button>
                  )}
                  
                  <div className="border-t border-gray-200 mt-1 pt-1">
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                    >
                      <LogOut className="h-4 w-4 mr-3" />
                      Se déconnecter
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Menu mobile */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>

        {/* Menu mobile */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <nav className="space-y-2">
              <a
                href="/dashboard"
                className="block px-3 py-2 text-base font-medium text-gray-900 hover:text-blue-600 transition-colors"
              >
                Dashboard
              </a>
              <a
                href="/services"
                className="block px-3 py-2 text-base font-medium text-gray-500 hover:text-blue-600 transition-colors"
              >
                Services
              </a>
              <a
                href="/reservations"
                className="block px-3 py-2 text-base font-medium text-gray-500 hover:text-blue-600 transition-colors"
              >
                Réservations
              </a>
            </nav>
          </div>
        )}
      </div>

      {/* Overlay pour fermer les menus */}
      {(isProfileMenuOpen || isMenuOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setIsProfileMenuOpen(false)
            setIsMenuOpen(false)
          }}
        />
      )}
    </header>
  )
}
