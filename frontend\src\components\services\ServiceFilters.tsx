import React, { useEffect, useState } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useServiceStore } from '@/stores';
import type { SearchFilters } from '@/stores/serviceStore';

export const ServiceFilters: React.FC = () => {
  const { 
    categories, 
    searchFilters, 
    updateSearchFilters, 
    searchServices, 
    loadCategories,
    clearSearchResults 
  } = useServiceStore();

  const [localFilters, setLocalFilters] = useState<SearchFilters>(searchFilters);
  const [showAdvanced, setShowAdvanced] = useState(false);

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  useEffect(() => {
    setLocalFilters(searchFilters);
  }, [searchFilters]);

  const handleSearch = () => {
    updateSearchFilters(localFilters);
    searchServices(localFilters, 1, 20);
  };

  const handleClearFilters = () => {
    const emptyFilters: SearchFilters = {};
    setLocalFilters(emptyFilters);
    updateSearchFilters(emptyFilters);
    clearSearchResults();
    searchServices(emptyFilters, 1, 20);
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const hasActiveFilters = Object.keys(localFilters).some(key => {
    const value = localFilters[key as keyof SearchFilters];
    return value !== undefined && value !== null && value !== '';
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filter Services</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="text-muted-foreground"
              >
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              {showAdvanced ? 'Simple' : 'Advanced'}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search Term */}
        <div className="flex space-x-2">
          <div className="flex-1">
            <Label htmlFor="search">Search Services</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Enter service name or keywords..."
                value={localFilters.searchTerm || ''}
                onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex items-end">
            <Button onClick={handleSearch}>
              Search
            </Button>
          </div>
        </div>

        {/* Category Filter */}
        <div>
          <Label htmlFor="category">Category</Label>
          <Select
            value={localFilters.categoryId || ''}
            onValueChange={(value) => handleFilterChange('categoryId', value || undefined)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="space-y-4 pt-4 border-t border-border">
            {/* Price Range */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="minPrice">Min Price (€)</Label>
                <Input
                  id="minPrice"
                  type="number"
                  placeholder="0"
                  value={localFilters.priceRange?.min || ''}
                  onChange={(e) => {
                    const value = e.target.value ? parseFloat(e.target.value) : undefined;
                    handleFilterChange('priceRange', {
                      ...localFilters.priceRange,
                      min: value
                    });
                  }}
                />
              </div>
              <div>
                <Label htmlFor="maxPrice">Max Price (€)</Label>
                <Input
                  id="maxPrice"
                  type="number"
                  placeholder="1000"
                  value={localFilters.priceRange?.max || ''}
                  onChange={(e) => {
                    const value = e.target.value ? parseFloat(e.target.value) : undefined;
                    handleFilterChange('priceRange', {
                      ...localFilters.priceRange,
                      max: value
                    });
                  }}
                />
              </div>
            </div>

            {/* Rating Filter */}
            <div>
              <Label htmlFor="rating">Minimum Rating</Label>
              <Select
                value={localFilters.rating?.toString() || ''}
                onValueChange={(value) => handleFilterChange('rating', value ? parseFloat(value) : undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Any rating" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any rating</SelectItem>
                  <SelectItem value="4.5">4.5+ stars</SelectItem>
                  <SelectItem value="4.0">4.0+ stars</SelectItem>
                  <SelectItem value="3.5">3.5+ stars</SelectItem>
                  <SelectItem value="3.0">3.0+ stars</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort Options */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sortBy">Sort By</Label>
                <Select
                  value={localFilters.sortBy || ''}
                  onValueChange={(value) => handleFilterChange('sortBy', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Relevance" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Relevance</SelectItem>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="price">Price</SelectItem>
                    <SelectItem value="rating">Rating</SelectItem>
                    <SelectItem value="distance">Distance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="sortDirection">Order</Label>
                <Select
                  value={localFilters.sortDirection || 'asc'}
                  onValueChange={(value) => handleFilterChange('sortDirection', value as 'asc' | 'desc')}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Ascending</SelectItem>
                    <SelectItem value="desc">Descending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-4 border-t border-border">
            {localFilters.searchTerm && (
              <Badge variant="secondary">
                Search: {localFilters.searchTerm}
              </Badge>
            )}
            {localFilters.categoryId && (
              <Badge variant="secondary">
                Category: {categories.find(c => c.id === localFilters.categoryId)?.name}
              </Badge>
            )}
            {localFilters.priceRange && (
              <Badge variant="secondary">
                Price: €{localFilters.priceRange.min || 0} - €{localFilters.priceRange.max || '∞'}
              </Badge>
            )}
            {localFilters.rating && (
              <Badge variant="secondary">
                Rating: {localFilters.rating}+ stars
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
