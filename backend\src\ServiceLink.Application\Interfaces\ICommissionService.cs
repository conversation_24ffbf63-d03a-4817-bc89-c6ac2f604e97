using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.Interfaces;

/// <summary>
/// Service pour le calcul automatique des commissions
/// </summary>
public interface ICommissionService
{
    /// <summary>
    /// Calcule la commission pour un paiement
    /// </summary>
    /// <param name="request">Détails du calcul de commission</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du calcul de commission</returns>
    Task<CommissionCalculationResult> CalculateCommissionAsync(CommissionCalculationRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les taux de commission pour un provider
    /// </summary>
    /// <param name="provider">Provider de paiement</param>
    /// <param name="serviceCategory">Catégorie de service</param>
    /// <returns>Configuration des taux</returns>
    CommissionRateConfiguration GetCommissionRates(PaymentProvider provider, string serviceCategory);

    /// <summary>
    /// Calcule la commission de la plateforme
    /// </summary>
    /// <param name="amount">Montant de base</param>
    /// <param name="serviceCategory">Catégorie de service</param>
    /// <param name="userTier">Niveau de l'utilisateur</param>
    /// <returns>Commission de la plateforme</returns>
    long CalculatePlatformCommission(long amount, string serviceCategory, UserTier userTier);

    /// <summary>
    /// Calcule les frais de traitement du provider
    /// </summary>
    /// <param name="amount">Montant de base</param>
    /// <param name="provider">Provider de paiement</param>
    /// <param name="paymentMethod">Méthode de paiement</param>
    /// <returns>Frais de traitement</returns>
    long CalculateProcessingFees(long amount, PaymentProvider provider, PaymentMethodType paymentMethod);

    /// <summary>
    /// Obtient l'historique des commissions pour un utilisateur
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="startDate">Date de début</param>
    /// <param name="endDate">Date de fin</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Historique des commissions</returns>
    Task<IEnumerable<CommissionHistoryDto>> GetCommissionHistoryAsync(Guid userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Traite le paiement des commissions
    /// </summary>
    /// <param name="commissionPayoutRequest">Demande de paiement</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat du paiement</returns>
    Task<CommissionPayoutResult> ProcessCommissionPayoutAsync(CommissionPayoutRequest commissionPayoutRequest, CancellationToken cancellationToken = default);
}

/// <summary>
/// Demande de calcul de commission
/// </summary>
public class CommissionCalculationRequest
{
    /// <summary>
    /// Montant de base en centimes
    /// </summary>
    public long BaseAmount { get; set; }

    /// <summary>
    /// Provider de paiement utilisé
    /// </summary>
    public PaymentProvider PaymentProvider { get; set; }

    /// <summary>
    /// Méthode de paiement utilisée
    /// </summary>
    public PaymentMethodType PaymentMethod { get; set; }

    /// <summary>
    /// Catégorie de service
    /// </summary>
    public string ServiceCategory { get; set; } = string.Empty;

    /// <summary>
    /// ID du prestataire de service
    /// </summary>
    public Guid ServiceProviderId { get; set; }

    /// <summary>
    /// Niveau du prestataire
    /// </summary>
    public UserTier ProviderTier { get; set; }

    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// Devise de la transaction
    /// </summary>
    public string Currency { get; set; } = "EUR";

    /// <summary>
    /// Métadonnées additionnelles
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Résultat du calcul de commission
/// </summary>
public class CommissionCalculationResult
{
    /// <summary>
    /// Montant de base
    /// </summary>
    public long BaseAmount { get; set; }

    /// <summary>
    /// Commission de la plateforme
    /// </summary>
    public long PlatformCommission { get; set; }

    /// <summary>
    /// Frais de traitement du provider
    /// </summary>
    public long ProcessingFees { get; set; }

    /// <summary>
    /// Montant net pour le prestataire
    /// </summary>
    public long NetAmount { get; set; }

    /// <summary>
    /// Montant total à facturer au client
    /// </summary>
    public long TotalAmount { get; set; }

    /// <summary>
    /// Taux de commission appliqué (en pourcentage)
    /// </summary>
    public decimal CommissionRate { get; set; }

    /// <summary>
    /// Taux des frais de traitement (en pourcentage)
    /// </summary>
    public decimal ProcessingFeeRate { get; set; }

    /// <summary>
    /// Détails du calcul
    /// </summary>
    public CommissionBreakdown Breakdown { get; set; } = new();

    /// <summary>
    /// Devise
    /// </summary>
    public string Currency { get; set; } = "EUR";
}

/// <summary>
/// Détail du calcul de commission
/// </summary>
public class CommissionBreakdown
{
    /// <summary>
    /// Commission de base de la plateforme
    /// </summary>
    public long BasePlatformCommission { get; set; }

    /// <summary>
    /// Réduction pour niveau utilisateur
    /// </summary>
    public long TierDiscount { get; set; }

    /// <summary>
    /// Frais fixes du provider
    /// </summary>
    public long FixedProcessingFee { get; set; }

    /// <summary>
    /// Frais variables du provider
    /// </summary>
    public long VariableProcessingFee { get; set; }

    /// <summary>
    /// Taxes applicables
    /// </summary>
    public long ApplicableTaxes { get; set; }

    /// <summary>
    /// Détails par étape de calcul
    /// </summary>
    public List<CalculationStep> Steps { get; set; } = new();
}

/// <summary>
/// Étape de calcul
/// </summary>
public class CalculationStep
{
    /// <summary>
    /// Description de l'étape
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Montant avant cette étape
    /// </summary>
    public long AmountBefore { get; set; }

    /// <summary>
    /// Montant de l'ajustement
    /// </summary>
    public long Adjustment { get; set; }

    /// <summary>
    /// Montant après cette étape
    /// </summary>
    public long AmountAfter { get; set; }

    /// <summary>
    /// Taux appliqué (si applicable)
    /// </summary>
    public decimal? Rate { get; set; }
}

/// <summary>
/// Configuration des taux de commission
/// </summary>
public class CommissionRateConfiguration
{
    /// <summary>
    /// Taux de commission de base (en pourcentage)
    /// </summary>
    public decimal BaseCommissionRate { get; set; }

    /// <summary>
    /// Frais fixes en centimes
    /// </summary>
    public long FixedFee { get; set; }

    /// <summary>
    /// Taux de frais de traitement (en pourcentage)
    /// </summary>
    public decimal ProcessingFeeRate { get; set; }

    /// <summary>
    /// Frais fixes de traitement en centimes
    /// </summary>
    public long FixedProcessingFee { get; set; }

    /// <summary>
    /// Réductions par niveau utilisateur
    /// </summary>
    public Dictionary<UserTier, decimal> TierDiscounts { get; set; } = new();

    /// <summary>
    /// Montant minimum de commission
    /// </summary>
    public long MinimumCommission { get; set; }

    /// <summary>
    /// Montant maximum de commission
    /// </summary>
    public long MaximumCommission { get; set; }
}

/// <summary>
/// Niveaux d'utilisateur pour les réductions
/// </summary>
public enum UserTier
{
    /// <summary>
    /// Nouveau utilisateur
    /// </summary>
    Newcomer = 1,

    /// <summary>
    /// Utilisateur standard
    /// </summary>
    Standard = 2,

    /// <summary>
    /// Utilisateur premium
    /// </summary>
    Premium = 3,

    /// <summary>
    /// Utilisateur VIP
    /// </summary>
    VIP = 4,

    /// <summary>
    /// Partenaire
    /// </summary>
    Partner = 5
}

/// <summary>
/// DTO pour l'historique des commissions
/// </summary>
public class CommissionHistoryDto
{
    /// <summary>
    /// ID de la commission
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// ID de la réservation
    /// </summary>
    public Guid BookingId { get; set; }

    /// <summary>
    /// ID du paiement
    /// </summary>
    public string PaymentId { get; set; } = string.Empty;

    /// <summary>
    /// Montant de base
    /// </summary>
    public long BaseAmount { get; set; }

    /// <summary>
    /// Commission de la plateforme
    /// </summary>
    public long PlatformCommission { get; set; }

    /// <summary>
    /// Frais de traitement
    /// </summary>
    public long ProcessingFees { get; set; }

    /// <summary>
    /// Montant net
    /// </summary>
    public long NetAmount { get; set; }

    /// <summary>
    /// Statut de la commission
    /// </summary>
    public CommissionStatus Status { get; set; }

    /// <summary>
    /// Date de création
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date de paiement
    /// </summary>
    public DateTime? PaidAt { get; set; }

    /// <summary>
    /// Provider de paiement
    /// </summary>
    public PaymentProvider PaymentProvider { get; set; }

    /// <summary>
    /// Devise
    /// </summary>
    public string Currency { get; set; } = "EUR";
}

/// <summary>
/// Statuts des commissions
/// </summary>
public enum CommissionStatus
{
    /// <summary>
    /// En attente
    /// </summary>
    Pending = 1,

    /// <summary>
    /// Calculée
    /// </summary>
    Calculated = 2,

    /// <summary>
    /// Approuvée
    /// </summary>
    Approved = 3,

    /// <summary>
    /// Payée
    /// </summary>
    Paid = 4,

    /// <summary>
    /// Annulée
    /// </summary>
    Cancelled = 5,

    /// <summary>
    /// En litige
    /// </summary>
    Disputed = 6
}

/// <summary>
/// Demande de paiement de commission
/// </summary>
public class CommissionPayoutRequest
{
    /// <summary>
    /// ID du prestataire
    /// </summary>
    public Guid ServiceProviderId { get; set; }

    /// <summary>
    /// Montant à payer
    /// </summary>
    public long Amount { get; set; }

    /// <summary>
    /// Devise
    /// </summary>
    public string Currency { get; set; } = "EUR";

    /// <summary>
    /// IDs des commissions à payer
    /// </summary>
    public List<Guid> CommissionIds { get; set; } = new();

    /// <summary>
    /// Méthode de paiement préférée
    /// </summary>
    public PayoutMethod PayoutMethod { get; set; }

    /// <summary>
    /// Informations bancaires
    /// </summary>
    public BankAccountInfo? BankAccount { get; set; }
}

/// <summary>
/// Méthodes de paiement des commissions
/// </summary>
public enum PayoutMethod
{
    /// <summary>
    /// Virement bancaire
    /// </summary>
    BankTransfer = 1,

    /// <summary>
    /// PayPal
    /// </summary>
    PayPal = 2,

    /// <summary>
    /// Mobile Money
    /// </summary>
    MobileMoney = 3,

    /// <summary>
    /// Crypto-monnaie
    /// </summary>
    Crypto = 4
}

/// <summary>
/// Informations bancaires
/// </summary>
public class BankAccountInfo
{
    /// <summary>
    /// Nom du titulaire
    /// </summary>
    public string AccountHolderName { get; set; } = string.Empty;

    /// <summary>
    /// Numéro de compte
    /// </summary>
    public string AccountNumber { get; set; } = string.Empty;

    /// <summary>
    /// Code IBAN
    /// </summary>
    public string IBAN { get; set; } = string.Empty;

    /// <summary>
    /// Code BIC/SWIFT
    /// </summary>
    public string BIC { get; set; } = string.Empty;

    /// <summary>
    /// Nom de la banque
    /// </summary>
    public string BankName { get; set; } = string.Empty;
}

/// <summary>
/// Résultat du paiement de commission
/// </summary>
public class CommissionPayoutResult
{
    /// <summary>
    /// Succès du paiement
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// ID de la transaction de paiement
    /// </summary>
    public string PayoutTransactionId { get; set; } = string.Empty;

    /// <summary>
    /// Montant payé
    /// </summary>
    public long AmountPaid { get; set; }

    /// <summary>
    /// Frais de paiement
    /// </summary>
    public long PayoutFees { get; set; }

    /// <summary>
    /// Montant net reçu
    /// </summary>
    public long NetAmountReceived { get; set; }

    /// <summary>
    /// Date de traitement
    /// </summary>
    public DateTime ProcessedAt { get; set; }

    /// <summary>
    /// Date estimée de réception
    /// </summary>
    public DateTime EstimatedArrival { get; set; }

    /// <summary>
    /// Message d'erreur si échec
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// IDs des commissions traitées
    /// </summary>
    public List<Guid> ProcessedCommissionIds { get; set; } = new();
}
