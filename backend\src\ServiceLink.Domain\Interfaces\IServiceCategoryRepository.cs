using ServiceLink.Domain.Entities;

namespace ServiceLink.Domain.Interfaces;

/// <summary>
/// Interface pour le repository des catégories de service
/// </summary>
public interface IServiceCategoryRepository : IRepository<ServiceCategory>
{
    /// <summary>
    /// Obtient les catégories racines
    /// </summary>
    Task<IEnumerable<ServiceCategory>> GetRootCategoriesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les sous-catégories d'une catégorie
    /// </summary>
    Task<IEnumerable<ServiceCategory>> GetSubCategoriesAsync(Guid parentCategoryId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient une catégorie par slug
    /// </summary>
    Task<ServiceCategory?> GetBySlugAsync(string slug, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les catégories mises en avant
    /// </summary>
    Task<IEnumerable<ServiceCategory>> GetFeaturedCategoriesAsync(CancellationToken cancellationToken = default);
}
