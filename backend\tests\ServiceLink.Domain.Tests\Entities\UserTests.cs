using Bogus;
using FluentAssertions;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Events;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Domain.Tests.Entities;

/// <summary>
/// Tests unitaires pour l'entité User
/// </summary>
public class UserTests
{
    private readonly Faker _faker;

    public UserTests()
    {
        _faker = new Faker("fr");
    }

    [Fact]
    public void Constructor_WithValidData_ShouldCreateUser()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");
        var firstName = "John";
        var lastName = "Doe";
        var role = UserRole.Client;
        var phoneNumber = PhoneNumber.Create("+33123456789");
        var createdBy = Guid.NewGuid();

        // Act
        var user = new User(email, firstName, lastName, role, phoneNumber, createdBy);

        // Assert
        user.Should().NotBeNull();
        user.Id.Should().NotBeEmpty();
        user.Email.Should().Be(email);
        user.FirstName.Should().Be(firstName);
        user.LastName.Should().Be(lastName);
        user.FullName.Should().Be("John Doe");
        user.Role.Should().Be(role);
        user.PhoneNumber.Should().Be(phoneNumber);
        user.IsEmailConfirmed.Should().BeFalse();
        user.IsPhoneConfirmed.Should().BeFalse();
        user.IsActive.Should().BeTrue();
        user.CreatedBy.Should().Be(createdBy);
        user.ProfileCompletionPercentage.Should().BeGreaterThan(0);
    }

    [Fact]
    public void Constructor_ShouldAddUserCreatedEvent()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");
        var firstName = "John";
        var lastName = "Doe";
        var role = UserRole.Client;

        // Act
        var user = new User(email, firstName, lastName, role);

        // Assert
        user.DomainEvents.Should().HaveCount(1);
        var domainEvent = user.DomainEvents.First();
        domainEvent.Should().BeOfType<UserCreatedEvent>();
        
        var userCreatedEvent = (UserCreatedEvent)domainEvent;
        userCreatedEvent.UserId.Should().Be(user.Id);
        userCreatedEvent.Email.Should().Be(email.Value);
        userCreatedEvent.Role.Should().Be(role);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("A")]
    [InlineData(null)]
    public void Constructor_WithInvalidFirstName_ShouldThrowArgumentException(string invalidFirstName)
    {
        // Arrange
        var email = Email.Create("<EMAIL>");
        var lastName = "Doe";
        var role = UserRole.Client;

        // Act & Assert
        var act = () => new User(email, invalidFirstName, lastName, role);
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("A")]
    [InlineData(null)]
    public void Constructor_WithInvalidLastName_ShouldThrowArgumentException(string invalidLastName)
    {
        // Arrange
        var email = Email.Create("<EMAIL>");
        var firstName = "John";
        var role = UserRole.Client;

        // Act & Assert
        var act = () => new User(email, firstName, invalidLastName, role);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Constructor_WithNullEmail_ShouldThrowArgumentNullException()
    {
        // Arrange
        var firstName = "John";
        var lastName = "Doe";
        var role = UserRole.Client;

        // Act & Assert
        var act = () => new User(null!, firstName, lastName, role);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void UpdateBasicInfo_WithValidData_ShouldUpdateUserAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        var newFirstName = "Jane";
        var newLastName = "Smith";
        var newPhoneNumber = PhoneNumber.Create("+33987654321");
        var updatedBy = Guid.NewGuid();
        user.ClearDomainEvents(); // Clear creation event

        // Act
        user.UpdateBasicInfo(newFirstName, newLastName, newPhoneNumber, updatedBy);

        // Assert
        user.FirstName.Should().Be(newFirstName);
        user.LastName.Should().Be(newLastName);
        user.FullName.Should().Be("Jane Smith");
        user.PhoneNumber.Should().Be(newPhoneNumber);
        user.UpdatedBy.Should().Be(updatedBy);
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserUpdatedEvent>();
    }

    [Fact]
    public void SetPassword_WithValidData_ShouldSetPasswordAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        var passwordHash = "hashedPassword123";
        var passwordSalt = "salt123";
        var updatedBy = Guid.NewGuid();
        user.ClearDomainEvents();

        // Act
        user.SetPassword(passwordHash, passwordSalt, updatedBy);

        // Assert
        user.PasswordHash.Should().Be(passwordHash);
        user.PasswordSalt.Should().Be(passwordSalt);
        user.PasswordChangedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        user.FailedLoginAttempts.Should().Be(0);
        user.LockedUntil.Should().BeNull();
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserPasswordChangedEvent>();
    }

    [Theory]
    [InlineData("", "salt")]
    [InlineData(" ", "salt")]
    [InlineData(null, "salt")]
    [InlineData("hash", "")]
    [InlineData("hash", " ")]
    [InlineData("hash", null)]
    public void SetPassword_WithInvalidData_ShouldThrowArgumentException(string passwordHash, string passwordSalt)
    {
        // Arrange
        var user = CreateValidUser();

        // Act & Assert
        var act = () => user.SetPassword(passwordHash, passwordSalt);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ConfirmEmail_ShouldConfirmEmailAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        var confirmedBy = Guid.NewGuid();
        user.ClearDomainEvents();

        // Act
        user.ConfirmEmail(confirmedBy);

        // Assert
        user.IsEmailConfirmed.Should().BeTrue();
        user.EmailConfirmationToken.Should().BeNull();
        user.EmailConfirmationTokenExpiry.Should().BeNull();
        user.UpdatedBy.Should().Be(confirmedBy);
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserEmailConfirmedEvent>();
    }

    [Fact]
    public void ConfirmPhone_WithPhoneNumber_ShouldConfirmPhoneAndAddEvent()
    {
        // Arrange
        var phoneNumber = PhoneNumber.Create("+33123456789");
        var user = CreateValidUser(phoneNumber);
        var confirmedBy = Guid.NewGuid();
        user.ClearDomainEvents();

        // Act
        user.ConfirmPhone(confirmedBy);

        // Assert
        user.IsPhoneConfirmed.Should().BeTrue();
        user.UpdatedBy.Should().Be(confirmedBy);
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserPhoneConfirmedEvent>();
    }

    [Fact]
    public void ConfirmPhone_WithoutPhoneNumber_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var user = CreateValidUser(phoneNumber: null);

        // Act & Assert
        var act = () => user.ConfirmPhone();
        act.Should().Throw<InvalidOperationException>()
           .WithMessage("Aucun numéro de téléphone à confirmer.");
    }

    [Fact]
    public void RecordSuccessfulLogin_ShouldUpdateLoginInfoAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        user.ClearDomainEvents();

        // Act
        user.RecordSuccessfulLogin();

        // Assert
        user.LastLoginAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        user.FailedLoginAttempts.Should().Be(0);
        user.LockedUntil.Should().BeNull();
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserLoggedInEvent>();
    }

    [Fact]
    public void RecordFailedLogin_ShouldIncrementFailedAttempts()
    {
        // Arrange
        var user = CreateValidUser();
        user.ClearDomainEvents();

        // Act
        user.RecordFailedLogin();

        // Assert
        user.FailedLoginAttempts.Should().Be(1);
        user.DomainEvents.Should().BeEmpty(); // No event for single failed attempt
    }

    [Fact]
    public void RecordFailedLogin_After5Attempts_ShouldLockAccountAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        user.ClearDomainEvents();

        // Act - Record 5 failed attempts
        for (int i = 0; i < 5; i++)
        {
            user.RecordFailedLogin();
        }

        // Assert
        user.FailedLoginAttempts.Should().Be(5);
        user.LockedUntil.Should().BeCloseTo(DateTime.UtcNow.AddMinutes(30), TimeSpan.FromSeconds(1));
        user.IsLocked().Should().BeTrue();
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserAccountLockedEvent>();
    }

    [Fact]
    public void UnlockAccount_ShouldUnlockAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        // Lock the account first
        for (int i = 0; i < 5; i++)
        {
            user.RecordFailedLogin();
        }
        user.ClearDomainEvents();
        var unlockedBy = Guid.NewGuid();

        // Act
        user.UnlockAccount(unlockedBy);

        // Assert
        user.FailedLoginAttempts.Should().Be(0);
        user.LockedUntil.Should().BeNull();
        user.IsLocked().Should().BeFalse();
        user.UpdatedBy.Should().Be(unlockedBy);
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserAccountUnlockedEvent>();
    }

    [Fact]
    public void EnableTwoFactor_WithValidData_ShouldEnableTwoFactorAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        var secret = "JBSWY3DPEHPK3PXP";
        var recoveryCodes = "code1,code2,code3";
        var enabledBy = Guid.NewGuid();
        user.ClearDomainEvents();

        // Act
        user.EnableTwoFactor(secret, recoveryCodes, enabledBy);

        // Assert
        user.TwoFactorSecret.Should().Be(secret);
        user.TwoFactorRecoveryCodes.Should().Be(recoveryCodes);
        user.IsTwoFactorEnabled.Should().BeTrue();
        user.UpdatedBy.Should().Be(enabledBy);
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserTwoFactorEnabledEvent>();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void EnableTwoFactor_WithInvalidSecret_ShouldThrowArgumentException(string invalidSecret)
    {
        // Arrange
        var user = CreateValidUser();
        var recoveryCodes = "code1,code2,code3";

        // Act & Assert
        var act = () => user.EnableTwoFactor(invalidSecret, recoveryCodes);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void DisableTwoFactor_ShouldDisableTwoFactorAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        user.EnableTwoFactor("JBSWY3DPEHPK3PXP", "codes");
        user.ClearDomainEvents();
        var disabledBy = Guid.NewGuid();

        // Act
        user.DisableTwoFactor(disabledBy);

        // Assert
        user.TwoFactorSecret.Should().BeNull();
        user.TwoFactorRecoveryCodes.Should().BeNull();
        user.IsTwoFactorEnabled.Should().BeFalse();
        user.UpdatedBy.Should().Be(disabledBy);
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserTwoFactorDisabledEvent>();
    }

    [Fact]
    public void Deactivate_ShouldDeactivateUserAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        var deactivatedBy = Guid.NewGuid();
        user.ClearDomainEvents();

        // Act
        user.Deactivate(deactivatedBy);

        // Assert
        user.IsActive.Should().BeFalse();
        user.UpdatedBy.Should().Be(deactivatedBy);
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserDeactivatedEvent>();
    }

    [Fact]
    public void Activate_ShouldActivateUserAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        user.Deactivate();
        user.ClearDomainEvents();
        var activatedBy = Guid.NewGuid();

        // Act
        user.Activate(activatedBy);

        // Assert
        user.IsActive.Should().BeTrue();
        user.UpdatedBy.Should().Be(activatedBy);
        
        user.DomainEvents.Should().HaveCount(1);
        user.DomainEvents.First().Should().BeOfType<UserActivatedEvent>();
    }

    [Fact]
    public void ChangeRole_ShouldChangeRoleAndAddEvent()
    {
        // Arrange
        var user = CreateValidUser();
        var oldRole = user.Role;
        var newRole = UserRole.Provider;
        var changedBy = Guid.NewGuid();
        user.ClearDomainEvents();

        // Act
        user.ChangeRole(newRole, changedBy);

        // Assert
        user.Role.Should().Be(newRole);
        user.UpdatedBy.Should().Be(changedBy);
        
        user.DomainEvents.Should().HaveCount(1);
        var roleChangedEvent = (UserRoleChangedEvent)user.DomainEvents.First();
        roleChangedEvent.OldRole.Should().Be(oldRole);
        roleChangedEvent.NewRole.Should().Be(newRole);
    }

    [Fact]
    public void HasPermission_ShouldReturnCorrectResult()
    {
        // Arrange
        var clientUser = CreateValidUser(role: UserRole.Client);
        var adminUser = CreateValidUser(role: UserRole.Admin);

        // Act & Assert
        clientUser.HasPermission("search_providers").Should().BeTrue();
        clientUser.HasPermission("manage_all_users").Should().BeFalse();
        
        adminUser.HasPermission("search_providers").Should().BeTrue();
        adminUser.HasPermission("manage_all_users").Should().BeTrue();
    }

    [Fact]
    public void CanAccess_ShouldReturnCorrectResult()
    {
        // Arrange
        var user = CreateValidUser();
        var otherUserId = Guid.NewGuid();
        var adminUser = CreateValidUser(role: UserRole.Admin);

        // Act & Assert
        user.CanAccess(user.Id).Should().BeTrue(); // Own resource
        user.CanAccess(otherUserId).Should().BeFalse(); // Other's resource
        
        adminUser.CanAccess(otherUserId).Should().BeTrue(); // Admin can access all
    }

    private User CreateValidUser(PhoneNumber? phoneNumber = null, UserRole role = UserRole.Client)
    {
        var email = Email.Create(_faker.Internet.Email());
        var firstName = _faker.Name.FirstName();
        var lastName = _faker.Name.LastName();
        
        return new User(email, firstName, lastName, role, phoneNumber);
    }
}
