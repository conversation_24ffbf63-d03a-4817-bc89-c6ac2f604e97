using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using ServiceLink.Infrastructure.Configuration;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Factory pour créer des instances de services de paiement
/// </summary>
public class PaymentServiceFactory : IPaymentServiceFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly ILogger<PaymentServiceFactory> _logger;
    private readonly PaymentSettings _settings;

    public PaymentServiceFactory(
        IServiceProvider serviceProvider,
        IConfiguration configuration,
        ILogger<PaymentServiceFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _logger = logger;
        _settings = configuration.GetSection("Payment").Get<PaymentSettings>() ?? new PaymentSettings();
    }

    /// <inheritdoc />
    public IPaymentService CreatePaymentService(PaymentProvider provider)
    {
        try
        {
            _logger.LogDebug("Création du service de paiement pour le provider {Provider}", provider);

            // Vérifier que le provider est activé
            if (!_settings.EnabledProviders.Contains(provider.ToString()))
            {
                throw new InvalidOperationException($"Le provider {provider} n'est pas activé dans la configuration");
            }

            IPaymentService service = provider switch
            {
                PaymentProvider.Stripe => _serviceProvider.GetRequiredService<StripePaymentService>(),
                PaymentProvider.PayPal => _serviceProvider.GetRequiredService<PayPalPaymentService>(),
                PaymentProvider.Flutterwave => _serviceProvider.GetRequiredService<FlutterwavePaymentService>(),
                _ => throw new ArgumentException($"Provider de paiement non supporté: {provider}")
            };

            _logger.LogInformation("Service de paiement {Provider} créé avec succès", provider);
            return service;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du service de paiement {Provider}", provider);
            throw;
        }
    }

    /// <inheritdoc />
    public IPaymentService GetDefaultPaymentService()
    {
        try
        {
            _logger.LogDebug("Récupération du service de paiement par défaut: {DefaultProvider}", _settings.DefaultProvider);

            if (!Enum.TryParse<PaymentProvider>(_settings.DefaultProvider, out var defaultProvider))
            {
                throw new InvalidOperationException($"Provider par défaut invalide: {_settings.DefaultProvider}");
            }

            return CreatePaymentService(defaultProvider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du service de paiement par défaut");
            throw;
        }
    }

    /// <inheritdoc />
    public IEnumerable<IPaymentService> GetAvailablePaymentServices()
    {
        try
        {
            _logger.LogDebug("Récupération de tous les services de paiement disponibles");

            var services = new List<IPaymentService>();

            foreach (var providerName in _settings.EnabledProviders)
            {
                if (Enum.TryParse<PaymentProvider>(providerName, out var provider))
                {
                    try
                    {
                        var service = CreatePaymentService(provider);
                        services.Add(service);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Impossible de créer le service pour le provider {Provider}", provider);
                    }
                }
                else
                {
                    _logger.LogWarning("Provider inconnu dans la configuration: {ProviderName}", providerName);
                }
            }

            _logger.LogInformation("Trouvé {Count} services de paiement disponibles", services.Count);
            return services;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des services de paiement disponibles");
            throw;
        }
    }

    /// <summary>
    /// Obtient le service de paiement optimal pour une devise donnée
    /// </summary>
    /// <param name="currency">Code de devise (EUR, USD, XOF, etc.)</param>
    /// <returns>Service de paiement optimal</returns>
    public IPaymentService GetOptimalPaymentServiceForCurrency(string currency)
    {
        try
        {
            _logger.LogDebug("Recherche du service optimal pour la devise {Currency}", currency);

            var provider = currency.ToUpperInvariant() switch
            {
                "XOF" or "NGN" or "GHS" or "KES" or "UGX" => PaymentProvider.Flutterwave, // Devises africaines
                "USD" or "EUR" or "GBP" => PaymentProvider.Stripe, // Devises internationales principales
                _ => GetDefaultProvider()
            };

            _logger.LogInformation("Provider optimal pour {Currency}: {Provider}", currency, provider);
            return CreatePaymentService(provider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la sélection du provider optimal pour {Currency}", currency);
            return GetDefaultPaymentService();
        }
    }

    /// <summary>
    /// Obtient le service de paiement optimal pour une méthode de paiement donnée
    /// </summary>
    /// <param name="paymentMethod">Méthode de paiement</param>
    /// <returns>Service de paiement optimal</returns>
    public IPaymentService GetOptimalPaymentServiceForMethod(PaymentMethodType paymentMethod)
    {
        try
        {
            _logger.LogDebug("Recherche du service optimal pour la méthode {PaymentMethod}", paymentMethod);

            var provider = paymentMethod switch
            {
                PaymentMethodType.MobileMoney => PaymentProvider.Flutterwave,
                PaymentMethodType.PayPal => PaymentProvider.PayPal,
                PaymentMethodType.Card => GetDefaultProvider(),
                PaymentMethodType.ApplePay or PaymentMethodType.GooglePay => PaymentProvider.Stripe,
                _ => GetDefaultProvider()
            };

            _logger.LogInformation("Provider optimal pour {PaymentMethod}: {Provider}", paymentMethod, provider);
            return CreatePaymentService(provider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la sélection du provider optimal pour {PaymentMethod}", paymentMethod);
            return GetDefaultPaymentService();
        }
    }

    /// <summary>
    /// Vérifie si un provider est disponible et configuré
    /// </summary>
    /// <param name="provider">Provider à vérifier</param>
    /// <returns>True si le provider est disponible</returns>
    public bool IsProviderAvailable(PaymentProvider provider)
    {
        try
        {
            return _settings.EnabledProviders.Contains(provider.ToString()) && 
                   IsProviderConfigured(provider);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Obtient les statistiques des providers de paiement
    /// </summary>
    /// <returns>Statistiques des providers</returns>
    public object GetProvidersStats()
    {
        var stats = new PaymentProvidersStats();

        foreach (var providerName in _settings.EnabledProviders)
        {
            if (Enum.TryParse<PaymentProvider>(providerName, out var provider))
            {
                var isAvailable = IsProviderAvailable(provider);
                var isConfigured = IsProviderConfigured(provider);

                stats.Providers.Add(new PaymentProviderInfo
                {
                    Provider = provider,
                    IsEnabled = true,
                    IsAvailable = isAvailable,
                    IsConfigured = isConfigured,
                    SupportedMethods = provider.GetSupportedMethods().ToList(),
                    Description = provider.GetDescription()
                });
            }
        }

        stats.TotalEnabled = stats.Providers.Count;
        stats.TotalAvailable = stats.Providers.Count(p => p.IsAvailable);
        stats.DefaultProvider = GetDefaultProvider();

        return stats;
    }

    #region Méthodes privées

    private PaymentProvider GetDefaultProvider()
    {
        if (Enum.TryParse<PaymentProvider>(_settings.DefaultProvider, out var provider))
        {
            return provider;
        }
        return PaymentProvider.Stripe; // Fallback
    }

    private bool IsProviderConfigured(PaymentProvider provider)
    {
        try
        {
            return provider switch
            {
                PaymentProvider.Stripe => IsStripeConfigured(),
                PaymentProvider.PayPal => IsPayPalConfigured(),
                PaymentProvider.Flutterwave => IsFlutterwaveConfigured(),
                _ => false
            };
        }
        catch
        {
            return false;
        }
    }

    private bool IsStripeConfigured()
    {
        var stripeSettings = _configuration.GetSection("Stripe").Get<StripeSettings>();
        return !string.IsNullOrEmpty(stripeSettings?.SecretKey) && 
               !string.IsNullOrEmpty(stripeSettings?.PublishableKey);
    }

    private bool IsPayPalConfigured()
    {
        var paypalSettings = _configuration.GetSection("PayPal").Get<PayPalSettings>();
        return !string.IsNullOrEmpty(paypalSettings?.ClientId) && 
               !string.IsNullOrEmpty(paypalSettings?.ClientSecret);
    }

    private bool IsFlutterwaveConfigured()
    {
        var flutterwaveSettings = _configuration.GetSection("Flutterwave").Get<FlutterwaveSettings>();
        return !string.IsNullOrEmpty(flutterwaveSettings?.SecretKey) && 
               !string.IsNullOrEmpty(flutterwaveSettings?.PublicKey);
    }

    #endregion
}

/// <summary>
/// Configuration générale pour les paiements
/// </summary>
public class PaymentSettings
{
    /// <summary>
    /// Provider par défaut
    /// </summary>
    public string DefaultProvider { get; set; } = "Stripe";

    /// <summary>
    /// Providers activés
    /// </summary>
    public List<string> EnabledProviders { get; set; } = new() { "Stripe", "PayPal", "Flutterwave" };

    /// <summary>
    /// Devise par défaut
    /// </summary>
    public string DefaultCurrency { get; set; } = "EUR";

    /// <summary>
    /// Devises supportées
    /// </summary>
    public List<string> SupportedCurrencies { get; set; } = new() { "EUR", "USD", "XOF", "NGN", "GHS" };

    /// <summary>
    /// Timeout pour les webhooks en secondes
    /// </summary>
    public int WebhookTimeout { get; set; } = 30;

    /// <summary>
    /// Nombre maximum de jours pour un remboursement
    /// </summary>
    public int MaxRefundDays { get; set; } = 30;

    /// <summary>
    /// Commission par défaut en pourcentage
    /// </summary>
    public decimal DefaultCommissionRate { get; set; } = 2.5m;

    /// <summary>
    /// Montant minimum pour un paiement en centimes
    /// </summary>
    public long MinimumAmount { get; set; } = 100; // 1.00 EUR

    /// <summary>
    /// Montant maximum pour un paiement en centimes
    /// </summary>
    public long MaximumAmount { get; set; } = 100000000; // 1,000,000.00 EUR
}

/// <summary>
/// Statistiques des providers de paiement
/// </summary>
public class PaymentProvidersStats
{
    /// <summary>
    /// Liste des providers
    /// </summary>
    public List<PaymentProviderInfo> Providers { get; set; } = new();

    /// <summary>
    /// Nombre total de providers activés
    /// </summary>
    public int TotalEnabled { get; set; }

    /// <summary>
    /// Nombre total de providers disponibles
    /// </summary>
    public int TotalAvailable { get; set; }

    /// <summary>
    /// Provider par défaut
    /// </summary>
    public PaymentProvider DefaultProvider { get; set; }
}

/// <summary>
/// Informations sur un provider de paiement
/// </summary>
public class PaymentProviderInfo
{
    /// <summary>
    /// Provider de paiement
    /// </summary>
    public PaymentProvider Provider { get; set; }

    /// <summary>
    /// Indique si le provider est activé
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// Indique si le provider est disponible
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Indique si le provider est configuré
    /// </summary>
    public bool IsConfigured { get; set; }

    /// <summary>
    /// Méthodes de paiement supportées
    /// </summary>
    public List<PaymentMethodType> SupportedMethods { get; set; } = new();

    /// <summary>
    /// Description du provider
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
