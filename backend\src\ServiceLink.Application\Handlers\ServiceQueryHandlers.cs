using MediatR;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Services.Queries;
using ServiceLink.Application.Common.Models;

namespace ServiceLink.Application.Handlers;

// Handler temporaire pour les catégories
public class GetServiceCategoriesQueryHandler : IRequestHandler<GetServiceCategoriesQuery, List<ServiceCategoryDto>>
{
    public async Task<List<ServiceCategoryDto>> Handle(GetServiceCategoriesQuery request, CancellationToken cancellationToken)
    {
        // Données mockées temporaires
        await Task.Delay(100, cancellationToken); // Simule la latence
        
        return new List<ServiceCategoryDto>
        {
            new() { Id = Guid.NewGuid(), Name = "Ménage", Description = "Services de ménage et nettoyage", Icon = "🏠", ServiceCount = 2500, IsActive = true, DisplayOrder = 1, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new() { Id = Guid.NewGuid(), Name = "Jardinage", Description = "Entretien de jardins et espaces verts", Icon = "🌱", ServiceCount = 1800, IsActive = true, DisplayOrder = 2, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new() { Id = Guid.NewGuid(), Name = "Bricolage", Description = "Petits travaux et réparations", Icon = "🔧", ServiceCount = 3200, IsActive = true, DisplayOrder = 3, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new() { Id = Guid.NewGuid(), Name = "Garde d'enfants", Description = "Services de garde et baby-sitting", Icon = "👶", ServiceCount = 1500, IsActive = true, DisplayOrder = 4, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new() { Id = Guid.NewGuid(), Name = "Cours particuliers", Description = "Soutien scolaire et formation", Icon = "📚", ServiceCount = 2100, IsActive = true, DisplayOrder = 5, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new() { Id = Guid.NewGuid(), Name = "Livraison", Description = "Services de livraison et courses", Icon = "📦", ServiceCount = 900, IsActive = true, DisplayOrder = 6, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
        };
    }
}

// Handler temporaire pour les services populaires
public class GetPopularServicesQueryHandler : IRequestHandler<GetPopularServicesQuery, List<ServiceDto>>
{
    public async Task<List<ServiceDto>> Handle(GetPopularServicesQuery request, CancellationToken cancellationToken)
    {
        await Task.Delay(100, cancellationToken);
        
        var services = new List<ServiceDto>();
        for (int i = 1; i <= request.Limit; i++)
        {
            services.Add(new ServiceDto
            {
                Id = Guid.NewGuid(),
                Name = $"Service Populaire {i}",
                Description = $"Description du service populaire {i}",
                ShortDescription = $"Service {i}",
                CategoryId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                BasePrice = 15 + (i * 5),
                PricingUnit = "Hourly",
                Currency = "EUR",
                Duration = 120,
                IsActive = true,
                IsUrgent = false,
                IsFeatured = true,
                Rating = 4.5m + (i * 0.1m),
                ReviewCount = 50 + (i * 20),
                BookingCount = 100 + (i * 30),
                Images = new List<string> { "/api/placeholder/400/300" },
                Tags = new List<string> { "Populaire", $"Tag{i}" },
                Location = new ServiceLocationDto
                {
                    Address = $"{i} rue de la Paix",
                    City = "Paris",
                    PostalCode = "75001",
                    Latitude = 48.8566,
                    Longitude = 2.3522,
                    ServiceRadius = 10
                },
                Provider = new ServiceProviderDto
                {
                    Id = Guid.NewGuid(),
                    FirstName = $"Prénom{i}",
                    LastName = $"Nom{i}",
                    Email = $"provider{i}@example.com",
                    Avatar = "/api/placeholder/80/80",
                    Rating = 4.5m + (i * 0.1m),
                    ReviewCount = 50 + (i * 20),
                    CompletedBookings = 100 + (i * 30),
                    ResponseTime = "< 2h",
                    IsVerified = true,
                    IsOnline = true,
                    JoinedAt = DateTime.UtcNow.AddMonths(-i),
                    Specialties = new List<string> { $"Spécialité {i}", "Professionnel" },
                    Languages = new List<string> { "Français" },
                    Certifications = new List<string> { "Certification professionnelle" }
                },
                CreatedAt = DateTime.UtcNow.AddDays(-i),
                UpdatedAt = DateTime.UtcNow
            });
        }
        
        return services;
    }
}

// Handler temporaire pour les services en vedette
public class GetFeaturedServicesQueryHandler : IRequestHandler<GetFeaturedServicesQuery, List<ServiceDto>>
{
    public async Task<List<ServiceDto>> Handle(GetFeaturedServicesQuery request, CancellationToken cancellationToken)
    {
        await Task.Delay(100, cancellationToken);
        
        var services = new List<ServiceDto>();
        for (int i = 1; i <= request.Limit; i++)
        {
            services.Add(new ServiceDto
            {
                Id = Guid.NewGuid(),
                Name = $"Service Vedette {i}",
                Description = $"Description du service en vedette {i}",
                ShortDescription = $"Vedette {i}",
                CategoryId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                BasePrice = 20 + (i * 3),
                PricingUnit = "Hourly",
                Currency = "EUR",
                Duration = 90,
                IsActive = true,
                IsUrgent = false,
                IsFeatured = true,
                Rating = 4.7m + (i * 0.05m),
                ReviewCount = 80 + (i * 15),
                BookingCount = 150 + (i * 25),
                Images = new List<string> { "/api/placeholder/400/300" },
                Tags = new List<string> { "Vedette", $"Premium{i}" },
                Location = new ServiceLocationDto
                {
                    Address = $"{i * 10} avenue des Champs",
                    City = "Paris",
                    PostalCode = "75008",
                    Latitude = 48.8566,
                    Longitude = 2.3522,
                    ServiceRadius = 15
                },
                Provider = new ServiceProviderDto
                {
                    Id = Guid.NewGuid(),
                    FirstName = $"Expert{i}",
                    LastName = $"Pro{i}",
                    Email = $"expert{i}@example.com",
                    Avatar = "/api/placeholder/80/80",
                    Rating = 4.7m + (i * 0.05m),
                    ReviewCount = 80 + (i * 15),
                    CompletedBookings = 150 + (i * 25),
                    ResponseTime = "< 1h",
                    IsVerified = true,
                    IsOnline = true,
                    JoinedAt = DateTime.UtcNow.AddMonths(-i * 2),
                    Specialties = new List<string> { $"Expert {i}", "Certifié", "Premium" },
                    Languages = new List<string> { "Français", "Anglais" },
                    Certifications = new List<string> { "Certification premium", "Expert certifié" }
                },
                CreatedAt = DateTime.UtcNow.AddDays(-i * 2),
                UpdatedAt = DateTime.UtcNow
            });
        }
        
        return services;
    }
}

// Handler temporaire pour les statistiques
public class GetServiceStatsQueryHandler : IRequestHandler<GetServiceStatsQuery, ServiceStatsDto>
{
    public async Task<ServiceStatsDto> Handle(GetServiceStatsQuery request, CancellationToken cancellationToken)
    {
        await Task.Delay(100, cancellationToken);
        
        return new ServiceStatsDto
        {
            TotalServices = 12000,
            TotalProviders = 322000,
            TotalBookings = 1255000,
            AverageRating = 4.9m,
            TopCategories = new List<TopCategoryDto>
            {
                new() { Name = "Ménage", Count = 2500 },
                new() { Name = "Bricolage", Count = 3200 },
                new() { Name = "Jardinage", Count = 1800 },
                new() { Name = "Garde d'enfants", Count = 1500 },
                new() { Name = "Cours particuliers", Count = 2100 }
            }
        };
    }
}

// Handler temporaire pour les suggestions
public class GetSearchSuggestionsQueryHandler : IRequestHandler<GetSearchSuggestionsQuery, List<string>>
{
    public async Task<List<string>> Handle(GetSearchSuggestionsQuery request, CancellationToken cancellationToken)
    {
        await Task.Delay(50, cancellationToken);
        
        var suggestions = new List<string>
        {
            "Ménage", "Jardinage", "Bricolage", "Garde d'enfants", 
            "Cours particuliers", "Livraison", "Plomberie", "Électricité"
        };
        
        return suggestions
            .Where(s => s.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase))
            .Take(5)
            .ToList();
    }
}
