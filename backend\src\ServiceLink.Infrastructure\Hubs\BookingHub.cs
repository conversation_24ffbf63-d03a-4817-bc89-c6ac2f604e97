using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Infrastructure.Hubs;

/// <summary>
/// Hub SignalR pour les notifications de réservations
/// Gère les notifications en temps réel pour les réservations, confirmations, annulations
/// </summary>
[Authorize]
public class BookingHub : BaseHub
{
    public BookingHub(ILogger<BookingHub> logger, ICacheService cacheService) 
        : base(logger, cacheService)
    {
    }

    /// <summary>
    /// Gère la connexion d'un utilisateur au hub de réservations
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        await ExecuteWithErrorHandlingAsync(nameof(OnConnectedAsync), async () =>
        {
            var userId = GetUserId();
            var userRole = GetUserRole();
            var userName = GetUserName();

            // Ajouter l'utilisateur au groupe correspondant à son rôle
            var roleGroup = SignalRGroups.GetRoleGroup(userRole);
            await Groups.AddToGroupAsync(Context.ConnectionId, roleGroup);

            _logger.LogInformation("Utilisateur {UserName} ({UserId}) connecté au BookingHub avec le rôle {Role}",
                userName, userId, userRole);

            // Envoyer les réservations en attente si c'est un prestataire
            if (userRole == UserRole.Provider)
            {
                await SendPendingBookingsAsync(userId);
            }

            await base.OnConnectedAsync();
        });
    }

    /// <summary>
    /// Permet à un utilisateur de rejoindre le groupe d'une réservation spécifique
    /// </summary>
    /// <param name="bookingId">ID de la réservation</param>
    [HubMethodName("JoinBookingGroup")]
    public async Task JoinBookingGroupAsync(Guid bookingId)
    {
        await ExecuteWithErrorHandlingAsync(nameof(JoinBookingGroupAsync), async () =>
        {
            ValidateInput(bookingId, nameof(bookingId));

            var userId = GetUserId();
            var userRole = GetUserRole();

            // Vérifier que l'utilisateur a le droit d'accéder à cette réservation
            if (!await CanAccessBookingAsync(userId, bookingId, userRole))
            {
                await SendErrorAsync(nameof(JoinBookingGroupAsync), "Vous n'avez pas accès à cette réservation");
                return;
            }

            var groupName = SignalRGroups.GetBookingGroup(bookingId);
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

            _logger.LogInformation("Utilisateur {UserId} a rejoint le groupe de réservation {BookingId}", userId, bookingId);

            await SendSuccessAsync(nameof(JoinBookingGroupAsync), $"Vous suivez maintenant la réservation {bookingId}");
        });
    }

    /// <summary>
    /// Permet à un utilisateur de quitter le groupe d'une réservation
    /// </summary>
    /// <param name="bookingId">ID de la réservation</param>
    [HubMethodName("LeaveBookingGroup")]
    public async Task LeaveBookingGroupAsync(Guid bookingId)
    {
        await ExecuteWithErrorHandlingAsync(nameof(LeaveBookingGroupAsync), async () =>
        {
            ValidateInput(bookingId, nameof(bookingId));

            var userId = GetUserId();
            var groupName = SignalRGroups.GetBookingGroup(bookingId);
            
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

            _logger.LogInformation("Utilisateur {UserId} a quitté le groupe de réservation {BookingId}", userId, bookingId);

            await SendSuccessAsync(nameof(LeaveBookingGroupAsync), $"Vous ne suivez plus la réservation {bookingId}");
        });
    }

    /// <summary>
    /// Notifie qu'une nouvelle réservation a été créée
    /// </summary>
    /// <param name="booking">Données de la réservation</param>
    [HubMethodName("NotifyNewBooking")]
    public async Task NotifyNewBookingAsync(BookingNotificationDto booking)
    {
        await ExecuteWithErrorHandlingAsync(nameof(NotifyNewBookingAsync), async () =>
        {
            ValidateInput(booking, nameof(booking));

            var userId = GetUserId();
            var userRole = GetUserRole();

            // Seuls les clients peuvent créer des réservations
            if (userRole != UserRole.Client)
            {
                await SendErrorAsync(nameof(NotifyNewBookingAsync), "Seuls les clients peuvent créer des réservations");
                return;
            }

            // Vérifier que l'utilisateur est bien le client de cette réservation
            if (booking.ClientId != userId)
            {
                await SendErrorAsync(nameof(NotifyNewBookingAsync), "Vous ne pouvez notifier que vos propres réservations");
                return;
            }

            var notification = new NotificationDto
            {
                Type = "NewBooking",
                Title = "Nouvelle réservation",
                Message = $"Nouvelle réservation pour {booking.ServiceName}",
                Priority = NotificationPriority.High,
                Data = booking
            };

            // Notifier le prestataire concerné
            await Clients.User(booking.ProviderId.ToString()).SendAsync("ReceiveBookingNotification", notification);

            // Notifier les administrateurs et managers
            await Clients.Groups(SignalRGroups.Administrators, SignalRGroups.Managers)
                .SendAsync("ReceiveBookingNotification", notification);

            // Ajouter la réservation au groupe spécifique
            var groupName = SignalRGroups.GetBookingGroup(booking.BookingId);
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

            _logger.LogInformation("Nouvelle réservation {BookingId} notifiée par le client {ClientId} au prestataire {ProviderId}",
                booking.BookingId, booking.ClientId, booking.ProviderId);

            await SendSuccessAsync(nameof(NotifyNewBookingAsync), "Réservation notifiée avec succès");
        });
    }

    /// <summary>
    /// Confirme une réservation (prestataire seulement)
    /// </summary>
    /// <param name="bookingId">ID de la réservation</param>
    /// <param name="message">Message de confirmation optionnel</param>
    [HubMethodName("ConfirmBooking")]
    public async Task ConfirmBookingAsync(Guid bookingId, string? message = null)
    {
        await ExecuteWithErrorHandlingAsync(nameof(ConfirmBookingAsync), async () =>
        {
            ValidateInput(bookingId, nameof(bookingId));

            var userId = GetUserId();
            var userRole = GetUserRole();
            var userName = GetUserName();

            // Seuls les prestataires peuvent confirmer des réservations
            if (userRole != UserRole.Provider)
            {
                await SendErrorAsync(nameof(ConfirmBookingAsync), "Seuls les prestataires peuvent confirmer des réservations");
                return;
            }

            var notification = new NotificationDto
            {
                Type = "BookingConfirmed",
                Title = "Réservation confirmée",
                Message = message ?? $"Votre réservation a été confirmée par {userName}",
                Priority = NotificationPriority.High,
                Data = new
                {
                    BookingId = bookingId,
                    ProviderId = userId,
                    ProviderName = userName,
                    ConfirmedAt = DateTime.UtcNow,
                    Message = message
                }
            };

            // Notifier tous les membres du groupe de cette réservation
            var groupName = SignalRGroups.GetBookingGroup(bookingId);
            await Clients.Group(groupName).SendAsync("ReceiveBookingNotification", notification);

            _logger.LogInformation("Réservation {BookingId} confirmée par le prestataire {ProviderId}",
                bookingId, userId);

            await SendSuccessAsync(nameof(ConfirmBookingAsync), "Réservation confirmée avec succès");
        });
    }

    /// <summary>
    /// Refuse une réservation (prestataire seulement)
    /// </summary>
    /// <param name="bookingId">ID de la réservation</param>
    /// <param name="reason">Raison du refus</param>
    [HubMethodName("RejectBooking")]
    public async Task RejectBookingAsync(Guid bookingId, string reason)
    {
        await ExecuteWithErrorHandlingAsync(nameof(RejectBookingAsync), async () =>
        {
            ValidateInput(bookingId, nameof(bookingId));
            ValidateInput(reason, nameof(reason));

            var userId = GetUserId();
            var userRole = GetUserRole();
            var userName = GetUserName();

            // Seuls les prestataires peuvent refuser des réservations
            if (userRole != UserRole.Provider)
            {
                await SendErrorAsync(nameof(RejectBookingAsync), "Seuls les prestataires peuvent refuser des réservations");
                return;
            }

            var notification = new NotificationDto
            {
                Type = "BookingRejected",
                Title = "Réservation refusée",
                Message = $"Votre réservation a été refusée par {userName}. Raison: {reason}",
                Priority = NotificationPriority.High,
                Data = new
                {
                    BookingId = bookingId,
                    ProviderId = userId,
                    ProviderName = userName,
                    RejectedAt = DateTime.UtcNow,
                    Reason = reason
                }
            };

            // Notifier tous les membres du groupe de cette réservation
            var groupName = SignalRGroups.GetBookingGroup(bookingId);
            await Clients.Group(groupName).SendAsync("ReceiveBookingNotification", notification);

            _logger.LogInformation("Réservation {BookingId} refusée par le prestataire {ProviderId} pour la raison: {Reason}",
                bookingId, userId, reason);

            await SendSuccessAsync(nameof(RejectBookingAsync), "Réservation refusée");
        });
    }

    /// <summary>
    /// Annule une réservation
    /// </summary>
    /// <param name="bookingId">ID de la réservation</param>
    /// <param name="reason">Raison de l'annulation</param>
    [HubMethodName("CancelBooking")]
    public async Task CancelBookingAsync(Guid bookingId, string reason)
    {
        await ExecuteWithErrorHandlingAsync(nameof(CancelBookingAsync), async () =>
        {
            ValidateInput(bookingId, nameof(bookingId));
            ValidateInput(reason, nameof(reason));

            var userId = GetUserId();
            var userRole = GetUserRole();
            var userName = GetUserName();

            var notification = new NotificationDto
            {
                Type = "BookingCancelled",
                Title = "Réservation annulée",
                Message = $"La réservation a été annulée par {userName}. Raison: {reason}",
                Priority = NotificationPriority.High,
                Data = new
                {
                    BookingId = bookingId,
                    CancelledBy = userId,
                    CancelledByName = userName,
                    CancelledByRole = userRole.ToString(),
                    CancelledAt = DateTime.UtcNow,
                    Reason = reason
                }
            };

            // Notifier tous les membres du groupe de cette réservation
            var groupName = SignalRGroups.GetBookingGroup(bookingId);
            await Clients.Group(groupName).SendAsync("ReceiveBookingNotification", notification);

            _logger.LogInformation("Réservation {BookingId} annulée par {UserId} ({Role}) pour la raison: {Reason}",
                bookingId, userId, userRole, reason);

            await SendSuccessAsync(nameof(CancelBookingAsync), "Réservation annulée");
        });
    }

    /// <summary>
    /// Met à jour le statut d'une réservation
    /// </summary>
    /// <param name="bookingId">ID de la réservation</param>
    /// <param name="newStatus">Nouveau statut</param>
    /// <param name="message">Message optionnel</param>
    [HubMethodName("UpdateBookingStatus")]
    public async Task UpdateBookingStatusAsync(Guid bookingId, string newStatus, string? message = null)
    {
        await ExecuteWithErrorHandlingAsync(nameof(UpdateBookingStatusAsync), async () =>
        {
            ValidateInput(bookingId, nameof(bookingId));
            ValidateInput(newStatus, nameof(newStatus));

            var userId = GetUserId();
            var userName = GetUserName();

            var notification = new NotificationDto
            {
                Type = "BookingStatusUpdated",
                Title = "Statut de réservation mis à jour",
                Message = message ?? $"Le statut de votre réservation a été mis à jour: {newStatus}",
                Priority = NotificationPriority.Normal,
                Data = new
                {
                    BookingId = bookingId,
                    NewStatus = newStatus,
                    UpdatedBy = userId,
                    UpdatedByName = userName,
                    UpdatedAt = DateTime.UtcNow,
                    Message = message
                }
            };

            // Notifier tous les membres du groupe de cette réservation
            var groupName = SignalRGroups.GetBookingGroup(bookingId);
            await Clients.Group(groupName).SendAsync("ReceiveBookingNotification", notification);

            _logger.LogInformation("Statut de la réservation {BookingId} mis à jour vers {NewStatus} par {UserId}",
                bookingId, newStatus, userId);

            await SendSuccessAsync(nameof(UpdateBookingStatusAsync), "Statut mis à jour");
        });
    }

    /// <summary>
    /// Envoie les réservations en attente à un prestataire
    /// </summary>
    private async Task SendPendingBookingsAsync(Guid providerId)
    {
        try
        {
            // Dans une implémentation complète, on récupérerait les réservations en attente depuis la base de données
            // Pour l'instant, on envoie juste une notification que le service est disponible
            await Clients.Caller.SendAsync("PendingBookingsAvailable", new
            {
                Message = "Service de notifications de réservations disponible",
                ProviderId = providerId,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi des réservations en attente pour le prestataire {ProviderId}", providerId);
        }
    }

    /// <summary>
    /// Vérifie si un utilisateur peut accéder à une réservation
    /// </summary>
    private async Task<bool> CanAccessBookingAsync(Guid userId, Guid bookingId, UserRole userRole)
    {
        // Dans une implémentation complète, on vérifierait en base de données
        // Pour l'instant, on autorise selon le rôle
        return userRole switch
        {
            UserRole.Admin or UserRole.Manager or UserRole.Support => true,
            UserRole.Client or UserRole.Provider => true, // On vérifierait que c'est leur réservation
            _ => false
        };
    }
}
