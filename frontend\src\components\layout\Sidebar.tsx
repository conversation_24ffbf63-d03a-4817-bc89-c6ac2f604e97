import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  Search,
  Calendar,
  User,
  Settings,
  Shield,
  Briefcase,
  Star,
  CreditCard,
  BarChart3,
  Users,
  Package,
  MessageSquare,
  AlertTriangle,
  FileCheck,
  TrendingUp,
  Headphones,
  Eye,
  DollarSign
} from 'lucide-react';
import { cn, getRoleName, getUserInitials } from '@/lib/utils';
import { useUIStore } from '@/stores';
import { useAuthStore } from '@/stores/authStore';
import { hasPermission, PERMISSIONS } from '@/lib/permissions';

interface SidebarItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  requiredPermissions?: string[];
  badge?: string;
}

// Menus selon permissions PRD
const getSidebarItems = (userRole: string): SidebarItem[] => {
  const items: SidebarItem[] = [];

  // Dashboard principal (tous les utilisateurs)
  items.push({
    title: 'Dashboard',
    href: getDashboardRoute(userRole),
    icon: Home,
  });

  // === ADMIN GLOBAL ===
  if (hasPermission(userRole, PERMISSIONS.MANAGE_ACCOUNTS.id)) {
    items.push({
      title: 'Gestion Utilisateurs',
      href: '/admin/users',
      icon: Users,
      requiredPermissions: [PERMISSIONS.MANAGE_ACCOUNTS.id],
    });
  }

  if (hasPermission(userRole, PERMISSIONS.MANAGE_SERVICES.id)) {
    items.push({
      title: 'Modération Services',
      href: '/admin/services',
      icon: Shield,
      requiredPermissions: [PERMISSIONS.MANAGE_SERVICES.id],
      badge: '12',
    });
  }

  if (hasPermission(userRole, PERMISSIONS.SUPERVISE_PAYMENTS.id)) {
    items.push({
      title: 'Finances',
      href: '/admin/finances',
      icon: DollarSign,
      requiredPermissions: [PERMISSIONS.SUPERVISE_PAYMENTS.id],
    });
  }

  if (hasPermission(userRole, PERMISSIONS.PLATFORM_CONFIG.id)) {
    items.push({
      title: 'Configuration',
      href: '/admin/config',
      icon: Settings,
      requiredPermissions: [PERMISSIONS.PLATFORM_CONFIG.id],
    });
  }

  // === MANAGER ===
  if (hasPermission(userRole, PERMISSIONS.MANAGE_COMPLAINTS.id)) {
    items.push({
      title: 'Réclamations',
      href: '/manager/complaints',
      icon: AlertTriangle,
      requiredPermissions: [PERMISSIONS.MANAGE_COMPLAINTS.id],
      badge: '5',
    });
  }

  if (hasPermission(userRole, PERMISSIONS.ADVANCED_REPORTS.id)) {
    items.push({
      title: 'Rapports Avancés',
      href: '/manager/reports',
      icon: BarChart3,
      requiredPermissions: [PERMISSIONS.ADVANCED_REPORTS.id],
    });
  }

  // === SUPPORT ===
  if (hasPermission(userRole, PERMISSIONS.CHAT_TICKETING.id)) {
    items.push({
      title: 'Chat Support',
      href: '/support/chat',
      icon: MessageSquare,
      requiredPermissions: [PERMISSIONS.CHAT_TICKETING.id],
      badge: '12',
    });

    items.push({
      title: 'Tickets',
      href: '/support/tickets',
      icon: Headphones,
      requiredPermissions: [PERMISSIONS.CHAT_TICKETING.id],
      badge: '8',
    });
  }

  if (hasPermission(userRole, PERMISSIONS.GLOBAL_STATS.id)) {
    items.push({
      title: 'Statistiques',
      href: '/support/stats',
      icon: TrendingUp,
      requiredPermissions: [PERMISSIONS.GLOBAL_STATS.id],
    });
  }

  // === SUPERVISEUR ===
  if (hasPermission(userRole, PERMISSIONS.VERIFY_DOCUMENTS.id)) {
    items.push({
      title: 'Vérification Documents',
      href: '/supervisor/documents',
      icon: FileCheck,
      requiredPermissions: [PERMISSIONS.VERIFY_DOCUMENTS.id],
      badge: '15',
    });
  }

  if (hasPermission(userRole, PERMISSIONS.SUSPEND_ACCOUNTS.id)) {
    items.push({
      title: 'Profils Signalés',
      href: '/supervisor/profiles',
      icon: Eye,
      requiredPermissions: [PERMISSIONS.SUSPEND_ACCOUNTS.id],
      badge: '8',
    });
  }

  // === CLIENT ===
  if (hasPermission(userRole, PERMISSIONS.SEARCH_BOOK.id)) {
    items.push({
      title: 'Parcourir Services',
      href: '/services',
      icon: Search,
      requiredPermissions: [PERMISSIONS.SEARCH_BOOK.id],
    });

    items.push({
      title: 'Mes Réservations',
      href: '/bookings',
      icon: Calendar,
      requiredPermissions: [PERMISSIONS.SEARCH_BOOK.id],
    });
  }

  // === PRESTATAIRE ===
  if (hasPermission(userRole, PERMISSIONS.MANAGE_PROFESSIONAL_PROFILE.id)) {
    items.push({
      title: 'Mes Services',
      href: '/provider/services',
      icon: Package,
      requiredPermissions: [PERMISSIONS.MANAGE_PROFESSIONAL_PROFILE.id],
    });
  }

  if (hasPermission(userRole, PERMISSIONS.MANAGE_CALENDAR.id)) {
    items.push({
      title: 'Réservations',
      href: '/provider/bookings',
      icon: Calendar,
      requiredPermissions: [PERMISSIONS.MANAGE_CALENDAR.id],
    });
  }

  if (hasPermission(userRole, PERMISSIONS.FINANCIAL_TRACKING.id)) {
    items.push({
      title: 'Revenus',
      href: '/provider/analytics',
      icon: TrendingUp,
      requiredPermissions: [PERMISSIONS.FINANCIAL_TRACKING.id],
    });
  }

  // === COMMUN À TOUS ===
  items.push({
    title: 'Mon Profil',
    href: '/profile',
    icon: User,
  });

  return items;
};

// Fonction pour obtenir la route du dashboard selon le rôle
function getDashboardRoute(userRole: string): string {
  switch (userRole) {
    case 'Client':
      return '/dashboard/client';
    case 'Provider':
    case 'ServiceProvider':
    case 'Prestataire':
      return '/dashboard/provider';
    case 'Admin':
    case 'Admin Global':
      return '/dashboard/admin';
    case 'Manager':
      return '/dashboard/manager';
    case 'Support':
      return '/dashboard/support';
    case 'Supervisor':
    case 'Superviseur':
      return '/dashboard/supervisor';
    default:
      return '/dashboard/general';
  }
}

export const Sidebar: React.FC = () => {
  const location = useLocation();
  const { user } = useAuthStore();
  const { sidebar, isMobile, setSidebarOpen } = useUIStore();

  // Obtenir les menus selon les permissions de l'utilisateur
  const sidebarItems = user ? getSidebarItems(user.role) : [];

  const handleItemClick = () => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  return (
    <aside 
      className={cn(
        "fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 bg-card border-r border-border transition-transform duration-300 ease-in-out z-30",
        sidebar.variant === 'overlay' && isMobile ? "translate-x-0" : "",
        !sidebar.isOpen ? "-translate-x-full" : "translate-x-0"
      )}
    >
      <div className="flex flex-col h-full">
        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {sidebarItems.map((item) => {
            const isActive = location.pathname === item.href ||
                           (item.href !== '/dashboard' && location.pathname.startsWith(item.href));
            
            return (
              <Link
                key={item.href}
                to={item.href}
                onClick={handleItemClick}
                className={cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:text-foreground hover:bg-accent"
                )}
              >
                <item.icon className="h-5 w-5" />
                <span className="flex-1">{item.title}</span>
                {item.badge && (
                  <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                    {item.badge}
                  </span>
                )}
              </Link>
            );
          })}
        </nav>

        {/* User Info */}
        {user && (
          <div className="p-4 border-t border-border">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground text-sm font-medium">
                  {getUserInitials(user.firstName, user.lastName)}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground truncate">
                  {user.firstName} {user.lastName}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {getRoleName(user.role)}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </aside>
  );
};
