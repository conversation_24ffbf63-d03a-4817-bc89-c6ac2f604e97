import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Search, 
  Calendar, 
  User, 
  Settings, 
  Shield, 
  Briefcase,
  Star,
  CreditCard,
  BarChart3,
  Users,
  Package
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useUIStore } from '@/stores';
import { useAuthStore } from '@/stores/authStore'; 

interface SidebarItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles?: string[];
}

const sidebarItems: SidebarItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: Home,
  },
  {
    title: 'Browse Services',
    href: '/services',
    icon: Search,
  },
  {
    title: 'My Bookings',
    href: '/bookings',
    icon: Calendar,
  },
  {
    title: 'Profile',
    href: '/profile',
    icon: User,
  },
  // Provider specific items
  {
    title: 'Provider Dashboard',
    href: '/provider/dashboard',
    icon: BarChart3,
    roles: ['ServiceProvider'],
  },
  {
    title: 'My Services',
    href: '/provider/services',
    icon: Package,
    roles: ['ServiceProvider'],
  },
  {
    title: 'Provider Bookings',
    href: '/provider/bookings',
    icon: Calendar,
    roles: ['ServiceProvider'],
  },
  {
    title: 'Reviews',
    href: '/provider/reviews',
    icon: Star,
    roles: ['ServiceProvider'],
  },
  {
    title: 'Earnings',
    href: '/provider/earnings',
    icon: CreditCard,
    roles: ['ServiceProvider'],
  },
  // Admin specific items
  {
    title: 'Admin Panel',
    href: '/admin',
    icon: Shield,
    roles: ['Admin'],
  },
  {
    title: 'User Management',
    href: '/admin/users',
    icon: Users,
    roles: ['Admin'],
  },
  {
    title: 'Service Management',
    href: '/admin/services',
    icon: Briefcase,
    roles: ['Admin'],
  },
  {
    title: 'System Settings',
    href: '/admin/settings',
    icon: Settings,
    roles: ['Admin'],
  },
];

export const Sidebar: React.FC = () => {
  const location = useLocation();
  const { user } = useAuthStore();
  const { sidebar, isMobile, setSidebarOpen } = useUIStore();

  const filteredItems = sidebarItems.filter(item => {
    if (!item.roles) return true;
    return user && item.roles.includes(user.role);
  });

  const handleItemClick = () => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  return (
    <aside 
      className={cn(
        "fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 bg-card border-r border-border transition-transform duration-300 ease-in-out z-30",
        sidebar.variant === 'overlay' && isMobile ? "translate-x-0" : "",
        !sidebar.isOpen ? "-translate-x-full" : "translate-x-0"
      )}
    >
      <div className="flex flex-col h-full">
        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {filteredItems.map((item) => {
            const isActive = location.pathname === item.href || 
                           (item.href !== '/dashboard' && location.pathname.startsWith(item.href));
            
            return (
              <Link
                key={item.href}
                to={item.href}
                onClick={handleItemClick}
                className={cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:text-foreground hover:bg-accent"
                )}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.title}</span>
              </Link>
            );
          })}
        </nav>

        {/* User Info */}
        {user && (
          <div className="p-4 border-t border-border">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground text-sm font-medium">
                  {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground truncate">
                  {user.firstName} {user.lastName}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.role}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </aside>
  );
};
