namespace ServiceLink.Domain.Enums;

/// <summary>
/// Énumération des unités de tarification
/// </summary>
public enum PricingUnit
{
    /// <summary>
    /// Tarification à l'heure
    /// </summary>
    Hourly = 1,

    /// <summary>
    /// Tarification forfaitaire
    /// </summary>
    Fixed = 2,

    /// <summary>
    /// Tarification à la journée
    /// </summary>
    Daily = 3,

    /// <summary>
    /// Tarification au mètre carré
    /// </summary>
    PerSquareMeter = 4,

    /// <summary>
    /// Tarification à la pièce
    /// </summary>
    PerRoom = 5,

    /// <summary>
    /// Tarification au kilomètre
    /// </summary>
    PerKilometer = 6,

    /// <summary>
    /// Tarification à l'unité
    /// </summary>
    PerUnit = 7,

    /// <summary>
    /// Tarification personnalisée
    /// </summary>
    Custom = 99
}

/// <summary>
/// Extensions pour PricingUnit
/// </summary>
public static class PricingUnitExtensions
{
    /// <summary>
    /// Obtient la description de l'unité de tarification
    /// </summary>
    public static string GetDescription(this PricingUnit unit)
    {
        return unit switch
        {
            PricingUnit.Hourly => "Par heure",
            PricingUnit.Fixed => "Forfait",
            PricingUnit.Daily => "Par jour",
            PricingUnit.PerSquareMeter => "Par m²",
            PricingUnit.PerRoom => "Par pièce",
            PricingUnit.PerKilometer => "Par km",
            PricingUnit.PerUnit => "Par unité",
            PricingUnit.Custom => "Personnalisé",
            _ => "Inconnu"
        };
    }

    /// <summary>
    /// Obtient le symbole de l'unité
    /// </summary>
    public static string GetSymbol(this PricingUnit unit)
    {
        return unit switch
        {
            PricingUnit.Hourly => "/h",
            PricingUnit.Fixed => "",
            PricingUnit.Daily => "/jour",
            PricingUnit.PerSquareMeter => "/m²",
            PricingUnit.PerRoom => "/pièce",
            PricingUnit.PerKilometer => "/km",
            PricingUnit.PerUnit => "/unité",
            PricingUnit.Custom => "",
            _ => ""
        };
    }
}
