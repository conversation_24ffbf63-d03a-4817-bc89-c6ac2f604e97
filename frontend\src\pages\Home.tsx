import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Search, ArrowRight, CheckCircle, Star, Clock, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useServiceStore } from '@/stores';

export const Home: React.FC = () => {
  const { 
    categories, 
    featuredServices, 
    popularServices, 
    loadCategories, 
    loadFeaturedServices, 
    loadPopularServices,
    searchFilters,
    updateSearchFilters
  } = useServiceStore();

  useEffect(() => {
    loadCategories();
    loadFeaturedServices();
    loadPopularServices();
  }, [loadCategories, loadFeaturedServices, loadPopularServices]);

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const searchTerm = formData.get('searchTerm') as string;
    
    if (searchTerm) {
      updateSearchFilters({ searchTerm });
      window.location.href = `/services?search=${encodeURIComponent(searchTerm)}`;
    }
  };

  return (
    <div className="space-y-16 pb-16">
      {/* Hero Section */}
      <section className="relative -mt-16 pt-32 pb-24 bg-gradient-to-r from-primary/10 to-secondary/10">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
            Trouvez le service parfait <br className="hidden md:block" />
            <span className="text-primary">en quelques clics</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Des milliers de prestataires qualifiés prêts à vous aider pour tous vos besoins du quotidien
          </p>

          {/* Search Form */}
          <form onSubmit={handleSearch} className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input 
              name="searchTerm"
              placeholder="Que recherchez-vous ? (ménage, jardinage, bricolage...)"
              className="pl-12 pr-32 py-6 text-lg rounded-full shadow-lg"
            />
            <Button 
              type="submit" 
              size="lg" 
              className="absolute right-1 top-1 bottom-1 rounded-full px-6"
            >
              Rechercher
            </Button>
          </form>

          {/* Popular Categories */}
          <div className="mt-12 flex flex-wrap justify-center gap-3">
            {categories.slice(0, 8).map((category) => (
              <Link 
                key={category.id} 
                to={`/services?category=${category.id}`}
                className="bg-background hover:bg-accent text-foreground px-4 py-2 rounded-full border border-border transition-colors"
              >
                {category.name}
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12">Comment ça marche ?</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">1. Recherchez un service</h3>
            <p className="text-muted-foreground">
              Parcourez notre large sélection de services ou utilisez la recherche pour trouver exactement ce dont vous avez besoin.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">2. Réservez en ligne</h3>
            <p className="text-muted-foreground">
              Choisissez une date et une heure qui vous conviennent et réservez en quelques clics.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">3. Profitez du service</h3>
            <p className="text-muted-foreground">
              Le prestataire vient chez vous et réalise la prestation selon vos besoins.
            </p>
          </div>
        </div>
        
        <div className="text-center mt-12">
          <Button asChild size="lg">
            <Link to="/services">
              Découvrir tous les services <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </section>

      {/* Featured Services */}
      <section className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">Services populaires</h2>
          <Button asChild variant="outline">
            <Link to="/services">Voir tous les services</Link>
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {popularServices.slice(0, 6).map((service) => (
            <Card key={service.id} className="group hover:shadow-lg transition-all duration-300">
              <CardContent className="p-0">
                {/* Service Image */}
                <div className="relative h-48 bg-muted">
                  {service.images && service.images.length > 0 ? (
                    <img 
                      src={service.images[0]} 
                      alt={service.name} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-primary/10">
                      <span className="text-primary font-medium">Service Image</span>
                    </div>
                  )}
                  
                  {/* Category Badge */}
                  {service.category && (
                    <Badge className="absolute top-3 left-3">
                      {service.category.name}
                    </Badge>
                  )}
                  
                  {/* Price Badge */}
                  <div className="absolute bottom-3 right-3 bg-background rounded-full px-3 py-1 text-sm font-medium shadow-sm">
                    À partir de {service.basePrice}€
                    {service.pricingUnit === 'Hourly' && '/h'}
                  </div>
                </div>
                
                {/* Service Info */}
                <div className="p-4">
                  <h3 className="font-semibold text-lg mb-2 group-hover:text-primary transition-colors">
                    {service.name}
                  </h3>
                  
                  <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                    {service.description}
                  </p>
                  
                  {/* Provider Info */}
                  {service.provider && (
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={service.provider.avatar} alt={service.provider.firstName} />
                        <AvatarFallback>
                          {service.provider.firstName.charAt(0)}{service.provider.lastName.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div>
                        <p className="text-sm font-medium">
                          {service.provider.firstName} {service.provider.lastName}
                        </p>
                        <div className="flex items-center">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span className="text-xs text-muted-foreground ml-1">
                            {service.provider.rating.toFixed(1)} ({service.provider.reviewCount} avis)
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {/* Action Button */}
                  <Button asChild className="w-full mt-4">
                    <Link to={`/services/${service.id}`}>
                      Réserver maintenant
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Testimonials */}
      <section className="bg-muted py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Ce que disent nos clients</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="bg-card">
                <CardContent className="p-6">
                  <div className="flex text-yellow-400 mb-4">
                    {[...Array(5)].map((_, j) => (
                      <Star key={j} className="h-5 w-5 fill-current" />
                    ))}
                  </div>
                  
                  <p className="text-foreground mb-6 italic">
                    "Service impeccable et rapide. Le prestataire était très professionnel et a fait un excellent travail. Je recommande vivement !"
                  </p>
                  
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarFallback>
                        {String.fromCharCode(65 + i)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <p className="font-medium">Client {i}</p>
                      <p className="text-sm text-muted-foreground">Service de ménage</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Trust Badges */}
      <section className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Shield className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold">Prestataires vérifiés</h3>
              <p className="text-sm text-muted-foreground">
                Tous nos prestataires sont soigneusement sélectionnés
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Star className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold">Satisfaction garantie</h3>
              <p className="text-sm text-muted-foreground">
                Des milliers de clients satisfaits
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Clock className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold">Service rapide</h3>
              <p className="text-sm text-muted-foreground">
                Réservez en quelques clics et obtenez un service rapide
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="bg-primary text-primary-foreground py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Prêt à simplifier votre quotidien ?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Rejoignez des milliers de clients satisfaits et trouvez le service parfait dès aujourd'hui.
          </p>
          <Button asChild size="lg" variant="secondary">
            <Link to="/services">
              Trouver un service maintenant
            </Link>
          </Button>
        </div>
      </section>
    </div>
  );
};
