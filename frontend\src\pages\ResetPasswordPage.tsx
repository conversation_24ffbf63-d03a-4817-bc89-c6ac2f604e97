import React from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { ForgotPasswordForm } from '../components/auth/ForgotPasswordForm'
import { ResetPasswordForm } from '../components/auth/ResetPasswordForm'

export const ResetPasswordPage: React.FC = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  
  const token = searchParams.get('token')
  const email = searchParams.get('email')

  const handleForgotPasswordSuccess = () => {
    // Rester sur la page pour afficher le message de succès
  }

  const handleResetPasswordSuccess = () => {
    // Rediriger vers la page de connexion après succès
    setTimeout(() => {
      navigate('/login')
    }, 2000)
  }

  const handleBackToLogin = () => {
    navigate('/login')
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      {token && email ? (
        // Mode réinitialisation avec token
        <ResetPasswordForm
          token={token}
          email={email}
          onSuccess={handleResetPasswordSuccess}
        />
      ) : (
        // Mode demande de réinitialisation
        <ForgotPasswordForm
          onSuccess={handleForgotPasswordSuccess}
          onBack={handleBackToLogin}
        />
      )}
    </div>
  )
}
