using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ServiceLink.Application.Interfaces;
using ServiceLink.Infrastructure.Services;

namespace ServiceLink.Infrastructure.Configuration;

/// <summary>
/// Configuration pour les services de paiement
/// </summary>
public static class PaymentConfiguration
{
    /// <summary>
    /// Ajoute les services de paiement à la collection de services
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration de l'application</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddPaymentServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configuration des settings
        services.Configure<PaymentSettings>(configuration.GetSection("Payment"));
        services.Configure<StripeSettings>(configuration.GetSection("Stripe"));
        services.Configure<PayPalSettings>(configuration.GetSection("PayPal"));
        services.Configure<FlutterwaveSettings>(configuration.GetSection("Flutterwave"));

        // Enregistrement des services de paiement individuels
        services.AddScoped<StripePaymentService>();
        services.AddScoped<PayPalPaymentService>();
        services.AddScoped<FlutterwavePaymentService>();

        // Enregistrement de la factory
        services.AddScoped<IPaymentServiceFactory, PaymentServiceFactory>();

        // Services de sécurité et d'audit
        services.AddScoped<IPaymentAuditService, PaymentAuditService>();
        services.AddScoped<IPaymentEncryptionService, PaymentEncryptionService>();

        // HttpClient pour Flutterwave
        services.AddHttpClient<FlutterwavePaymentService>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
            client.DefaultRequestHeaders.Add("User-Agent", "ServiceLink/1.0");
        });

        return services;
    }

    /// <summary>
    /// Ajoute les health checks pour les services de paiement
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddPaymentHealthChecks(this IServiceCollection services)
    {
        services.AddHealthChecks()
            .AddCheck<PaymentHealthCheck>("payment", tags: new[] { "payment", "external" });

        return services;
    }
}

/// <summary>
/// Health check pour les services de paiement
/// </summary>
public class PaymentHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IPaymentServiceFactory _paymentServiceFactory;

    public PaymentHealthCheck(IPaymentServiceFactory paymentServiceFactory)
    {
        _paymentServiceFactory = paymentServiceFactory;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Pour l'instant, retournons un health check basique
            var healthData = new Dictionary<string, object>
            {
                ["status"] = "Payment services configured"
            };

            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                "Services de paiement configurés", healthData);
        }
        catch (Exception ex)
        {
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                "Erreur lors de la vérification des services de paiement", ex);
        }
    }
}

/// <summary>
/// Extensions pour les enums de paiement (déplacées ici depuis PaymentEnums.cs supprimé)
/// </summary>
public static class PaymentEnumsExtensions
{
    /// <summary>
    /// Obtient la description d'un provider de paiement
    /// </summary>
    /// <param name="provider">Provider de paiement</param>
    /// <returns>Description du provider</returns>
    public static string GetDescription(this Domain.Enums.PaymentProvider provider)
    {
        return provider switch
        {
            Domain.Enums.PaymentProvider.Stripe => "Stripe - Paiements internationaux sécurisés",
            Domain.Enums.PaymentProvider.PayPal => "PayPal - Solution de paiement populaire",
            Domain.Enums.PaymentProvider.Flutterwave => "Flutterwave - Paiements pour l'Afrique",
            _ => "Provider inconnu"
        };
    }

    /// <summary>
    /// Obtient les méthodes de paiement supportées par un provider
    /// </summary>
    /// <param name="provider">Provider de paiement</param>
    /// <returns>Liste des méthodes supportées</returns>
    public static IEnumerable<Domain.Enums.PaymentMethodType> GetSupportedMethods(this Domain.Enums.PaymentProvider provider)
    {
        return provider switch
        {
            Domain.Enums.PaymentProvider.Stripe => new[]
            {
                Domain.Enums.PaymentMethodType.Card,
                Domain.Enums.PaymentMethodType.BankTransfer,
                Domain.Enums.PaymentMethodType.ApplePay,
                Domain.Enums.PaymentMethodType.GooglePay
            },
            Domain.Enums.PaymentProvider.PayPal => new[]
            {
                Domain.Enums.PaymentMethodType.PayPal,
                Domain.Enums.PaymentMethodType.Card
            },
            Domain.Enums.PaymentProvider.Flutterwave => new[]
            {
                Domain.Enums.PaymentMethodType.Card,
                Domain.Enums.PaymentMethodType.BankTransfer,
                Domain.Enums.PaymentMethodType.MobileMoney
            },
            _ => Array.Empty<Domain.Enums.PaymentMethodType>()
        };
    }

    /// <summary>
    /// Obtient la description d'un statut de paiement
    /// </summary>
    /// <param name="status">Statut de paiement</param>
    /// <returns>Description du statut</returns>
    public static string GetDescription(this Domain.Enums.PaymentStatus status)
    {
        return status switch
        {
            Domain.Enums.PaymentStatus.Pending => "En attente de traitement",
            Domain.Enums.PaymentStatus.Processing => "En cours de traitement",
            Domain.Enums.PaymentStatus.Completed => "Paiement réussi",
            Domain.Enums.PaymentStatus.Failed => "Paiement échoué",
            Domain.Enums.PaymentStatus.Cancelled => "Paiement annulé",
            Domain.Enums.PaymentStatus.PartiallyRefunded => "Remboursé partiellement",
            Domain.Enums.PaymentStatus.Refunded => "Remboursé totalement",
            Domain.Enums.PaymentStatus.RequiresConfirmation => "En attente de confirmation",
            Domain.Enums.PaymentStatus.RequiresAction => "Action utilisateur requise",
            _ => "Statut inconnu"
        };
    }

    /// <summary>
    /// Vérifie si un statut de paiement est final
    /// </summary>
    /// <param name="status">Statut de paiement</param>
    /// <returns>True si le statut est final</returns>
    public static bool IsFinal(this Domain.Enums.PaymentStatus status)
    {
        return status is Domain.Enums.PaymentStatus.Completed or Domain.Enums.PaymentStatus.Failed or 
               Domain.Enums.PaymentStatus.Cancelled or Domain.Enums.PaymentStatus.Refunded;
    }

    /// <summary>
    /// Vérifie si un statut de paiement indique un succès
    /// </summary>
    /// <param name="status">Statut de paiement</param>
    /// <returns>True si le paiement est réussi</returns>
    public static bool IsSuccessful(this Domain.Enums.PaymentStatus status)
    {
        return status is Domain.Enums.PaymentStatus.Completed or Domain.Enums.PaymentStatus.PartiallyRefunded;
    }

    /// <summary>
    /// Obtient la description d'une méthode de paiement
    /// </summary>
    /// <param name="method">Méthode de paiement</param>
    /// <returns>Description de la méthode</returns>
    public static string GetDescription(this Domain.Enums.PaymentMethodType method)
    {
        return method switch
        {
            Domain.Enums.PaymentMethodType.Card => "Carte de crédit/débit",
            Domain.Enums.PaymentMethodType.BankTransfer => "Virement bancaire",
            Domain.Enums.PaymentMethodType.PayPal => "PayPal",
            Domain.Enums.PaymentMethodType.MobileMoney => "Mobile Money",
            Domain.Enums.PaymentMethodType.Crypto => "Crypto-monnaies",
            Domain.Enums.PaymentMethodType.ApplePay => "Apple Pay",
            Domain.Enums.PaymentMethodType.GooglePay => "Google Pay",
            _ => "Méthode inconnue"
        };
    }

    /// <summary>
    /// Obtient la description d'un statut de remboursement
    /// </summary>
    /// <param name="status">Statut de remboursement</param>
    /// <returns>Description du statut</returns>
    public static string GetDescription(this Domain.Enums.RefundStatus status)
    {
        return status switch
        {
            Domain.Enums.RefundStatus.Pending => "Remboursement en attente",
            Domain.Enums.RefundStatus.Processing => "Remboursement en cours",
            Domain.Enums.RefundStatus.Succeeded => "Remboursement réussi",
            Domain.Enums.RefundStatus.Failed => "Remboursement échoué",
            Domain.Enums.RefundStatus.Canceled => "Remboursement annulé",
            _ => "Statut inconnu"
        };
    }

    /// <summary>
    /// Vérifie si un statut de remboursement est final
    /// </summary>
    /// <param name="status">Statut de remboursement</param>
    /// <returns>True si le statut est final</returns>
    public static bool IsFinal(this Domain.Enums.RefundStatus status)
    {
        return status is Domain.Enums.RefundStatus.Succeeded or Domain.Enums.RefundStatus.Failed or Domain.Enums.RefundStatus.Canceled;
    }
}
