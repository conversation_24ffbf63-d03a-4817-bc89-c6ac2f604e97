import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Users, TrendingUp, Shield, Settings, AlertTriangle, CheckCircle, DollarSign, Calendar } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useServiceStore, useBookingStore } from '@/stores';
import { useAuthStore } from '@/stores/authStore';

export const DashboardAdmin: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    services,
    serviceStats,
    loadServices,
    getServiceStats 
  } = useServiceStore();
  const { 
    bookingStats,
    getBookingStats 
  } = useBookingStore();

  // Temporairement désactivé pour éviter les erreurs 401
  // useEffect(() => {
  //   loadServices();
  //   getServiceStats();
  //   getBookingStats();
  // }, [loadServices, getServiceStats, getBookingStats]);

  // Mock admin stats (à remplacer par de vraies données)
  const adminStats = {
    totalUsers: 1247,
    activeProviders: 89,
    totalServices: 156,
    pendingApprovals: 12,
    totalRevenue: 45678.90,
    platformCommission: 3654.31,
    monthlyGrowth: 12.5
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 -mx-6 px-6 py-8 rounded-lg">
        <h1 className="text-3xl font-bold mb-2 text-purple-800">
          Tableau de bord Admin - {user?.firstName}
        </h1>
        <p className="text-purple-700 mb-6">
          Surveillez l'activité de la plateforme et gérez les utilisateurs
        </p>
        
        {/* Admin Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-800">{adminStats.totalUsers}</div>
            <p className="text-sm text-blue-600">Utilisateurs totaux</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Shield className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-800">{adminStats.activeProviders}</div>
            <p className="text-sm text-green-600">Prestataires actifs</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-800">{adminStats.totalServices}</div>
            <p className="text-sm text-purple-600">Services disponibles</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <AlertTriangle className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-800">{adminStats.pendingApprovals}</div>
            <p className="text-sm text-orange-600">En attente d'approbation</p>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Revenus Plateforme</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800">
              {new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR',
              }).format(adminStats.totalRevenue)}
            </div>
            <p className="text-xs text-green-600">
              Commission: {new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR',
              }).format(adminStats.platformCommission)}
            </p>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Croissance Mensuelle</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">+{adminStats.monthlyGrowth}%</div>
            <p className="text-xs text-blue-600">
              Par rapport au mois dernier
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700">Réservations Totales</CardTitle>
            <Calendar className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800">
              {bookingStats?.totalBookings || 0}
            </div>
            <p className="text-xs text-purple-600">
              Taux de réussite: {bookingStats?.completionRate?.toFixed(1) || 0}%
            </p>
          </CardContent>
        </Card>

        <Card className="border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700">Actions Requises</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-800">{adminStats.pendingApprovals}</div>
            <p className="text-xs text-orange-600">
              Approbations en attente
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Actions d'administration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/admin/users" className="block">
              <Button className="h-auto p-6 flex-col space-y-3 w-full bg-purple-600 hover:bg-purple-700">
                <Users className="h-8 w-8" />
                <span className="text-lg">Gestion Utilisateurs</span>
                <span className="text-sm opacity-90">Clients et prestataires</span>
              </Button>
            </Link>
            
            <Link to="/admin/services" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-green-200 hover:bg-green-50">
                <Shield className="h-8 w-8 text-green-600" />
                <span className="text-lg">Modération Services</span>
                <span className="text-sm text-muted-foreground">
                  {adminStats.pendingApprovals} en attente
                </span>
              </Button>
            </Link>
            
            <Link to="/admin/analytics" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-blue-200 hover:bg-blue-50">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <span className="text-lg">Analyses Avancées</span>
                <span className="text-sm text-muted-foreground">Rapports et statistiques</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Platform Health */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Santé de la Plateforme
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Taux de satisfaction</span>
                <span>94%</span>
              </div>
              <Progress value={94} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Temps de réponse moyen</span>
                <span>1.2h</span>
              </div>
              <Progress value={85} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Prestataires vérifiés</span>
                <span>98%</span>
              </div>
              <Progress value={98} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              Alertes et Notifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <span className="text-sm">12 services en attente d'approbation</span>
                </div>
                <Badge variant="secondary">Urgent</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">Pic d'activité détecté</span>
                </div>
                <Badge variant="outline">Info</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Système opérationnel</span>
                </div>
                <Badge className="bg-green-100 text-green-800">OK</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
