using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Enums;
using Stripe;
using Stripe.Checkout;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Implémentation du service de paiement Stripe
/// </summary>
public class StripePaymentService : IPaymentService
{
    private readonly ILogger<StripePaymentService> _logger;
    private readonly StripeSettings _settings;
    private readonly PaymentIntentService _paymentIntentService;
    private readonly RefundService _refundService;
    private readonly SessionService _sessionService;

    public StripePaymentService(
        IConfiguration configuration,
        ILogger<StripePaymentService> logger)
    {
        _logger = logger;
        _settings = configuration.GetSection("Stripe").Get<StripeSettings>() ?? new StripeSettings();
        
        // Configuration de Stripe
        StripeConfiguration.ApiKey = _settings.SecretKey;
        
        // Initialisation des services Stripe
        _paymentIntentService = new PaymentIntentService();
        _refundService = new RefundService();
        _sessionService = new SessionService();

        _logger.LogInformation("Service de paiement Stripe initialisé");
    }

    /// <inheritdoc />
    public PaymentProvider Provider => PaymentProvider.Stripe;

    /// <inheritdoc />
    public async Task<PaymentIntentResponse> CreatePaymentIntentAsync(
        CreatePaymentIntentRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Création d'une intention de paiement Stripe pour {Amount} {Currency}", 
                request.Amount, request.Currency);

            var options = new PaymentIntentCreateOptions
            {
                Amount = request.Amount,
                Currency = request.Currency.ToLowerInvariant(),
                Description = request.Description,
                Metadata = new Dictionary<string, string>
                {
                    ["user_id"] = request.UserId.ToString(),
                    ["booking_id"] = request.BookingId.ToString(),
                    ["provider"] = "stripe"
                }
            };

            // Ajouter les métadonnées personnalisées
            foreach (var metadata in request.Metadata)
            {
                options.Metadata[metadata.Key] = metadata.Value;
            }

            // Configurer les méthodes de paiement autorisées
            if (request.AllowedPaymentMethods.Any())
            {
                options.PaymentMethodTypes = request.AllowedPaymentMethods
                    .Where(m => IsStripeMethodSupported(m))
                    .Select(MapToStripePaymentMethod)
                    .ToList();
            }
            else
            {
                options.PaymentMethodTypes = new List<string> { "card" };
            }

            var paymentIntent = await _paymentIntentService.CreateAsync(options, cancellationToken: cancellationToken);

            _logger.LogInformation("Intention de paiement Stripe créée: {PaymentIntentId}", paymentIntent.Id);

            return new PaymentIntentResponse
            {
                PaymentIntentId = paymentIntent.Id,
                ClientSecret = paymentIntent.ClientSecret,
                Status = MapStripeStatus(paymentIntent.Status),
                Amount = paymentIntent.Amount,
                Currency = paymentIntent.Currency.ToUpperInvariant(),
                ProviderData = new Dictionary<string, object>
                {
                    ["stripe_payment_intent_id"] = paymentIntent.Id,
                    ["stripe_status"] = paymentIntent.Status
                }
            };
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Erreur Stripe lors de la création de l'intention de paiement");
            throw new InvalidOperationException($"Erreur Stripe: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de l'intention de paiement Stripe");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<PaymentConfirmationResponse> ConfirmPaymentAsync(
        string paymentIntentId, 
        string paymentMethodId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Confirmation du paiement Stripe {PaymentIntentId}", paymentIntentId);

            var options = new PaymentIntentConfirmOptions
            {
                PaymentMethod = paymentMethodId,
                ReturnUrl = _settings.ReturnUrl
            };

            var paymentIntent = await _paymentIntentService.ConfirmAsync(
                paymentIntentId, 
                options, 
                cancellationToken: cancellationToken);

            var success = paymentIntent.Status == "succeeded";
            
            _logger.LogInformation("Paiement Stripe {PaymentIntentId} confirmé: {Status}", 
                paymentIntentId, paymentIntent.Status);

            return new PaymentConfirmationResponse
            {
                Success = success,
                TransactionId = paymentIntent.Id,
                Status = MapStripeStatus(paymentIntent.Status),
                ErrorMessage = success ? null : $"Paiement non confirmé: {paymentIntent.Status}",
                ErrorCode = success ? null : paymentIntent.Status
            };
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Erreur Stripe lors de la confirmation du paiement {PaymentIntentId}", paymentIntentId);
            
            return new PaymentConfirmationResponse
            {
                Success = false,
                TransactionId = paymentIntentId,
                Status = PaymentStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = ex.StripeError?.Code
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la confirmation du paiement Stripe {PaymentIntentId}", paymentIntentId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<PaymentStatusResponse> GetPaymentStatusAsync(
        string paymentId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Récupération du statut du paiement Stripe {PaymentId}", paymentId);

            var paymentIntent = await _paymentIntentService.GetAsync(paymentId, cancellationToken: cancellationToken);

            return new PaymentStatusResponse
            {
                PaymentId = paymentIntent.Id,
                Status = MapStripeStatus(paymentIntent.Status),
                Amount = paymentIntent.Amount,
                Currency = paymentIntent.Currency.ToUpperInvariant(),
                CreatedAt = paymentIntent.Created,
                UpdatedAt = DateTime.UtcNow, // Stripe ne fournit pas de UpdatedAt
                Metadata = paymentIntent.Metadata ?? new Dictionary<string, string>()
            };
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Erreur Stripe lors de la récupération du statut {PaymentId}", paymentId);
            throw new InvalidOperationException($"Erreur Stripe: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du statut Stripe {PaymentId}", paymentId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<RefundResponse> RefundPaymentAsync(
        RefundRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Remboursement Stripe pour le paiement {PaymentId}", request.PaymentId);

            var options = new RefundCreateOptions
            {
                PaymentIntent = request.PaymentId,
                Reason = MapRefundReason(request.Reason),
                Metadata = request.Metadata
            };

            if (request.Amount.HasValue)
            {
                options.Amount = request.Amount.Value;
            }

            var refund = await _refundService.CreateAsync(options, cancellationToken: cancellationToken);

            _logger.LogInformation("Remboursement Stripe créé: {RefundId}", refund.Id);

            return new RefundResponse
            {
                Success = refund.Status == "succeeded",
                RefundId = refund.Id,
                Amount = refund.Amount,
                Status = MapStripeRefundStatus(refund.Status)
            };
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Erreur Stripe lors du remboursement {PaymentId}", request.PaymentId);
            
            return new RefundResponse
            {
                Success = false,
                RefundId = string.Empty,
                Amount = 0,
                Status = RefundStatus.Failed,
                ErrorMessage = ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du remboursement Stripe {PaymentId}", request.PaymentId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PaymentTransactionDto>> GetUserTransactionsAsync(
        Guid userId, 
        int limit = 50, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Récupération des transactions Stripe pour l'utilisateur {UserId}", userId);

            var options = new PaymentIntentListOptions
            {
                Limit = limit,
                Created = new DateRangeOptions
                {
                    GreaterThanOrEqual = DateTime.UtcNow.AddMonths(-6) // Derniers 6 mois
                }
            };

            var paymentIntents = await _paymentIntentService.ListAsync(options, cancellationToken: cancellationToken);

            // Filtrer par utilisateur (via métadonnées)
            var userTransactions = paymentIntents.Data
                .Where(pi => pi.Metadata?.ContainsKey("user_id") == true && 
                           pi.Metadata["user_id"] == userId.ToString())
                .Select(pi => new PaymentTransactionDto
                {
                    TransactionId = pi.Id,
                    Provider = PaymentProvider.Stripe,
                    Amount = pi.Amount,
                    Currency = pi.Currency.ToUpperInvariant(),
                    Status = MapStripeStatus(pi.Status),
                    Description = pi.Description ?? string.Empty,
                    CreatedAt = pi.Created,
                    BookingId = pi.Metadata?.ContainsKey("booking_id") == true && 
                              Guid.TryParse(pi.Metadata["booking_id"], out var bookingId) ? bookingId : null
                })
                .ToList();

            _logger.LogDebug("Trouvé {Count} transactions Stripe pour l'utilisateur {UserId}", 
                userTransactions.Count, userId);

            return userTransactions;
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Erreur Stripe lors de la récupération des transactions pour {UserId}", userId);
            throw new InvalidOperationException($"Erreur Stripe: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des transactions Stripe pour {UserId}", userId);
            throw;
        }
    }

    /// <inheritdoc />
    public bool ValidateWebhookSignature(string payload, string signature, string secret)
    {
        try
        {
            var stripeEvent = EventUtility.ConstructEvent(payload, signature, secret);
            return stripeEvent != null;
        }
        catch (StripeException ex)
        {
            _logger.LogWarning(ex, "Signature webhook Stripe invalide");
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<WebhookProcessingResult> ProcessWebhookEventAsync(
        WebhookEventDto webhookEvent, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Traitement de l'événement webhook Stripe: {EventType}", webhookEvent.EventType);

            var result = new WebhookProcessingResult { Success = true };

            switch (webhookEvent.EventType)
            {
                case "payment_intent.succeeded":
                    result.ActionsPerformed.Add("Paiement confirmé avec succès");
                    break;

                case "payment_intent.payment_failed":
                    result.ActionsPerformed.Add("Paiement échoué - notification envoyée");
                    break;

                case "charge.dispute.created":
                    result.ActionsPerformed.Add("Litige créé - équipe notifiée");
                    break;

                default:
                    result.Message = $"Événement {webhookEvent.EventType} ignoré";
                    break;
            }

            _logger.LogInformation("Événement webhook Stripe {EventType} traité avec succès", webhookEvent.EventType);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de l'événement webhook Stripe {EventType}", 
                webhookEvent.EventType);
            
            return new WebhookProcessingResult
            {
                Success = false,
                Message = "Erreur lors du traitement",
                Errors = { ex.Message }
            };
        }
    }

    #region Méthodes privées de mapping

    private static PaymentStatus MapStripeStatus(string stripeStatus)
    {
        return stripeStatus switch
        {
            "requires_payment_method" => PaymentStatus.Pending,
            "requires_confirmation" => PaymentStatus.RequiresConfirmation,
            "requires_action" => PaymentStatus.RequiresAction,
            "processing" => PaymentStatus.Processing,
            "succeeded" => PaymentStatus.Completed,
            "canceled" => PaymentStatus.Cancelled,
            _ => PaymentStatus.Failed
        };
    }

    private static RefundStatus MapStripeRefundStatus(string stripeStatus)
    {
        return stripeStatus switch
        {
            "pending" => RefundStatus.Pending,
            "succeeded" => RefundStatus.Succeeded,
            "failed" => RefundStatus.Failed,
            "canceled" => RefundStatus.Canceled,
            _ => RefundStatus.Failed
        };
    }

    private static bool IsStripeMethodSupported(PaymentMethodType method)
    {
        return method is PaymentMethodType.Card or PaymentMethodType.BankTransfer or 
               PaymentMethodType.ApplePay or PaymentMethodType.GooglePay;
    }

    private static string MapToStripePaymentMethod(PaymentMethodType method)
    {
        return method switch
        {
            PaymentMethodType.Card => "card",
            PaymentMethodType.BankTransfer => "sepa_debit",
            PaymentMethodType.ApplePay => "apple_pay",
            PaymentMethodType.GooglePay => "google_pay",
            _ => "card"
        };
    }

    private static string MapRefundReason(string reason)
    {
        // Stripe accepte: duplicate, fraudulent, requested_by_customer
        return reason.ToLowerInvariant() switch
        {
            var r when r.Contains("fraud") => "fraudulent",
            var r when r.Contains("duplicate") => "duplicate",
            _ => "requested_by_customer"
        };
    }

    #endregion
}

/// <summary>
/// Configuration pour Stripe
/// </summary>
public class StripeSettings
{
    /// <summary>
    /// Clé publique Stripe
    /// </summary>
    public string PublishableKey { get; set; } = string.Empty;

    /// <summary>
    /// Clé secrète Stripe
    /// </summary>
    public string SecretKey { get; set; } = string.Empty;

    /// <summary>
    /// Secret du webhook
    /// </summary>
    public string WebhookSecret { get; set; } = string.Empty;

    /// <summary>
    /// URL de retour après paiement
    /// </summary>
    public string ReturnUrl { get; set; } = string.Empty;

    /// <summary>
    /// Mode test activé
    /// </summary>
    public bool TestMode { get; set; } = true;
}
