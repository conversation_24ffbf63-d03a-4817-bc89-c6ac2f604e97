import type { Service, ServiceProvider } from './service'
import type { User } from './auth'

export type BookingStatus = 
  | 'Pending'      // En attente de confirmation
  | 'Confirmed'    // Confirmée par le prestataire
  | 'InProgress'   // En cours
  | 'Completed'    // Terminée
  | 'Cancelled'    // Annulée
  | 'Rejected'     // Refusée par le prestataire
  | 'Refunded'     // Remboursée

export type PaymentStatus = 
  | 'Pending'      // En attente
  | 'Authorized'   // Autorisé
  | 'Captured'     // Capturé
  | 'Failed'       // Échoué
  | 'Refunded'     // Remboursé
  | 'PartiallyRefunded' // Partiellement remboursé

export interface Booking {
  id: string
  serviceId: string
  service?: Service
  clientId: string
  client?: User
  providerId: string
  provider?: ServiceProvider
  
  // Détails de la réservation
  scheduledDate: string // YYYY-MM-DD
  scheduledStartTime: string // HH:mm
  scheduledEndTime: string // HH:mm
  duration: number // en minutes
  
  // Localisation
  address: string
  city: string
  postalCode: string
  latitude?: number
  longitude?: number
  additionalInstructions?: string
  
  // Statut
  status: BookingStatus
  paymentStatus: PaymentStatus
  
  // Prix
  basePrice: number
  urgentFee: number
  serviceFee: number
  totalPrice: number
  currency: string
  
  // Options
  isUrgent: boolean
  isRecurring: boolean
  recurringPattern?: {
    frequency: 'Daily' | 'Weekly' | 'Monthly'
    interval: number
    endDate?: string
  }
  
  // Messages et notes
  clientNotes?: string
  providerNotes?: string
  cancellationReason?: string
  rejectionReason?: string
  
  // Timestamps
  createdAt: string
  updatedAt: string
  confirmedAt?: string
  startedAt?: string
  completedAt?: string
  cancelledAt?: string
  
  // Relations
  payment?: BookingPayment
  review?: BookingReview
  messages?: BookingMessage[]
}

export interface BookingPayment {
  id: string
  bookingId: string
  amount: number
  currency: string
  paymentMethod: 'Card' | 'PayPal' | 'BankTransfer' | 'MobileMoney'
  paymentProvider: 'Stripe' | 'PayPal' | 'Flutterwave'
  transactionId?: string
  status: PaymentStatus
  paidAt?: string
  refundedAt?: string
  refundAmount?: number
  createdAt: string
  updatedAt: string
}

export interface BookingReview {
  id: string
  bookingId: string
  rating: number
  comment?: string
  images?: string[]
  isVerified: boolean
  response?: {
    comment: string
    createdAt: string
  }
  createdAt: string
  updatedAt: string
}

export interface BookingMessage {
  id: string
  bookingId: string
  senderId: string
  sender?: User
  message: string
  attachments?: string[]
  isRead: boolean
  createdAt: string
}

export interface CreateBookingRequest {
  serviceId: string
  scheduledDate: string
  scheduledStartTime: string
  scheduledEndTime: string
  address: string
  city: string
  postalCode: string
  latitude?: number
  longitude?: number
  additionalInstructions?: string
  isUrgent?: boolean
  isRecurring?: boolean
  recurringPattern?: {
    frequency: 'Daily' | 'Weekly' | 'Monthly'
    interval: number
    endDate?: string
  }
  clientNotes?: string
  paymentMethod: 'Card' | 'PayPal' | 'BankTransfer' | 'MobileMoney'
}

export interface UpdateBookingRequest {
  scheduledDate?: string
  scheduledStartTime?: string
  scheduledEndTime?: string
  address?: string
  city?: string
  postalCode?: string
  latitude?: number
  longitude?: number
  additionalInstructions?: string
  clientNotes?: string
  providerNotes?: string
}

export interface BookingFilters {
  status?: BookingStatus
  paymentStatus?: PaymentStatus
  serviceId?: string
  providerId?: string
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
  isUrgent?: boolean
  isRecurring?: boolean
}

export interface BookingStats {
  totalBookings: number
  pendingBookings: number
  confirmedBookings: number
  completedBookings: number
  cancelledBookings: number
  totalRevenue: number
  averageBookingValue: number
  averageRating: number
  repeatCustomerRate: number
  monthlyGrowth: number
  topServices: {
    serviceId: string
    serviceName: string
    bookingCount: number
    revenue: number
  }[]
  recentBookings: Booking[]
}
