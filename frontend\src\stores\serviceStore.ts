import { create } from 'zustand';

export interface ServiceCategory {
  id: string;
  name: string;
  description: string;
  slug: string;
  icon?: string;
  parentCategoryId?: string;
  subCategories?: ServiceCategory[];
  isActive: boolean;
  isFeatured: boolean;
  displayOrder: number;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  categoryId: string;
  category?: ServiceCategory;
  providerId: string;
  provider?: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    rating: number;
    reviewCount: number;
  };
  basePrice: number;
  pricingUnit: 'Hourly' | 'Fixed' | 'PerUnit';
  minDurationMinutes: number;
  maxDurationMinutes: number;
  serviceRadiusKm: number;
  minAdvanceHours: number;
  maxAdvanceDays: number;
  isActive: boolean;
  averageRating: number;
  totalBookings: number;
  tags: string;
  images?: string[];
  address?: {
    addressLine1: string;
    city: string;
    postalCode: string;
    country: string;
    latitude?: number;
    longitude?: number;
  };
}

export interface SearchFilters {
  searchTerm?: string;
  categoryId?: string;
  location?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  availability?: {
    date: Date;
    duration: number;
  };
  sortBy?: 'name' | 'price' | 'rating' | 'distance';
  sortDirection?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  items: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ServiceState {
  // State
  services: Service[];
  categories: ServiceCategory[];
  featuredServices: Service[];
  popularServices: Service[];
  selectedService: Service | null;
  searchFilters: SearchFilters;
  searchResults: PaginatedResult<Service> | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  loadCategories: () => Promise<void>;
  loadFeaturedServices: () => Promise<void>;
  loadPopularServices: () => Promise<void>;
  searchServices: (filters: SearchFilters, page?: number, pageSize?: number) => Promise<void>;
  getServiceById: (id: string) => Promise<Service>;
  getServicesByProvider: (providerId: string) => Promise<Service[]>;
  checkAvailability: (serviceId: string, date: Date, duration: number) => Promise<boolean>;
  calculatePrice: (serviceId: string, duration: number, isUrgent?: boolean) => Promise<number>;
  setSelectedService: (service: Service | null) => void;
  updateSearchFilters: (filters: Partial<SearchFilters>) => void;
  clearSearchResults: () => void;
  clearError: () => void;
}

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5280/api';

export const useServiceStore = create<ServiceState>((set, get) => ({
  // Initial state
  services: [],
  categories: [],
  featuredServices: [],
  popularServices: [],
  selectedService: null,
  searchFilters: {},
  searchResults: null,
  isLoading: false,
  error: null,

  // Actions
  loadCategories: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await fetch(`${API_BASE_URL}/categories`);
      
      if (!response.ok) {
        throw new Error('Failed to load categories');
      }

      const categories: ServiceCategory[] = await response.json();
      
      set({
        categories,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load categories',
      });
    }
  },

  loadFeaturedServices: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await fetch(`${API_BASE_URL}/services/featured`);
      
      if (!response.ok) {
        throw new Error('Failed to load featured services');
      }

      const featuredServices: Service[] = await response.json();
      
      set({
        featuredServices,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load featured services',
      });
    }
  },

  loadPopularServices: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await fetch(`${API_BASE_URL}/services/popular`);
      
      if (!response.ok) {
        throw new Error('Failed to load popular services');
      }

      const popularServices: Service[] = await response.json();
      
      set({
        popularServices,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load popular services',
      });
    }
  },

  searchServices: async (filters: SearchFilters, page = 1, pageSize = 20) => {
    set({ isLoading: true, error: null, searchFilters: filters });

    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (filters.searchTerm) {
        queryParams.append('searchTerm', filters.searchTerm);
      }
      if (filters.categoryId) {
        queryParams.append('categoryId', filters.categoryId);
      }
      if (filters.priceRange) {
        queryParams.append('minPrice', filters.priceRange.min.toString());
        queryParams.append('maxPrice', filters.priceRange.max.toString());
      }
      if (filters.rating) {
        queryParams.append('minRating', filters.rating.toString());
      }
      if (filters.sortBy) {
        queryParams.append('sortBy', filters.sortBy);
        queryParams.append('sortDirection', filters.sortDirection || 'asc');
      }

      const response = await fetch(`${API_BASE_URL}/services/search?${queryParams}`);
      
      if (!response.ok) {
        throw new Error('Failed to search services');
      }

      const searchResults: PaginatedResult<Service> = await response.json();
      
      set({
        searchResults,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to search services',
      });
    }
  },

  getServiceById: async (id: string) => {
    set({ isLoading: true, error: null });

    try {
      const response = await fetch(`${API_BASE_URL}/services/${id}`);
      
      if (!response.ok) {
        throw new Error('Service not found');
      }

      const service: Service = await response.json();
      
      set({
        selectedService: service,
        isLoading: false,
        error: null,
      });

      return service;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load service',
      });
      throw error;
    }
  },

  getServicesByProvider: async (providerId: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/services/provider/${providerId}`);
      
      if (!response.ok) {
        throw new Error('Failed to load provider services');
      }

      const services: Service[] = await response.json();
      return services;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to load provider services',
      });
      throw error;
    }
  },

  checkAvailability: async (serviceId: string, date: Date, duration: number) => {
    try {
      const queryParams = new URLSearchParams({
        serviceId,
        scheduledDate: date.toISOString(),
        durationMinutes: duration.toString(),
      });

      const response = await fetch(`${API_BASE_URL}/booking/check-availability?${queryParams}`);
      
      if (!response.ok) {
        throw new Error('Failed to check availability');
      }

      const result = await response.json();
      return result.isAvailable;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to check availability',
      });
      throw error;
    }
  },

  calculatePrice: async (serviceId: string, duration: number, isUrgent = false) => {
    try {
      const queryParams = new URLSearchParams({
        serviceId,
        durationMinutes: duration.toString(),
        isUrgent: isUrgent.toString(),
      });

      const response = await fetch(`${API_BASE_URL}/booking/calculate-price?${queryParams}`);
      
      if (!response.ok) {
        throw new Error('Failed to calculate price');
      }

      const result = await response.json();
      return result.totalAmount;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to calculate price',
      });
      throw error;
    }
  },

  setSelectedService: (service: Service | null) => {
    set({ selectedService: service });
  },

  updateSearchFilters: (filters: Partial<SearchFilters>) => {
    const currentFilters = get().searchFilters;
    set({
      searchFilters: { ...currentFilters, ...filters },
    });
  },

  clearSearchResults: () => {
    set({
      searchResults: null,
      searchFilters: {},
    });
  },

  clearError: () => {
    set({ error: null });
  },
}));
