import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Search, Star, TrendingUp, Users, DollarSign, Euro, Shield, Zap, Award, ArrowRight, CheckCircle, Clock, MapPin } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ServiceCard } from '@/components/services/ServiceCard';
import { BookingCard } from '@/components/booking/BookingCard';
import { useServiceStore, useBookingStore } from '@/stores';
import { useAuthStore } from '@/stores/authStore';

export const Dashboard: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    featuredServices, 
    popularServices, 
    loadFeaturedServices, 
    loadPopularServices 
  } = useServiceStore();
  const { 
    clientBookings, 
    providerBookings, 
    bookingStats,
    getClientBookings, 
    getProviderBookings,
    getBookingStats 
  } = useBookingStore();

  useEffect(() => {
    // Load dashboard data
    loadFeaturedServices();
    loadPopularServices();
    
    if (user) {
      if (user.role === 'Client' || user.role === 'Admin') {
        getClientBookings({ status: ['Pending', 'Confirmed', 'InProgress'] });
      }
      if (user.role === 'ServiceProvider' || user.role === 'Admin') {
        getProviderBookings({ status: ['Pending', 'Confirmed', 'InProgress'] });
        getBookingStats();
      }
    }
  }, [user, loadFeaturedServices, loadPopularServices, getClientBookings, getProviderBookings, getBookingStats]);

  const recentBookings = user?.role === 'ServiceProvider' 
    ? providerBookings.slice(0, 3)
    : clientBookings.slice(0, 3);

  const renderWelcomeSection = () => (
    <div className="mb-8">
      <div className="bg-gradient-to-r from-primary/10 to-secondary/10 -mx-6 px-6 py-8 rounded-lg mb-6">
        <h1 className="text-3xl font-bold mb-2">
          Bienvenue, {user?.firstName} !
        </h1>
        <p className="text-muted-foreground mb-6">
          {user?.role === 'ServiceProvider'
            ? 'Gérez vos services et réservations avec notre plateforme à commission réduite'
            : user?.role === 'Admin'
            ? 'Surveillez l\'activité de la plateforme et gérez les utilisateurs'
            : 'Découvrez des services de qualité avec paiement sécurisé et prestataires vérifiés'
          }
        </p>

        {/* ServiceLink Advantages */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3 bg-background/50 rounded-lg p-3">
            <Euro className="h-5 w-5 text-green-500" />
            <div>
              <p className="font-medium text-sm">Commission réduite</p>
              <p className="text-xs text-muted-foreground">8% vs 15% concurrence</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 bg-background/50 rounded-lg p-3">
            <Shield className="h-5 w-5 text-blue-500" />
            <div>
              <p className="font-medium text-sm">Paiement sécurisé</p>
              <p className="text-xs text-muted-foreground">Protection intégrée</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 bg-background/50 rounded-lg p-3">
            <Clock className="h-5 w-5 text-purple-500" />
            <div>
              <p className="font-medium text-sm">Réponse rapide</p>
              <p className="text-xs text-muted-foreground">Moyenne &lt; 2h</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderStatsCards = () => {
    if (user?.role !== 'ServiceProvider' || !bookingStats) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.totalBookings}</div>
            <p className="text-xs text-muted-foreground">
              {bookingStats.completionRate.toFixed(1)}% completion rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR',
              }).format(bookingStats.totalRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">
              From {bookingStats.completedBookings} completed bookings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.averageRating.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              From {bookingStats.totalReviews} reviews
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Bookings</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.pendingBookings}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting your response
            </p>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderQuickActions = () => (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {user?.role === 'Client' && (
            <>
              <Link to="/services" className="block">
                <Button className="h-auto p-4 flex-col space-y-2 w-full">
                  <Search className="h-6 w-6" />
                  <span>Parcourir les services</span>
                </Button>
              </Link>
              <Link to="/bookings" className="block">
                <Button variant="outline" className="h-auto p-4 flex-col space-y-2 w-full">
                  <Calendar className="h-6 w-6" />
                  <span>Mes réservations</span>
                </Button>
              </Link>
              <Link to="/profile" className="block">
                <Button variant="outline" className="h-auto p-4 flex-col space-y-2 w-full">
                  <Users className="h-6 w-6" />
                  <span>Mon profil</span>
                </Button>
              </Link>
            </>
          )}

          {user?.role === 'ServiceProvider' && (
            <>
              <Link to="/provider/services" className="block">
                <Button className="h-auto p-4 flex-col space-y-2 w-full">
                  <Search className="h-6 w-6" />
                  <span>Mes services</span>
                </Button>
              </Link>
              <Link to="/provider/bookings" className="block">
                <Button variant="outline" className="h-auto p-4 flex-col space-y-2 w-full">
                  <Calendar className="h-6 w-6" />
                  <span>Réservations</span>
                </Button>
              </Link>
              <Link to="/provider/dashboard" className="block">
                <Button variant="outline" className="h-auto p-4 flex-col space-y-2 w-full">
                  <TrendingUp className="h-6 w-6" />
                  <span>Analyses</span>
                </Button>
              </Link>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const renderRecentBookings = () => {
    if (recentBookings.length === 0) return null;

    return (
      <Card className="mb-8">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Bookings</CardTitle>
            <Link to={user?.role === 'ServiceProvider' ? '/provider/bookings' : '/bookings'}>
              <Button variant="outline" size="sm">
                Voir tout
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentBookings.map((booking) => (
              <BookingCard
                key={booking.id}
                booking={booking}
                userRole={user?.role || 'Client'}
              />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderFeaturedServices = () => {
    if (user?.role === 'ServiceProvider' || featuredServices.length === 0) return null;

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Featured Services</CardTitle>
            <Link to="/services">
              <Button variant="outline" size="sm">
                Voir tout
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredServices.slice(0, 3).map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {renderWelcomeSection()}
      {renderStatsCards()}
      {renderQuickActions()}
      {renderRecentBookings()}
      {renderFeaturedServices()}
    </div>
  );
};
