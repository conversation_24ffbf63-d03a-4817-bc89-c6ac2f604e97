import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Calendar, Search, Star, TrendingUp, Users, DollarSign } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ServiceCard } from '@/components/services/ServiceCard';
import { BookingCard } from '@/components/booking/BookingCard';
import { useServiceStore, useBookingStore } from '@/stores';
import { useAuthStore } from '@/stores/authStore'; 

export const Dashboard: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    featuredServices, 
    popularServices, 
    loadFeaturedServices, 
    loadPopularServices 
  } = useServiceStore();
  const { 
    clientBookings, 
    providerBookings, 
    bookingStats,
    getClientBookings, 
    getProviderBookings,
    getBookingStats 
  } = useBookingStore();

  useEffect(() => {
    // Load dashboard data
    loadFeaturedServices();
    loadPopularServices();
    
    if (user) {
      if (user.role === 'Client' || user.role === 'Admin') {
        getClientBookings({ status: ['Pending', 'Confirmed', 'InProgress'] });
      }
      if (user.role === 'ServiceProvider' || user.role === 'Admin') {
        getProviderBookings({ status: ['Pending', 'Confirmed', 'InProgress'] });
        getBookingStats();
      }
    }
  }, [user, loadFeaturedServices, loadPopularServices, getClientBookings, getProviderBookings, getBookingStats]);

  const recentBookings = user?.role === 'ServiceProvider' 
    ? providerBookings.slice(0, 3)
    : clientBookings.slice(0, 3);

  const renderWelcomeSection = () => (
    <div className="mb-8">
      <h1 className="text-3xl font-bold text-foreground mb-2">
        Welcome back, {user?.firstName}!
      </h1>
      <p className="text-muted-foreground">
        {user?.role === 'ServiceProvider' 
          ? 'Manage your services and bookings'
          : user?.role === 'Admin'
          ? 'Monitor platform activity and manage users'
          : 'Discover and book amazing services'
        }
      </p>
    </div>
  );

  const renderStatsCards = () => {
    if (user?.role !== 'ServiceProvider' || !bookingStats) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.totalBookings}</div>
            <p className="text-xs text-muted-foreground">
              {bookingStats.completionRate.toFixed(1)}% completion rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR',
              }).format(bookingStats.totalRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">
              From {bookingStats.completedBookings} completed bookings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.averageRating.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              From {bookingStats.totalReviews} reviews
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Bookings</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.pendingBookings}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting your response
            </p>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderQuickActions = () => (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {user?.role === 'Client' && (
            <>
              <Button asChild className="h-auto p-4 flex-col space-y-2">
                <Link to="/services">
                  <Search className="h-6 w-6" />
                  <span>Browse Services</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                <Link to="/bookings">
                  <Calendar className="h-6 w-6" />
                  <span>My Bookings</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                <Link to="/profile">
                  <Users className="h-6 w-6" />
                  <span>My Profile</span>
                </Link>
              </Button>
            </>
          )}
          
          {user?.role === 'ServiceProvider' && (
            <>
              <Button asChild className="h-auto p-4 flex-col space-y-2">
                <Link to="/provider/services">
                  <Search className="h-6 w-6" />
                  <span>My Services</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                <Link to="/provider/bookings">
                  <Calendar className="h-6 w-6" />
                  <span>Bookings</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                <Link to="/provider/dashboard">
                  <TrendingUp className="h-6 w-6" />
                  <span>Analytics</span>
                </Link>
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const renderRecentBookings = () => {
    if (recentBookings.length === 0) return null;

    return (
      <Card className="mb-8">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Bookings</CardTitle>
            <Button asChild variant="outline" size="sm">
              <Link to={user?.role === 'ServiceProvider' ? '/provider/bookings' : '/bookings'}>
                View All
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentBookings.map((booking) => (
              <BookingCard
                key={booking.id}
                booking={booking}
                userRole={user?.role || 'Client'}
              />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderFeaturedServices = () => {
    if (user?.role === 'ServiceProvider' || featuredServices.length === 0) return null;

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Featured Services</CardTitle>
            <Button asChild variant="outline" size="sm">
              <Link to="/services">
                View All
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredServices.slice(0, 3).map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {renderWelcomeSection()}
      {renderStatsCards()}
      {renderQuickActions()}
      {renderRecentBookings()}
      {renderFeaturedServices()}
    </div>
  );
};
