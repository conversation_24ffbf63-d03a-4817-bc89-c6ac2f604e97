using MediatR;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Interfaces;

namespace ServiceLink.Application.Handlers;

public class ForgotPasswordCommandHandler : IRequestHandler<ForgotPasswordCommand, bool>
{
    private readonly IUserRepository _userRepository;
    private readonly IEmailService _emailService;
    private readonly IPasswordResetTokenRepository _passwordResetTokenRepository;

    public ForgotPasswordCommandHandler(IUserRepository userRepository, IEmailService emailService, IPasswordResetTokenRepository passwordResetTokenRepository)
    {
        _userRepository = userRepository;
        _emailService = emailService;
        _passwordResetTokenRepository = passwordResetTokenRepository;
    }

    public async Task<bool> Handle(ForgotPasswordCommand request, CancellationToken cancellationToken)
    {
        // 1. Vérifier que l'utilisateur existe et est actif
        var user = await _userRepository.GetByEmailAsync(request.Email);
        if (user == null || !user.IsActive)
            return false;

        // 2. <PERSON><PERSON><PERSON>rer un token de reset
        var resetToken = _passwordResetTokenRepository.GenerateToken();
        await _passwordResetTokenRepository.SaveAsync(user.Id, resetToken);

        // 3. Envoyer l'email avec le lien/token
        await _emailService.SendPasswordResetEmailAsync(user.Email.Value, user.FirstName, resetToken);

        return true;
    }
}
