using FluentAssertions;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Domain.Tests.ValueObjects;

/// <summary>
/// Tests unitaires pour le Value Object Email
/// </summary>
public class EmailTests
{
    [Fact]
    public void Create_WithValidEmail_ShouldReturnEmailInstance()
    {
        // Arrange
        var validEmail = "<EMAIL>";

        // Act
        var email = Email.Create(validEmail);

        // Assert
        email.Should().NotBeNull();
        email.Value.Should().Be(validEmail.ToLowerInvariant());
        email.Domain.Should().Be("example.com");
        email.LocalPart.Should().Be("test");
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Create_WithNullOrEmptyEmail_ShouldThrowArgumentException(string invalidEmail)
    {
        // Act & Assert
        var act = () => Email.Create(invalidEmail);
        act.Should().Throw<ArgumentException>()
           .WithMessage("L'adresse email ne peut pas être vide.*");
    }

    [Theory]
    [InlineData("invalid-email")]
    [InlineData("@example.com")]
    [InlineData("test@")]
    [InlineData("<EMAIL>")]
    [InlineData(".<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("test@example")]
    public void Create_WithInvalidEmailFormat_ShouldThrowArgumentException(string invalidEmail)
    {
        // Act & Assert
        var act = () => Email.Create(invalidEmail);
        act.Should().Throw<ArgumentException>()
           .WithMessage("*n'est pas valide*");
    }

    [Fact]
    public void Create_WithTooLongEmail_ShouldThrowArgumentException()
    {
        // Arrange
        var longEmail = new string('a', 250) + "@example.com"; // > 254 caractères

        // Act & Assert
        var act = () => Email.Create(longEmail);
        act.Should().Throw<ArgumentException>()
           .WithMessage("*ne peut pas dépasser 254 caractères*");
    }

    [Fact]
    public void Create_WithTooLongLocalPart_ShouldThrowArgumentException()
    {
        // Arrange
        var longLocalPart = new string('a', 65) + "@example.com"; // > 64 caractères pour la partie locale

        // Act & Assert
        var act = () => Email.Create(longLocalPart);
        act.Should().Throw<ArgumentException>()
           .WithMessage("*partie locale*ne peut pas dépasser 64 caractères*");
    }

    [Theory]
    [InlineData("<EMAIL>", "<EMAIL>")]
    [InlineData("  <EMAIL>  ", "<EMAIL>")]
    [InlineData("<EMAIL>", "<EMAIL>")]
    public void Create_ShouldNormalizeEmailToLowerCase(string input, string expected)
    {
        // Act
        var email = Email.Create(input);

        // Assert
        email.Value.Should().Be(expected);
    }

    [Theory]
    [InlineData("<EMAIL>", "gmail.com", true)]
    [InlineData("<EMAIL>", "gmail.com", false)]
    [InlineData("<EMAIL>", "gmail.com", true)]
    public void BelongsToDomain_ShouldReturnCorrectResult(string emailAddress, string domain, bool expected)
    {
        // Arrange
        var email = Email.Create(emailAddress);

        // Act
        var result = email.BelongsToDomain(domain);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", false)]
    [InlineData("<EMAIL>", false)]
    public void IsDisposable_ShouldDetectDisposableEmails(string emailAddress, bool expected)
    {
        // Arrange
        var email = Email.Create(emailAddress);

        // Act
        var result = email.IsDisposable();

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", false)]
    [InlineData("<EMAIL>", false)]
    public void IsFromPopularProvider_ShouldDetectPopularProviders(string emailAddress, bool expected)
    {
        // Arrange
        var email = Email.Create(emailAddress);

        // Act
        var result = email.IsFromPopularProvider();

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("<EMAIL>", "j***@example.com")]
    [InlineData("<EMAIL>", "a***@example.com")]
    [InlineData("<EMAIL>", "ab***@example.com")]
    [InlineData("<EMAIL>", "j*******<EMAIL>")]
    public void ToMaskedString_ShouldMaskEmailCorrectly(string emailAddress, string expected)
    {
        // Arrange
        var email = Email.Create(emailAddress);

        // Act
        var masked = email.ToMaskedString();

        // Assert
        masked.Should().Be(expected);
    }

    [Fact]
    public void TryCreate_WithValidEmail_ShouldReturnTrueAndEmail()
    {
        // Arrange
        var validEmail = "<EMAIL>";

        // Act
        var success = Email.TryCreate(validEmail, out var email);

        // Assert
        success.Should().BeTrue();
        email.Should().NotBeNull();
        email!.Value.Should().Be(validEmail);
    }

    [Fact]
    public void TryCreate_WithInvalidEmail_ShouldReturnFalseAndNull()
    {
        // Arrange
        var invalidEmail = "invalid-email";

        // Act
        var success = Email.TryCreate(invalidEmail, out var email);

        // Assert
        success.Should().BeFalse();
        email.Should().BeNull();
    }

    [Fact]
    public void Equals_WithSameEmail_ShouldReturnTrue()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        email1.Should().Be(email2);
        email1.Equals(email2).Should().BeTrue();
        (email1 == email2).Should().BeTrue();
        (email1 != email2).Should().BeFalse();
    }

    [Fact]
    public void Equals_WithDifferentEmail_ShouldReturnFalse()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        email1.Should().NotBe(email2);
        email1.Equals(email2).Should().BeFalse();
        (email1 == email2).Should().BeFalse();
        (email1 != email2).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_WithSameEmail_ShouldReturnSameHashCode()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        email1.GetHashCode().Should().Be(email2.GetHashCode());
    }

    [Fact]
    public void ToString_ShouldReturnEmailValue()
    {
        // Arrange
        var emailAddress = "<EMAIL>";
        var email = Email.Create(emailAddress);

        // Act
        var result = email.ToString();

        // Assert
        result.Should().Be(emailAddress);
    }

    [Fact]
    public void ImplicitConversion_ToString_ShouldWork()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");

        // Act
        string emailString = email;

        // Assert
        emailString.Should().Be("<EMAIL>");
    }

    [Fact]
    public void ExplicitConversion_FromString_ShouldWork()
    {
        // Arrange
        var emailString = "<EMAIL>";

        // Act
        var email = (Email)emailString;

        // Assert
        email.Value.Should().Be(emailString);
    }

    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    public void Create_WithVariousValidFormats_ShouldSucceed(string validEmail)
    {
        // Act
        var act = () => Email.Create(validEmail);

        // Assert
        act.Should().NotThrow();
    }

    [Fact]
    public void Equals_WithNull_ShouldReturnFalse()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");

        // Act & Assert
        email.Equals(null).Should().BeFalse();
        (email == null).Should().BeFalse();
        (email != null).Should().BeTrue();
    }

    [Fact]
    public void Equals_WithDifferentType_ShouldReturnFalse()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");
        var otherObject = "<EMAIL>";

        // Act & Assert
        email.Equals(otherObject).Should().BeFalse();
    }
}
