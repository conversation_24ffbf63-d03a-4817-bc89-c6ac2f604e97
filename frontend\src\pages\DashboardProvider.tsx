import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Calendar, TrendingUp, Star, DollarSign, Users, Plus, AlertCircle, CheckCircle } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BookingCard } from '@/components/booking/BookingCard';
import { useServiceStore, useBookingStore } from '@/stores';
import { useAuthStore } from '@/stores/authStore';

export const DashboardProvider: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    providerBookings, 
    bookingStats,
    getProviderBookings,
    getBookingStats 
  } = useBookingStore();

  useEffect(() => {
    if (user) {
      getProviderBookings({ status: ['Pending', 'Confirmed', 'InProgress'] });
      getBookingStats();
    }
  }, [user, getProviderBookings, getBookingStats]);

  const recentBookings = providerBookings.slice(0, 3);
  const pendingBookings = providerBookings.filter(b => b.status === 'Pending');

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 -mx-6 px-6 py-8 rounded-lg">
        <h1 className="text-3xl font-bold mb-2 text-green-800">
          Bienvenue, {user?.firstName} !
        </h1>
        <p className="text-green-700 mb-6">
          Gérez vos services et réservations avec notre plateforme à commission réduite (8% seulement)
        </p>
        
        {/* Provider Advantages */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-800">8%</div>
            <p className="text-sm text-green-600">Commission seulement</p>
            <p className="text-xs text-green-500">vs 15% concurrence</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-800">Vérifié</div>
            <p className="text-sm text-blue-600">Profil certifié</p>
          </div>
          
          <div className="bg-white/70 rounded-lg p-4 text-center">
            <Star className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-yellow-800">
              {bookingStats?.averageRating?.toFixed(1) || '0.0'}
            </div>
            <p className="text-sm text-yellow-600">Note moyenne</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {bookingStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-green-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-700">Total Réservations</CardTitle>
              <Calendar className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-800">{bookingStats.totalBookings}</div>
              <p className="text-xs text-green-600">
                {bookingStats.completionRate.toFixed(1)}% taux de réussite
              </p>
            </CardContent>
          </Card>

          <Card className="border-blue-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-700">Revenus Totaux</CardTitle>
              <DollarSign className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-800">
                {new Intl.NumberFormat('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                }).format(bookingStats.totalRevenue)}
              </div>
              <p className="text-xs text-blue-600">
                Sur {bookingStats.completedBookings} services terminés
              </p>
            </CardContent>
          </Card>

          <Card className="border-yellow-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-yellow-700">Note Moyenne</CardTitle>
              <Star className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-800">{bookingStats.averageRating.toFixed(1)}</div>
              <p className="text-xs text-yellow-600">
                Sur {bookingStats.totalReviews} avis
              </p>
            </CardContent>
          </Card>

          <Card className="border-orange-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-orange-700">En Attente</CardTitle>
              <AlertCircle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-800">{bookingStats.pendingBookings}</div>
              <p className="text-xs text-orange-600">
                Demandes à traiter
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Actions rapides
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/provider/services" className="block">
              <Button className="h-auto p-6 flex-col space-y-3 w-full bg-green-600 hover:bg-green-700">
                <Plus className="h-8 w-8" />
                <span className="text-lg">Mes services</span>
                <span className="text-sm opacity-90">Gérer mes offres</span>
              </Button>
            </Link>
            
            <Link to="/provider/bookings" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-orange-200 hover:bg-orange-50">
                <Calendar className="h-8 w-8 text-orange-600" />
                <span className="text-lg">Réservations</span>
                <span className="text-sm text-muted-foreground">
                  {pendingBookings.length} en attente
                </span>
              </Button>
            </Link>
            
            <Link to="/provider/analytics" className="block">
              <Button variant="outline" className="h-auto p-6 flex-col space-y-3 w-full border-blue-200 hover:bg-blue-50">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <span className="text-lg">Analyses</span>
                <span className="text-sm text-muted-foreground">Performances et revenus</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Recent Bookings */}
      {recentBookings.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Dernières réservations
              </CardTitle>
              <Link to="/provider/bookings">
                <Button variant="outline" size="sm">
                  Voir tout
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <BookingCard
                  key={booking.id}
                  booking={booking}
                  userRole="ServiceProvider"
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Tips */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="text-green-800">🚀 Conseils pour optimiser vos performances</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-1" />
              <div>
                <p className="font-medium text-green-800">Répondez rapidement</p>
                <p className="text-sm text-green-600">Les clients préfèrent une réponse sous 2h</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Star className="h-5 w-5 text-yellow-600 mt-1" />
              <div>
                <p className="font-medium text-yellow-800">Soignez votre profil</p>
                <p className="text-sm text-yellow-600">Photos et descriptions détaillées</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
